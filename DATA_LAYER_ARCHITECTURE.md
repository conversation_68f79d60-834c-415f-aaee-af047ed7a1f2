# 数据层架构文档

## 模块概述
数据层是应用的数据访问和管理中心，负责网络请求、本地存储、数据缓存、Repository模式实现等核心数据操作功能。

## 模块位置
```
data/                               # 数据层根目录
├── network/                        # 网络模块
│   ├── api/                       # API接口定义
│   ├── client/                    # 网络客户端
│   ├── interceptor/               # 网络拦截器
│   ├── model/                     # 网络数据模型
│   └── storage/                   # 网络缓存存储
├── db/                            # 数据库模块
│   ├── dao/                       # 数据访问对象
│   └── entity/                    # 数据库实体
├── repository/                    # 仓库层
└── manager/                       # 数据管理器
```

## 核心架构

### 1. 网络层 (Network Layer)
- **API接口**: 统一的REST API接口定义
- **HTTP客户端**: 基于Retrofit + OkHttp的网络客户端
- **请求拦截**: 全局请求拦截器和响应处理
- **错误处理**: 统一的网络错误处理机制

### 2. 数据库层 (Database Layer)
- **Room数据库**: SQLite的上层抽象
- **DAO接口**: 数据访问对象接口
- **实体模型**: 数据库表结构定义
- **数据迁移**: 数据库版本迁移策略

### 3. 仓库层 (Repository Layer)
- **数据仓库**: 统一的数据访问接口
- **数据源协调**: 网络和本地数据源的协调
- **缓存策略**: 智能的数据缓存机制
- **离线支持**: 离线模式数据访问

## 网络模块详解

### API接口设计
```kotlin
// 用户相关API接口
interface UserInfoNetInterface {
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): ApiResult<LoginResponse>
    
    @GET("user/profile")
    suspend fun getUserProfile(): ApiResult<UserInfo>
    
    @PUT("user/profile") 
    suspend fun updateProfile(@Body userInfo: UserInfo): ApiResult<Unit>
    
    @POST("user/avatar")
    suspend fun updateAvatar(@Body request: UpdateAvatarRequest): ApiResult<UpdateAvatarResult>
}
```

### 网络客户端配置
```kotlin
// EnhancedNetworkClient配置
class EnhancedNetworkClient {
    // HTTP客户端配置
    - 连接超时: 15秒
    - 读取超时: 30秒  
    - 写入超时: 30秒
    - 重试机制: 自动重试3次
    - 连接池: 最大10个并发连接
}
```

### 拦截器功能
- **HeaderInterceptor**: 自动添加通用请求头
- **ResponseInterceptor**: 统一响应处理和日志
- **AuthInterceptor**: 自动Token刷新和重试
- **CacheInterceptor**: 智能缓存控制

## 数据库模块详解

### Room数据库配置
```kotlin
@Database(
    entities = [UserEntity::class, CacheEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun cacheDao(): CacheDao
}
```

### 用户数据DAO
```kotlin
@Dao
interface UserDao {
    @Query("SELECT * FROM users WHERE userId = :userId")
    suspend fun getUserById(userId: String): UserEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUser(user: UserEntity)
    
    @Update
    suspend fun updateUser(user: UserEntity)
    
    @Delete
    suspend fun deleteUser(user: UserEntity)
}
```

## 仓库层实现

### UserInfoRepository
```kotlin
class UserInfoRepository @Inject constructor(
    private val networkApi: UserInfoNetInterface,
    private val userDao: UserDao,
    private val cacheManager: CacheManager
) {
    // 获取用户信息 (缓存优先策略)
    suspend fun getUserInfo(userId: String): Resource<UserInfo> {
        return try {
            // 1. 检查缓存
            val cachedUser = cacheManager.getUser(userId)
            if (cachedUser != null && !isExpired(cachedUser)) {
                return Resource.Success(cachedUser)
            }
            
            // 2. 网络请求
            val response = networkApi.getUserProfile()
            if (response.isSuccess) {
                // 3. 更新缓存
                cacheManager.cacheUser(response.data)
                userDao.insertUser(response.data.toEntity())
                Resource.Success(response.data)
            } else {
                // 4. 返回本地数据作为降级
                val localUser = userDao.getUserById(userId)
                if (localUser != null) {
                    Resource.Success(localUser.toUserInfo())
                } else {
                    Resource.Error(response.message)
                }
            }
        } catch (e: Exception) {
            Resource.Error(e.message ?: "Unknown error")
        }
    }
}
```

## 缓存策略

### 多级缓存架构
```kotlin
// 缓存层次结构
1. 内存缓存 (MemoryDataStorage)
   - LRU缓存算法
   - 最大缓存100MB
   - 缓存时间30分钟

2. 磁盘缓存 (RoomDataStorage)  
   - SQLite数据库存储
   - 压缩存储优化
   - 缓存时间7天

3. 网络缓存 (OkHttp Cache)
   - HTTP缓存机制
   - 缓存大小50MB
   - 遵循HTTP缓存策略
```

### 缓存更新策略
- **写入时更新**: 数据写入时同步更新所有缓存层
- **定时刷新**: 定期从服务器刷新热点数据
- **用户触发**: 用户下拉刷新触发缓存更新
- **智能预加载**: 根据用户行为预加载相关数据

## 数据管理器

### UserManager
```kotlin
object UserManager {
    // 用户状态管理
    - 当前登录用户信息
    - 登录状态监听
    - Token管理和刷新
    - 用户数据同步
    
    // 核心方法
    fun getCurrentUser(): StateFlow<UserInfo?>
    fun isLoggedIn(): Boolean
    fun login(userInfo: UserInfo, token: String)
    fun logout()
    suspend fun syncUserData()
}
```

### 数据同步机制
- **增量同步**: 只同步变更的数据
- **冲突解决**: 服务器优先的冲突解决策略
- **离线队列**: 离线时的数据操作队列
- **后台同步**: 应用后台时的数据同步

## 错误处理

### 统一错误处理
```kotlin
sealed class Resource<T> {
    data class Success<T>(val data: T) : Resource<T>()
    data class Error<T>(val message: String) : Resource<T>()
    data class Loading<T>(val data: T? = null) : Resource<T>()
}

// 网络异常分类
sealed class NetworkException : Exception() {
    object NetworkUnavailable : NetworkException()
    object Timeout : NetworkException()
    object ServerError : NetworkException()
    object Unauthorized : NetworkException()
    data class ApiError(val code: Int, val msg: String) : NetworkException()
}
```

### 错误恢复机制
- **自动重试**: 网络异常自动重试
- **降级策略**: 返回缓存数据作为降级
- **用户提示**: 友好的错误提示信息
- **错误上报**: 关键错误自动上报

## 性能优化

### 数据库优化
- **索引策略**: 关键字段建立索引
- **批量操作**: 大量数据使用批量插入
- **连接池**: 数据库连接池管理
- **预编译语句**: SQL语句预编译优化

### 网络优化
- **请求合并**: 相似请求合并处理
- **压缩传输**: GZIP压缩减少流量
- **连接复用**: HTTP/2连接复用
- **DNS缓存**: DNS解析结果缓存

## 配置选项

### 网络配置
```kotlin
// 网络超时配置
connectTimeout = 15.seconds
readTimeout = 30.seconds
writeTimeout = 30.seconds

// 缓存配置
cacheSize = 50.MB
maxCacheAge = 7.days
maxStaleAge = 30.days
```

### 数据库配置
```kotlin
// Room配置
allowMainThreadQueries = false
enableWAL = true
journalMode = JournalMode.WAL
synchronousMode = SynchronousMode.NORMAL
```