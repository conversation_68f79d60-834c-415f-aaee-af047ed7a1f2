# Puxxi 应用个人资料模块文档

## 目录
1. [概述](#概述)
2. [UI 组件](#ui-组件)
3. [头像上传流程](#头像上传流程)
4. [表单验证与提交](#表单验证与提交)
5. [状态管理](#状态管理)
6. [数据流](#数据流)
7. [安全机制](#安全机制)
8. [性能优化](#性能优化)
9. [代码结构](#代码结构)

## 概述

Puxxi 应用的个人资料模块采用 MVVM 架构设计，使用 Jetpack Compose 构建 UI，Kotlin 协程处理异步操作。该模块提供完整的个人资料管理功能，包括头像上传、基本信息编辑、生日选择和性别设置等。特别是头像上传功能，实现了从图片选择、压缩处理到云端存储的完整流程。

## UI 组件

### 1. ProfileFormScreen

个人资料表单页面是用户完善个人信息的主要界面，包含以下核心组件：

- **头像上传区域**：圆形头像显示区域，底部有相机图标用于触发图片选择
- **昵称输入框**：用户名称输入区域，使用自定义 TextField
- **生日选择器**：点击打开日期选择对话框，支持年月日选择
- **性别选择器**：男/女选项卡，支持单选
- **邀请码输入框**：可选的邀请码输入区域
- **提交按钮**：金色渐变按钮，用于提交表单

```kotlin
@Composable
fun ProfileFormScreen(
    navController: NavController,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    // 状态收集
    val state by viewModel.uiState.collectAsState()
    
    // 本地状态管理
    var name by remember { mutableStateOf(state.userInfo?.nickname ?: "") }
    var ageText by remember { mutableStateOf(state.userInfo?.age?.toString() ?: "") }
    var genderValue by remember { mutableStateOf(state.userInfo?.gender?.toString() ?: "0") }
    var inviteCode by remember { mutableStateOf(state.invitationCode) }
    var avatarUri by remember { mutableStateOf<Uri?>(null) }
    var avatarRefreshKey by remember { mutableStateOf(0) }
    
    // 监听状态变化
    LaunchedEffect(state.userInfo) { /* 更新表单数据 */ }
    LaunchedEffect(state.avatarUploadState) { /* 处理头像上传状态 */ }
    LaunchedEffect(state.submitSuccess) { /* 处理提交成功 */ }
    LaunchedEffect(state.error) { /* 处理错误状态 */ }
    
    // UI 组件
    Box(modifier = Modifier.fillMaxSize()) {
        // 背景图片
        // 头像区域
        // 表单字段
        // 提交按钮
        // 对话框管理组件
        // 全局加载视图
    }
}
```

### 2. 自定义组件

#### 头像组件

```kotlin
Box(
    modifier = Modifier.fillMaxWidth(),
    contentAlignment = Alignment.Center
) {
    // 用户头像
    val avatarUrl = if (state.userInfo?.avatarUrl?.isNotEmpty() == true) {
        state.userInfo?.avatarUrl
    } else {
        avatarUri?.toString()
    } ?: ""
    
    Box(
        modifier = Modifier.wrapContentSize(),
        contentAlignment = Alignment.Center
    ) {
        // 使用刷新键强制重新加载头像
        key(avatarUrl, avatarRefreshKey) {
            ImageLoader.LoadImage(
                imageUrl = avatarUrl,
                contentDescription = stringResource(R.string.profile_avatar_description),
                modifier = Modifier
                    .size(168.dp)
                    .clip(CircleShape)
            )
        }
        
        // 相机图标
        Box(
            modifier = Modifier
                .size(60.dp)
                .offset(y = 25.dp)
                .noRippleClickable {
                    DialogManager.showImagePicker(
                        onImageSelected = { uri ->
                            avatarUri = uri
                            viewModel.updateAvatar(uri)
                        }
                    )
                }
                .align(Alignment.BottomCenter)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.profile_pic_icon),
                contentDescription = stringResource(R.string.profile_change_avatar),
                modifier = Modifier.size(60.dp),
                tint = Color.Unspecified
            )
        }
    }
}
```

#### 自定义输入框

```kotlin
@Composable
fun CustomTextField(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    modifier: Modifier = Modifier,
    readOnly: Boolean = false,
    trailingIcon: @Composable (() -> Unit)? = null
) {
    val focusedBorderColor = Color(0xFF766854)
    val unfocusedBorderColor = Color(0xFF3F204C)
    val backgroundColor = Color.White.copy(alpha = 0.08f)
    
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    
    Box(
        modifier = modifier
            .background(backgroundColor, RoundedCornerShape(10.dp))
            .border(
                width = 1.dp,
                color = if (isFocused) focusedBorderColor else unfocusedBorderColor,
                shape = RoundedCornerShape(10.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.CenterStart
            ) {
                BasicTextField(/* ... */)
                
                // Placeholder
                if (value.isEmpty()) {
                    Text(
                        text = placeholder,
                        style = OutlinedTextFieldHintText,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            // Trailing icon
            trailingIcon?.invoke()
        }
    }
}
```

#### 性别选择器

```kotlin
@Composable
fun GenderOption(
    label: String, 
    selected: Boolean, 
    iconResId: Int,
    modifier: Modifier = Modifier,
    textColor: Color
) {
    val borderWidth = if (selected) 2.dp else 0.dp
    val borderColor = if (selected) Color(0xFF766854) else Color.Transparent
    val backgroundBrush = if (selected) {
        Brush.horizontalGradient(
            colors = listOf(Color(0xFF390B4C), Color(0xFF551071))
        )
    } else {
        Brush.horizontalGradient(
            colors = listOf(Color(0xFF3F204C), Color(0xFF3F204C))
        )
    }

    Box(
        modifier = modifier
            .height(108.dp)
            .border(borderWidth, borderColor, RoundedCornerShape(10.dp))
            .background(backgroundBrush, RoundedCornerShape(10.dp)),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                painter = painterResource(id = iconResId),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.width(20.dp))
            Text(text = label, color = textColor, fontWeight = FontWeight.SemiBold, fontSize = 32.sp, lineHeight = 45.sp)
        }
    }
}
```

## 头像上传流程

### 1. 完整流程

头像上传功能实现了从图片选择到云端存储的完整流程：

```
用户点击头像区域
↓
显示图片选择器 (DialogManager.showImagePicker)
↓
用户选择图片来源 (相机/相册)
↓
获取图片 URI
↓
调用 updateAvatar(uri) 方法
↓
获取 OSS 上传策略 (getUserOssPolicyPostV2)
↓
上传文件到 OSS (uploadFile)
↓
更新用户头像信息 (userUpdateAvatar)
↓
更新本地用户数据 (UserManager.updateUserInfo)
↓
刷新 UI 显示新头像
```

### 2. 核心代码分析

#### 头像上传方法

```kotlin
fun updateAvatar(avatarUri: Uri) {
    updateState {
        copy(
            isLoading = true,
            error = null,
            avatarUploadState = AvatarUploadState.GettingOssPolicy
        )
    }
    
    // 显示全局加载动画
    GlobalLoadingManager.show()

    PuxxiCoroutine.io {
        try {
            // 步骤1: 获取OSS上传策略
            var ossPolicy: OssPolicyResult? = null
            userInfoRepository.getUserOssPolicyPostV2().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        ossPolicy = resource.data
                        return@collect
                    }
                    is Resource.Error -> {
                        updateState {
                            copy(
                                error = "获取上传权限失败: ${resource.message}",
                                isLoading = false,
                                avatarUploadState = AvatarUploadState.Error(resource.message)
                            )
                        }
                        GlobalLoadingManager.hide()
                        return@collect
                    }
                    is Resource.Loading -> { /* 继续等待 */ }
                }
            }

            // 步骤2: 上传文件到OSS
            updateState { copy(avatarUploadState = AvatarUploadState.Uploading(0)) }
            var uploadResult: FileUploadDownloadUtil.UploadState.Success? = null
            var isUploadCompleted = false

            fileUploadDownloadUtil.uploadFile(
                uri = avatarUri,
                ossPolicyResult = ossPolicy,
                headers = mapOf("Content-Type" to "image/jpeg"),
                fieldName = "file"
            ).collect { uploadState ->
                when (uploadState) {
                    is FileUploadDownloadUtil.UploadState.Success -> {
                        uploadResult = uploadState
                        isUploadCompleted = true
                    }
                    is FileUploadDownloadUtil.UploadState.Progress -> {
                        updateState {
                            copy(avatarUploadState = AvatarUploadState.Uploading(uploadState.progress))
                        }
                    }
                    is FileUploadDownloadUtil.UploadState.Error -> {
                        isUploadCompleted = true
                        updateState {
                            copy(
                                error = "头像上传失败: ${uploadState.message}",
                                isLoading = false,
                                avatarUploadState = AvatarUploadState.Error(uploadState.message)
                            )
                        }
                        GlobalLoadingManager.hide()
                    }
                }
            }

            // 步骤3: 调用更新头像接口
            val xFileUploadResult = uploadResult?.result
            if (xFileUploadResult != null) {
                updateState { copy(avatarUploadState = AvatarUploadState.UpdatingAvatar) }
                val updateData = mapOf("avatarPath" to xFileUploadResult.filename)

                userInfoRepository.userUpdateAvatar(data = updateData)
                    .collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                val updateAvatarResult = resource.data
                                // 步骤4: 更新本地用户信息
                                UserManager.updateUserInfo { currentUser ->
                                    currentUser.copy(
                                        avatar = updateAvatarResult.mediaUrl,
                                        avatarUrl = updateAvatarResult.mediaUrl
                                    )
                                }
                                updateState {
                                    copy(
                                        isLoading = false,
                                        error = null,
                                        avatarUploadState = AvatarUploadState.Success
                                    )
                                }
                                GlobalLoadingManager.hide()
                            }
                            is Resource.Error -> { /* 处理错误 */ }
                            is Resource.Loading -> { /* 处理加载状态 */ }
                        }
                    }
            }
        } catch (e: Exception) {
            // 异常处理
        }
    }
}
```

#### 头像上传状态

```kotlin
sealed class AvatarUploadState {
    object Idle : AvatarUploadState()
    object GettingOssPolicy : AvatarUploadState()
    data class Uploading(val progress: Int) : AvatarUploadState()
    object UpdatingAvatar : AvatarUploadState()
    object Success : AvatarUploadState()
    data class Error(val message: String) : AvatarUploadState()
}
```

#### 头像上传状态监听

```kotlin
LaunchedEffect(state.avatarUploadState) {
    when (state.avatarUploadState) {
        is AvatarUploadState.Success -> {
            ToastUtil.showSuccess(context.getString(R.string.profile_avatar_upload_success))
            avatarRefreshKey++
            viewModel.resetAvatarUploadState()
        }
        is AvatarUploadState.Error -> {
            val errorMessage = (state.avatarUploadState as AvatarUploadState.Error).message
            ToastUtil.showError(context.getString(R.string.profile_avatar_upload_failed, errorMessage))
            viewModel.resetAvatarUploadState()
        }
        else -> { /* 其他状态不处理 */ }
    }
}
```

## 表单验证与提交

### 1. 表单验证

表单提交前进行多项验证，确保数据有效：

- **必填字段验证**：确保姓名和生日不为空
- **年龄验证**：确保用户年龄大于18岁
- **日期格式验证**：确保生日格式正确 (YYYY-MM-DD)

```kotlin
fun submitProfile(name: String, age: String, gender: String, invitationCode: String) {
    // 验证名字和年龄不能为空
    if (name.isBlank() || age.isBlank()) {
        updateState {
            this.copy(error = "姓名和生日不能为空")
        }
        return
    }
    
    // 验证年龄是否大于18岁
    try {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val birthDate = dateFormat.parse(age)
        if (birthDate != null) {
            val ageYears = calculateAge(birthDate)
            if (ageYears < 18) {
                updateState {
                    this.copy(error = "年龄必须大于18岁")
                }
                return
            }
        }
    } catch (e: Exception) {
        updateState {
            this.copy(error = "生日格式不正确")
        }
        return
    }
    
    // 验证通过，继续提交
    // ...
}
```

### 2. 年龄计算

```kotlin
private fun calculateAge(birthDate: Date): Int {
    val today = Calendar.getInstance()
    val birthDay = Calendar.getInstance()
    birthDay.time = birthDate
    
    var age = today.get(Calendar.YEAR) - birthDay.get(Calendar.YEAR)
    
    // 如果今年的生日还没到，年龄减1
    if (today.get(Calendar.MONTH) < birthDay.get(Calendar.MONTH) ||
        (today.get(Calendar.MONTH) == birthDay.get(Calendar.MONTH) && 
         today.get(Calendar.DAY_OF_MONTH) < birthDay.get(Calendar.DAY_OF_MONTH))) {
        age--
    }
    
    return if (age < 0) 0 else age
}
```

### 3. 表单提交流程

```
用户点击提交按钮
↓
表单验证 (姓名、生日、年龄)
↓
准备提交数据 (nickname, gender, birthday, invitationCode)
↓
调用 saveUserInfo 接口
↓
处理响应结果
↓
成功: 刷新用户信息，导航到主页
↓
失败: 显示错误提示
```

### 4. 提交代码分析

```kotlin
// 准备提交到服务器的数据
val submitData = mapOf(
    "nickname" to name,
    "gender" to genderInt,
    "birthday" to age,
    "invitationCode" to invitationCode
)

// 使用 UserInfoRepository 保存用户信息到服务器
PuxxiCoroutine.io {
    try {
        userInfoRepository.saveUserInfo(data = submitData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        LogUtil.d("ProfileViewModel", "用户信息保存成功")
                        refreshUserInfo()
                        updateState { 
                            copy(isLoading = false, error = null, submitSuccess = true) 
                        }
                        GlobalLoadingManager.hide()
                    }
                    is Resource.Error -> {
                        LogUtil.e("ProfileViewModel", "用户信息保存失败: ${resource.message}")
                        updateState { 
                            copy(error = resource.message, isLoading = false) 
                        }
                        GlobalLoadingManager.hide()
                    }
                    is Resource.Loading -> {
                        GlobalLoadingManager.show()
                    }
                }
            }
    } catch (e: Exception) {
        // 异常处理
    }
}
```

## 状态管理

### 1. UI 状态

个人资料模块使用 `ProfileUiState` 类管理 UI 状态：

```kotlin
data class ProfileUiState(
    val invitationCode: String = "",
    val isLoading: Boolean = false,
    val error: String? = null,
    val userInfo: UserInfo? = null,
    val avatarUploadState: AvatarUploadState = AvatarUploadState.Idle,
    val submitSuccess: Boolean = false
) : UiState
```

### 2. 状态更新

使用 `updateState` 方法更新 UI 状态：

```kotlin
updateState {
    copy(
        isLoading = false,
        error = null,
        avatarUploadState = AvatarUploadState.Success
    )
}
```

### 3. 状态监听

在 UI 层使用 `collectAsState` 和 `LaunchedEffect` 监听状态变化：

```kotlin
val state by viewModel.uiState.collectAsState()

LaunchedEffect(state.submitSuccess) {
    if (state.submitSuccess) {
        navController.navigate("main") {
            popUpTo("profile_form") { inclusive = true }
        }
    }
}
```

## 数据流

### 1. 用户信息流

```
UserManager.currentUser (StateFlow)
↓
ProfileViewModel 监听变化
↓
更新 ProfileUiState.userInfo
↓
UI 层通过 collectAsState 获取最新状态
↓
更新表单显示
```

### 2. 头像上传流

```
用户选择图片
↓
ProfileViewModel.updateAvatar(uri)
↓
更新 avatarUploadState 为 GettingOssPolicy
↓
获取 OSS 策略
↓
更新 avatarUploadState 为 Uploading(progress)
↓
上传文件到 OSS
↓
更新 avatarUploadState 为 UpdatingAvatar
↓
调用更新头像接口
↓
更新 UserManager 中的用户信息
↓
更新 avatarUploadState 为 Success
↓
UI 层响应状态变化，刷新头像显示
```

### 3. 表单提交流

```
用户点击提交按钮
↓
ProfileViewModel.submitProfile(...)
↓
表单验证
↓
更新 isLoading 为 true
↓
调用 saveUserInfo 接口
↓
处理响应结果
↓
成功: 更新 submitSuccess 为 true
↓
UI 层监听 submitSuccess 变化，导航到主页
```

## 安全机制

### 1. 输入验证

- **必填字段验证**：确保必要字段不为空
- **格式验证**：确保日期格式正确
- **年龄验证**：确保用户年龄符合要求（大于18岁）

### 2. 文件安全

- **文件类型验证**：确保上传的是图片文件
- **文件大小限制**：避免上传过大的文件
- **安全传输**：使用 HTTPS 进行文件传输

### 3. 数据加密

- **敏感数据加密**：用户信息使用 AES 加密存储
- **安全存储**：使用安全的 SharedPreferences 存储机制
- **临时凭证**：使用 OSS 临时凭证进行文件上传

## 性能优化

### 1. 图片处理

- **图片压缩**：上传前对图片进行压缩，减少网络传输量
- **异步加载**：使用 `ImageLoader` 异步加载图片，避免阻塞 UI 线程
- **缓存机制**：实现图片缓存，减少重复加载

### 2. 网络优化

- **并发控制**：使用协程管理并发请求
- **重试机制**：网络请求失败时自动重试
- **缓存策略**：缓存用户信息，减少网络请求

### 3. UI 优化

- **延迟加载**：使用 `LazyColumn` 实现列表延迟加载
- **状态恢复**：保存和恢复用户输入状态
- **加载动画**：使用 `GlobalLoadingView` 提供加载反馈

## 代码结构

### 1. MVVM 架构

个人资料模块遵循 MVVM 架构设计模式：

- **Model**: `UserInfo`, `OssPolicyResult` 等数据模型
- **View**: `ProfileFormScreen` Compose UI
- **ViewModel**: `ProfileViewModel` 处理业务逻辑

### 2. 依赖注入

使用 Hilt 进行依赖注入，简化组件间依赖关系：

```kotlin
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val userInfoRepository: UserInfoRepository,
    private val fileUploadDownloadUtil: FileUploadDownloadUtil
) : BaseViewModel<ProfileUiState>() {
    // ...
}
```

### 3. 协程与 Flow

使用 Kotlin 协程和 Flow 处理异步操作和数据流：

```kotlin
PuxxiCoroutine.io {
    userInfoRepository.getUserOssPolicyPostV2()
        .collect { resource ->
            // 处理响应
        }
}
```

### 4. 组件化设计

个人资料模块采用组件化设计，将复杂 UI 拆分为可复用组件：

- `CustomTextField`: 自定义输入框组件
- `GenderOption`: 性别选择组件
- `DialogManagerComponent`: 对话框管理组件
- `GlobalLoadingView`: 全局加载组件

### 5. 文件结构

```
profile/
├── ProfileFormScreen.kt   # 个人资料表单UI
├── ProfileViewModel.kt    # 个人资料业务逻辑
└── AvatarUpload_README.md # 头像上传功能文档
```

### 6. 相关组件

- `UserInfoRepository`: 用户信息数据仓库
- `FileUploadDownloadUtil`: 文件上传下载工具
- `UserManager`: 用户信息管理器
- `DialogManager`: 对话框管理器
- `ImageLoader`: 图片加载器 