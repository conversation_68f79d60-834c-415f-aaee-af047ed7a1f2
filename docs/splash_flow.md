# Puxxi 应用启动流程文档

## 目录
1. [概述](#概述)
2. [UI 流程](#ui-流程)
3. [应用配置初始化](#应用配置初始化)
4. [密钥获取与解密流程](#密钥获取与解密流程)
5. [缓存策略](#缓存策略)
6. [导航逻辑](#导航逻辑)
7. [代码结构](#代码结构)
8. [安全考量](#安全考量)

## 概述

Puxxi 应用的启动流程采用现代化的 MVVM 架构设计，使用 Jetpack Compose 构建 UI，Kotlin 协程处理异步操作。启动流程包括显示闪屏页面、初始化应用配置、获取和解密服务器密钥、处理缓存策略，以及根据用户登录状态进行导航。

## UI 流程

### 1. 闪屏页面

闪屏页面是用户打开应用时看到的第一个界面，主要用于展示应用品牌标识，同时在后台完成必要的初始化工作：

- **实现类**: `SplashScreen` 在 `SplashScreen.kt` 中
- **视觉元素**:
  - 全屏背景图片 (`R.mipmap.bg_splash`)
  - 居中的应用标志（当前被注释掉）

### 2. 状态监听

闪屏页面会根据 ViewModel 提供的状态决定导航行为：

```kotlin
// Monitor navigation events
LaunchedEffect(state.navigateToMain) {
    if (state.navigateToMain) {
        navController.navigate("main") {
            popUpTo("splash") { inclusive = true }
        }
    }
}

// Monitor login navigation
LaunchedEffect(state.navigateToLogin) {
    if (state.navigateToLogin) {
        navController.navigate("login") {
            popUpTo("splash") { inclusive = true }
        }
    }
}
```

### 3. 自动启动计时器

闪屏页面会在显示后自动启动一个计时器，触发配置初始化流程：

```kotlin
// Auto navigation
LaunchedEffect(Unit) {
    viewModel.startSplashTimer()
}
```

## 应用配置初始化

### 1. 启动流程

应用配置初始化流程如下：

```
SplashScreen 显示
↓
LaunchedEffect 触发 startSplashTimer()
↓
initializeAppConfig() 开始初始化应用配置
↓
尝试从缓存加载配置 (getAppConfig())
↓
如果缓存存在且有效，在后台刷新配置并导航
↓
如果缓存不存在或无效，从服务器获取配置
↓
处理配置数据 (processAppConfig())
↓
导航到登录页面或主页面
```

### 2. 关键代码分析

#### 启动计时器

```kotlin
fun startSplashTimer() {
    viewModelScope.launch {
        try {
            initializeAppConfig()
        } catch (e: Exception) {
            updateState { copy(isLoading = false, error = e.message) }
        }
    }
}
```

#### 初始化应用配置

```kotlin
private fun initializeAppConfig() {
    PuxxiCoroutine.io(
        tag = "AppConfig_Init",
        onError = { error ->
            // 配置初始化失败，使用默认配置
        }
    ) {
        // 先尝试从缓存加载配置
        appNetInterfaceRepository.getAppConfig()
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        // 缓存配置加载成功，在后台刷新
                        refreshConfigInBackground()
                        PuxxiCoroutine.delay(2000L)
                        updateState { copy(isLoading = false, navigateToLogin = true) }
                        GlobalLoadingManager.hide()
                    }
                    is Resource.Error -> {
                        // 缓存不存在，从服务器获取
                        fetchConfigFromServer()
                    }
                    is Resource.Loading -> {
                        PuxxiCoroutine.withMain { GlobalLoadingManager.show() }
                    }
                }
            }
    }
}
```

## 密钥获取与解密流程

### 1. 配置数据结构

应用配置数据包含多个加密字段，需要解密处理：

```kotlin
data class AppConfigData(
    val abc_k1: String?,
    val source: String?,
    val abc_k3: String?,
    val lang: String?,
    val isWebApp: Boolean?,
    val abc_k2: String?,
    val abc_k4: String?
)
```

### 2. 解密流程

配置数据解密流程如下：

```
获取 AppConfigData 对象
↓
从 abc_k2 和 abc_k3 解码出 appKey
↓
将 appKey 存储到 SharedPreferences
↓
从 abc_k4 解码出加密内容
↓
使用 appKey 解密内容，获取配置项 Map
↓
处理配置项，存储到 SharedPreferences
```

### 3. 关键代码分析

#### 配置处理与解密

```kotlin
private fun processAppConfig(data: AppConfigData) {
    // 从 abc_k2 和 abc_k3 解码出 appKey
    val appKey = AESUtil.decodeBase64ToString(data.abc_k2.toString()).plus(AESUtil.decodeBase64ToString(data.abc_k3.toString()))

    // 存储 appKey 到 SharedPreferences
    SPUtil.putString(SPKey.APP_SERVER_KEY, appKey)

    LogUtil.d("appKey", appKey)

    // 从 abc_k4 解码出加密内容
    val content = AESUtil.decodeBase64ToString(data.abc_k4.toString())
    val dataStr = content?.replace("\r\n", "")
    
    // 使用 appKey 解密内容
    val datass = AESUtil.decryptBase64(dataStr.toString(), appKey)
    
    // 解析为配置项 Map
    val dataMap: Map<String, Any> =
        gson.fromJson(datass, object : TypeToken<Map<String, Any>>() {}.type)
    LogUtil.d("content", dataMap.toString())

    // 处理配置项
    if (dataMap["items"] != null) {
        val items = dataMap["items"] as ArrayList<Map<*, *>>

        // 定义需要处理的配置项键值对
        val configKeys = mapOf(
            SPKey.ATTRIBUTION_SDK to true,
            SPKey.APP_FB_CLIENT_TOKEN to true,
            SPKey.GLT to true,
            SPKey.RC_APP_KEY to true,
            SPKey.APP_FB_ID to true,
            SPKey.RC_AREA_CODE to true,
            SPKey.TRANSLATE_V2 to true,
            SPKey.GOOGLE_TRANSLATION_KEY to true,
        )

        // 统一处理所有配置项
        configKeys.forEach { (key, shouldSave) ->
            val configItem = items.firstOrNull { it["name"] == key }
            val value = (configItem?.get("data") ?: "") as String
            if (shouldSave) {
                SPUtil.putString(key, value)
                LogUtil.d(key, value)
            }
        }
    }
}
```

#### AES 解密实现

```kotlin
// Base64解码（String -> String）
fun decodeBase64ToString(data: String): String? = decodeBase64(data)?.let { String(it, CHARSET) }

// AES解密Base64字符串
fun decryptBase64(data: String, key: String): String? {
    val decoded = try {
        Base64.decode(data, Base64.NO_WRAP)
    } catch (e: Exception) {
        return null
    }
    val decrypted = decrypt(decoded, key.toByteArray(CHARSET)) ?: return null
    return try {
        String(decrypted, CHARSET)
    } catch (e: Exception) {
        null
    }
}
```

## 缓存策略

### 1. 缓存优先策略

应用采用缓存优先策略，减少网络请求，提高启动速度：

```kotlin
fun getAppConfig(): Flow<Resource<AppConfigData>> {
    return networkClient.executeWithCacheTyped(
        cacheKey = CacheKeys.Config.appConfig(),
        fetchStrategy = FetchStrategy.CACHE_FIRST,
        expireTime = CONFIG_CACHE_EXPIRE_TIME,
        timeUnit = TimeUnit.MINUTES,
        type = AppConfigData::class.java,
        apiCall = {
            appNetInterfaceInfo.getAppConfig()
        }
    ).onEach { resource ->
        if (resource is Resource.Success) {
            processAppConfig(resource.data)
        }
    }
}
```

### 2. 后台刷新机制

即使缓存有效，应用也会在后台刷新配置，确保数据最新：

```kotlin
private fun refreshConfigInBackground() {
    PuxxiCoroutine.background(
        tag = "AppConfig_Refresh",
        onError = { error ->
        }
    ) {
        appNetInterfaceRepository.refreshAppConfig()
            .collect { resource ->
                // 处理刷新结果
            }
    }
}
```

### 3. 缓存过期时间

配置缓存有30分钟的过期时间，过期后会自动从服务器获取新配置：

```kotlin
private const val CONFIG_CACHE_EXPIRE_TIME = 30L // 30分钟
```

## 导航逻辑

### 1. 导航状态

SplashViewModel 提供两个导航状态：

```kotlin
data class SplashState(
    val isLoading: Boolean = true,
    val navigateToMain: Boolean = false,
    val navigateToLogin: Boolean = false,
    val error: String? = null
) : UiState
```

### 2. 导航决策

根据配置初始化结果和用户登录状态决定导航目标：

- 如果配置初始化成功，导航到登录页面：
  ```kotlin
  updateState { copy(isLoading = false, navigateToLogin = true) }
  ```

- 如果用户已登录，可以直接导航到主页面（当前未实现）：
  ```kotlin
  // 可能的实现
  if (UserManager.isLoggedIn) {
      updateState { copy(isLoading = false, navigateToMain = true) }
  } else {
      updateState { copy(isLoading = false, navigateToLogin = true) }
  }
  ```

### 3. 导航执行

在 SplashScreen 中监听导航状态变化，执行实际导航：

```kotlin
LaunchedEffect(state.navigateToLogin) {
    if (state.navigateToLogin) {
        navController.navigate("login") {
            popUpTo("splash") { inclusive = true }
        }
    }
}
```

## 代码结构

### 1. MVVM 架构

闪屏模块遵循 MVVM 架构设计模式：

- **Model**: `AppConfigData` 配置数据模型
- **View**: `SplashScreen` Compose UI
- **ViewModel**: `SplashViewModel` 处理业务逻辑

### 2. 依赖注入

使用 Hilt 进行依赖注入，简化组件间依赖关系：

```kotlin
@HiltViewModel
class SplashViewModel @Inject constructor(
    private val appNetInterfaceRepository : AppNetInterfaceRepository
) : BaseViewModel<SplashState>() {
    // ...
}
```

### 3. 协程与 Flow

使用 Kotlin 协程和 Flow 处理异步操作和数据流：

```kotlin
PuxxiCoroutine.io {
    appNetInterfaceRepository.getAppConfig()
        .collect { resource ->
            // 处理响应
        }
}
```

## 安全考量

### 1. 密钥保护

应用采用多层加密保护服务器密钥：

1. **分散存储**: 密钥被分散存储在 `abc_k2` 和 `abc_k3` 两个字段中
2. **Base64 编码**: 密钥使用 Base64 编码存储
3. **安全存储**: 解密后的密钥存储在 SharedPreferences 中

### 2. 配置加密

敏感配置数据使用 AES 加密保护：

1. **加密存储**: 配置数据存储在 `abc_k4` 字段中，使用 Base64 编码
2. **动态密钥**: 使用从服务器获取的 `appKey` 进行解密
3. **安全传输**: 配置数据通过 HTTPS 安全传输

### 3. 缓存安全

缓存数据也有安全保护机制：

1. **过期机制**: 配置缓存有30分钟的过期时间
2. **加密存储**: 敏感数据使用 AES 加密后存储
3. **内存保护**: 密钥和解密后的配置仅在内存中短暂存在 