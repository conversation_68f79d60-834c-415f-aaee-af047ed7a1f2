# Puxxi 应用登录流程文档

## 目录
1. [概述](#概述)
2. [UI 流程](#ui-流程)
3. [数据流](#数据流)
4. [安全机制](#安全机制)
5. [数据存储](#数据存储)
6. [错误处理](#错误处理)
7. [代码结构](#代码结构)
8. [最佳实践](#最佳实践)

## 概述

Puxxi 应用采用现代化的 MVVM 架构设计登录流程，使用 Jetpack Compose 构建 UI，Kotlin 协程处理异步操作，并实现了完整的数据加密与安全存储机制。登录流程包括条款确认、设备识别、用户认证、策略获取和数据持久化等步骤。

## UI 流程

### 1. 欢迎对话框

用户首次进入登录页面时，会显示欢迎对话框，要求用户同意服务条款和隐私政策：

- **实现类**: `WelcomeDialog` 在 `LoginScreen.kt` 中
- **交互方式**:
  - 用户可以选择"同意"或"不同意"
  - 选择"同意"会自动勾选条款复选框，并允许登录
  - 选择"不同意"会取消勾选条款复选框，禁用登录按钮
  - 点击右上角关闭按钮可关闭对话框

### 2. 登录页面

主登录页面包含以下元素：

- **背景**: 全屏背景图片
- **欢迎文本**: "Hi,Gimi"
- **登录按钮**: 金色渐变按钮，只有在用户接受条款后才能点击
- **条款复选框**: 自定义复选框组件，显示服务条款和隐私政策链接

### 3. 登录状态

登录页面会根据不同状态显示不同UI：

- **加载中**: 显示全局加载动画 (`GlobalLoadingManager.show()`)
- **错误状态**: 显示错误提示 (`ToastUtil.showError(error)`)
- **登录成功**: 自动导航到主页面 (`navController.navigate("main")`)

## 数据流

### 1. 登录流程

```
用户点击登录按钮
↓
LoginViewModel.login()
↓
获取设备唯一ID (DeviceInfoUtil.getUniqueDeviceId())
↓
调用登录API (appNetInterfaceRepository.login())
↓
处理登录响应 (processLoginResponse())
↓
获取应用策略 (getStrategyPostV2())
↓
更新UI状态 (isLoggedIn = true)
↓
导航到主页面
```

### 2. 关键代码分析

#### 登录请求

```kotlin
fun login() {
    updateState { copy(isLoading = true, error = null) }
    
    PuxxiCoroutine.io{
        try {
            appNetInterfaceRepository.login(data = mapOf(
                "oauthType" to "4",
                "token" to deviceInfoUtil.getUniqueDeviceId()
            ))
                .collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            getStrategyPostV2()
                        }
                        is Resource.Error -> {
                            updateState { copy(error = resource.message, isLoading = false) }
                        }
                        is Resource.Loading -> {
                        }
                    }
                }
        } catch (e: Exception) {
            updateState { copy(error = e.message ?: "Login failed", isLoading = false) }
        }
    }
}
```

#### 登录响应处理

```kotlin
private fun processLoginResponse(data: LoginResponse) {
    // 保存 token 到加密的 SharedPreferences
    data.token.let { token ->
        // 敏感信息使用加密存储
        SPUtil.putEncryptedString(SPKey.ACCESS_TOKEN, token)
        SPUtil.putBoolean(SPKey.IS_LOGIN, true)
        SPUtil.putLong(SPKey.LAST_LOGIN_TIME, System.currentTimeMillis())
    }

    UserManager.isFirstRegister = data.isFirstRegister

    // 保存用户信息到 Room 数据库和加密的 SharedPreferences
    data.userInfo?.let { userInfo ->
        // 保存必要的快速访问信息（常用字段，非敏感）
        SPUtil.putString(SPKey.USER_ID, userInfo.userId)

        // 将完整用户信息序列化为JSON并加密存储
        SPUtil.putEncryptedObject("user_info_secure", userInfo)
        
        // 使用 UserManager 保存用户信息到数据库
        UserManager.saveUserInfo(userInfo)
    }
}
```

## 安全机制

### 1. 设备识别

应用使用设备唯一标识符作为登录凭证：

```kotlin
fun getUniqueDeviceId(): String {
    return try {
        Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: ""
    } catch (e: Exception) {
        ""
    }
}
```

### 2. 数据加密

应用使用 AES 加密算法保护敏感数据：

#### AES 加密实现

```kotlin
object AESUtil {
    private const val AES = "AES"
    private const val TRANSFORMATION = "AES/ECB/PKCS5Padding"
    private val CHARSET: Charset = Charsets.UTF_8

    // AES加密，返回Base64字符串
    fun encrypt2Base64(data: String, key: String): String? {
        val encrypted = encrypt(data.toByteArray(CHARSET), key.toByteArray(CHARSET)) ?: return null
        return Base64.encodeToString(encrypted, Base64.NO_WRAP)
    }

    // AES解密Base64字符串
    fun decryptBase64(data: String, key: String): String? {
        val decoded = try {
            Base64.decode(data, Base64.NO_WRAP)
        } catch (e: Exception) {
            return null
        }
        val decrypted = decrypt(decoded, key.toByteArray(CHARSET)) ?: return null
        return try {
            String(decrypted, CHARSET)
        } catch (e: Exception) {
            null
        }
    }

    // AES加密核心方法
    fun encrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val keySpec = SecretKeySpec(key, AES)
            cipher.init(Cipher.ENCRYPT_MODE, keySpec)
            cipher.doFinal(data)
        } catch (e: Exception) {
            null
        }
    }

    // AES解密核心方法
    fun decrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val keySpec = SecretKeySpec(key, AES)
            cipher.init(Cipher.DECRYPT_MODE, keySpec)
            cipher.doFinal(data)
        } catch (e: Exception) {
            null
        }
    }
}
```

### 3. 加密密钥管理

应用使用 API 域名作为基础密钥，确保长度为 32 字节：

```kotlin
private val ENCRYPTION_KEY by lazy { 
    ApiConstants.APP_API_DOMAIN.padEnd(32, '0')
}
```

## 数据存储

### 1. 分层存储策略

应用采用分层存储策略，根据数据敏感度选择不同的存储方式：

- **非敏感数据**: 普通 SharedPreferences
- **敏感数据**: 加密 SharedPreferences
- **用户信息**: Room 数据库 + 内存缓存

### 2. SharedPreferences 工具类

`SPUtil` 提供了统一的数据存储接口，支持普通存储和加密存储：

```kotlin
// 普通存储
fun putString(key: String, value: String?) {
    getDefaultSP().edit { putString(key, value) }
}

// 加密存储
fun putEncryptedString(key: String, value: String?) {
    if (value == null) {
        remove(SECURE_PREFS_NAME, key)
        return
    }
    
    // 使用AESUtil加密
    val encryptedValue = AESUtil.encrypt2Base64(value, ENCRYPTION_KEY)
    putString(SECURE_PREFS_NAME, key, encryptedValue)
}

// 获取解密的字符串
fun getDecryptedString(key: String, defaultValue: String? = null): String? {
    val encryptedValue = getString(SECURE_PREFS_NAME, key) ?: return defaultValue
    
    // 使用AESUtil解密
    return AESUtil.decryptBase64(encryptedValue, ENCRYPTION_KEY) ?: defaultValue
}
```

### 3. 用户数据管理

`UserManager` 负责用户数据的统一管理：

- **内存缓存**: 使用 `StateFlow` 存储当前用户信息
- **数据库存储**: 使用 Room 数据库存储用户详细信息
- **快速访问**: 使用 SharedPreferences 存储常用字段

```kotlin
object UserManager {
    // 当前用户信息 StateFlow
    private val _currentUser = MutableStateFlow<UserInfo?>(null)
    val currentUser: StateFlow<UserInfo?> = _currentUser
    
    // 保存用户信息到数据库
    fun saveUserInfo(userInfo: UserInfo) {
        // 保存基本信息到SP
        SPUtil.putString(SPKey.USER_ID, userInfo.userId)

        // 转换为数据库实体
        val userEntity = convertToEntity(userInfo)
        
        // 保存到数据库
        PuxxiCoroutine.io {
            userDao?.insertOrUpdate(userEntity)
            // 更新内存中的用户信息
            PuxxiCoroutine.withMain {
                _currentUser.value = userInfo
            }
        }
    }
}
```

## 错误处理

### 1. 网络错误处理

登录过程中的网络错误通过 `Resource` 密封类进行统一处理：

```kotlin
sealed class Resource<out T> {
    data class Success<T>(val data: T) : Resource<T>()
    data class Error(val message: String) : Resource<Nothing>()
    object Loading : Resource<Nothing>()
}
```

### 2. UI 错误提示

错误信息通过 `ToastUtil` 显示给用户：

```kotlin
state.error?.let { error ->
    ToastUtil.showError(error)
}
```

### 3. 异常捕获

所有网络请求都包含在 try-catch 块中，确保异常不会导致应用崩溃：

```kotlin
try {
    appNetInterfaceRepository.login(data = mapOf(
        "oauthType" to "4",
        "token" to deviceInfoUtil.getUniqueDeviceId()
    ))
        .collect { resource ->
            // 处理响应
        }
} catch (e: Exception) {
    updateState { copy(error = e.message ?: "Login failed", isLoading = false) }
}
```

## 代码结构

### 1. MVVM 架构

登录模块遵循 MVVM 架构设计模式：

- **Model**: `UserInfo`, `LoginResponse` 等数据模型
- **View**: `LoginScreen` Compose UI
- **ViewModel**: `LoginViewModel` 处理业务逻辑

### 2. 依赖注入

使用 Hilt 进行依赖注入，简化组件间依赖关系：

```kotlin
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val appNetInterfaceRepository: AppNetInterfaceRepository,
    private val deviceInfoUtil: DeviceInfoUtil,
) : BaseViewModel<LoginState>() {
    // ...
}
```

### 3. 协程与 Flow

使用 Kotlin 协程和 Flow 处理异步操作和数据流：

```kotlin
PuxxiCoroutine.io {
    appNetInterfaceRepository.getStrategyPostV2().collect { resource ->
        // 处理响应
    }
}
```

## 最佳实践

### 1. 安全存储

- **敏感数据加密**: 用户令牌等敏感信息使用 AES 加密后存储
- **密钥管理**: 加密密钥不硬编码，而是动态生成

### 2. 用户体验

- **状态反馈**: 登录过程中显示加载动画
- **错误提示**: 登录失败时显示友好的错误信息
- **条款确认**: 强制用户确认服务条款和隐私政策

### 3. 代码质量

- **单一职责**: 每个类和方法只负责一个功能
- **可测试性**: 通过依赖注入和接口抽象提高代码可测试性
- **错误处理**: 全面的异常捕获和错误处理机制 