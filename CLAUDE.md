# Claude Code Configuration

## Java Environment
For this Android project, use Android Studio's bundled Java runtime for compilation:

```bash
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"
```

## Build Commands
- Clean build: `./gradlew clean build`
- Debug build: `./gradlew assembleDebug`  
- Release build: `./gradlew assembleRelease`
- Run tests: `./gradlew test`

## Migration Notes
- Project migrated from KAPT to KSP for annotation processing
- Using KSP for Hilt and Room compilers