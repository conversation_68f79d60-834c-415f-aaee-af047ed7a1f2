# Coding Standards & Naming Conventions

## Project Structure

```
app/
├── build.gradle.kts
├── src/
    ├── main/
        ├── java/com/liveandroid/puxxi/
            ├── di/                 # Dependency injection modules
            ├── data/               # Data layer
            │   ├── api/            # Remote API services
            │   ├── db/             # Local database
            │   ├── model/          # Data models (DTOs)
            │   ├── repository/     # Repository implementations
            │   └── preferences/    # SharedPreferences
            ├── domain/             # Domain layer
            │   ├── model/          # Domain models
            │   ├── repository/     # Repository interfaces
            │   └── usecase/        # Business logic use cases
            ├── presentation/       # UI layer
            │   ├── common/         # Shared UI components
            │   ├── navigation/     # Navigation graphs
            │   └── features/       # Feature-specific UI
            │       ├── feature1/
            │       │   ├── components/   # UI components
            │       │   ├── screens/      # Screens
            │       │   └── viewmodels/   # ViewModels
            └── utils/              # Utility classes
```

## Naming Conventions

### Files

- **Kotlin Files**: PascalCase matching the class name
  - `UserRepository.kt`
  - `HomeScreen.kt`
  
- **Compose UI Files**: PascalCase with component type
  - `HomeScreen.kt`
  - `ProfileCard.kt`
  - `CustomButton.kt`

- **Resource Files**: snake_case
  - Layouts: `activity_main.xml`, `fragment_home.xml`
  - Drawables: `ic_launcher.xml`, `bg_gradient.xml`
  - Values: `colors.xml`, `strings.xml`

### Classes & Interfaces

- **Classes**: PascalCase, descriptive nouns
  - `UserRepository`
  - `NetworkModule`

- **Interfaces**: PascalCase, adjective or noun
  - `Searchable`
  - `UserRepository`

- **ViewModels**: PascalCase with "ViewModel" suffix
  - `HomeViewModel`
  - `ProfileViewModel`

### Variables & Constants

- **Variables**: camelCase, descriptive nouns
  - `userName`
  - `isEnabled`

- **Constants**: UPPER_SNAKE_CASE
  - `MAX_RETRY_COUNT`
  - `API_BASE_URL`

- **Companion Object Constants**: UPPER_SNAKE_CASE
  - `companion object { const val MAX_LIMIT = 100 }`

### Functions

- **Functions**: camelCase, verb or verb phrases
  - `getUserProfile()`
  - `saveUserData()`
  - `isNetworkAvailable()`

- **Compose Functions**: PascalCase, noun
  - `ProfileScreen()`
  - `CustomButton()`

### Compose Best Practices

- **State Hoisting**: Lift state up to the caller when needed
- **Immutable Parameters**: Use immutable data types for parameters
- **Stateless Components**: Keep components stateless when possible
- **Preview Functions**: Provide preview functions for UI components
- **Theming**: Use MaterialTheme for consistent styling

### Architecture Components

- **Repository**: PascalCase with "Repository" suffix
  - `UserRepository`

- **Use Cases**: PascalCase with action and "UseCase" suffix
  - `GetUserProfileUseCase`
  - `UpdateUserSettingsUseCase`

- **Models**: PascalCase nouns
  - `User`
  - `ProfileSettings`

- **Data Models**: PascalCase with "Dto" or "Entity" suffix
  - `UserDto`
  - `ProfileEntity`

## Package Structure

- Package names should be all lowercase, no underscores
  - `com.stargate.pxo.feature`
  - `com.stargate.pxo.utils`

## Comments & Documentation

- Use KDoc format for public APIs
- Document complex algorithms
- Explain "why" not "what" in comments
- Include sample usage in public utility functions

## Import Order

1. Android imports
2. Third-party libraries
3. Java/Kotlin standard library
4. Project imports

## Code Style

- Max line length: 100 characters
- Indentation: 4 spaces
- Trailing commas for multiline parameter lists
- No wildcard imports
- No Hungarian notation 