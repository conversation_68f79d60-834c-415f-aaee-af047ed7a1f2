{"version": 1, "rules": [{"name": "Global App Project Rules", "description": "Production-grade international app development guidelines", "content": "You are a senior engineer with deep experience building production-grade AI agents, automations, and workflow systems. Every task you execute must follow this procedure without exception:\n\n1. Clarify <PERSON>ope First\n• Before writing any code, map out exactly how you will approach the task.\n• Confirm your interpretation of the objective.\n• Write a clear plan showing what functions, modules, or components will be touched and why.\n• Do not begin implementation until this is done and reasoned through.\n\n2. Locate Exact Code Insertion Point\n• Identify the precise file(s) and line(s) where the change will live.\n• Never make sweeping edits across unrelated files.\n• If multiple files are needed, justify each inclusion explicitly.\n• Do not create new abstractions or refactor unless the task explicitly says so.\n\n3. Minimal, Contained Changes\n• Only write code directly required to satisfy the task.\n• Avoid adding logging, comments, tests, TODOs, cleanup, or error handling unless directly necessary.\n• No speculative changes or \"while we're here\" edits.\n• All logic should be isolated to not break existing flows.\n\n4. Double Check Everything\n• Review for correctness, scope adherence, and side effects.\n• Ensure your code is aligned with the existing codebase patterns and avoids regressions.\n• Explicitly verify whether anything downstream will be impacted.\n\n5. Deliver Clearly\n• Summarize what was changed and why.\n• List every file modified and what was done in each.\n• If there are any assumptions or risks, flag them for review."}, {"name": "International Compliance Rules", "description": "Rules for ensuring app store compliance in global markets", "content": "This project is an international app targeting multiple global markets. Always ensure your code follows these compliance guidelines:\n\n1. Never use hardcoded user-facing strings\n• All text visible to users must come from localized resource files\n• Never concatenate strings with dynamic content\n• Use proper string formatting with placeholders\n\n2. Avoid politically sensitive content or references\n• No references to disputed territories or politically divisive topics\n• Use neutral terminology in all code and comments\n\n3. Follow proper data protection practices\n• Implement proper data encryption for all stored user data\n• Request minimal necessary permissions with clear explanations\n• Never collect data from users under 13 without parental consent\n• Include proper GDPR, CCPA, and other regional compliance\n\n4. Avoid store rejection risks\n• No implementation of alternative payment systems\n• Never implement hidden or undocumented features\n• Never use trademarked names without proper licensing\n• No incentivization for ratings, reviews or installations\n\n5. Technical implementation\n• Handle permissions denial gracefully\n• Optimize for low-end devices common in emerging markets\n• Implement proper localization for date/time/currency formats\n• Ensure RTL layout support for appropriate languages\n• Test for accessibility compliance"}, {"name": "Code Quality Standards", "description": "Modern Android development best practices", "content": "Follow modern Android development best practices in all code:\n\n1. Architecture\n• Implement Clean Architecture with clear separation of concerns\n• Use MVVM/MVI pattern for the presentation layer\n• Apply repository pattern for data management\n• Create clear module boundaries with explicit dependencies\n\n2. Jetpack Compose\n• Use Material Design 3 components and guidelines\n• Implement proper state management and state hoisting\n• Create responsive layouts for different screen sizes\n• Support dynamic theming and dark mode\n\n3. Code Style\n• Classes: PascalCase, descriptive nouns (UserRepository)\n• Variables: camelCase, descriptive nouns (userName)\n• Functions: camelCase, verb or verb phrases (getUserProfile)\n• Compose Functions: PascalCase, noun (ProfileScreen)\n• Constants: UPPER_SNAKE_CASE (MAX_RETRY_COUNT)\n\n4. Performance\n• Avoid blocking the main thread\n• Use proper coroutine dispatchers for async operations\n• Optimize startup time (<2 seconds cold start)\n• Implement proper caching strategies to reduce network calls\n• Handle network connectivity changes gracefully"}, {"id": "compose-usage", "description": "所有页面必须使用 Jetpack Compose 开发，禁止再使用 XML。", "enabled": true}, {"id": "mvvm-layer", "description": "ViewModel 不允许直接访问 Repository，必须通过 UseCase 中转。", "enabled": true}, {"id": "network-repository", "description": "所有网络请求必须封装在统一的 Repository 模块。", "enabled": true}, {"id": "no-static-context", "description": "禁止使用全局变量或静态变量持有 Context。", "enabled": true}, {"id": "state-management", "description": "所有状态管理使用 UiState 或类似 sealed class 管理。", "enabled": true}, {"id": "no-unique-id-access", "description": "不得收集用户设备唯一标识（如 IMEI、MAC 地址、Android ID、Serial Number）。", "enabled": true}, {"id": "no-privacy-apis", "description": "不允许调用 getInstalledPackages、getRunningAppProcesses 等 API 来收集用户隐私信息。", "enabled": true}, {"id": "permissions-explicit", "description": "所有权限申请必须显式请求，禁止静默获取或滥用权限。", "enabled": true}, {"id": "privacy-consent", "description": "所有数据收集行为必须提供清晰的用户授权和隐私说明。", "enabled": true}, {"id": "no-apk-download", "description": "不允许动态下载和执行 APK 安装行为（除非通过系统安装器和获得权限）。", "enabled": true}, {"id": "no-hardware-upload", "description": "网络请求中禁止上传任何硬件信息指纹或设备环境。", "enabled": true}, {"id": "sdk-policy", "description": "禁止使用未在 Google Play 审核通过的第三方 SDK（如灰产广告 SDK）。", "enabled": true}, {"id": "webview-policy", "description": "使用 WebView 时需禁用调试模式，关闭 file access，清理缓存。", "enabled": true}, {"id": "unique-app-assets", "description": "每个 App 必须使用独立的包名、签名、图标、资源文件、布局命名。", "enabled": true}, {"id": "unique-ui-naming", "description": "避免使用固定格式首页结构和通用组件命名（如 MainActivity、HomeFragment）。", "enabled": true}, {"id": "code-differentiation", "description": "所有模块必须有业务差异（代码结构 / 功能）避免重复；AI 生成代码时必须改写命名风格。", "enabled": true}, {"id": "unique-api-key", "description": "禁止多个 App 使用相同 API key（如广告、推送、统计等 SDK）。", "enabled": true}, {"id": "unique-storage-names", "description": "所有数据库名称、SharedPreferences 名称必须唯一，避免代码逻辑高度重复。", "enabled": true}, {"id": "unique-ui-assets", "description": "App 图标、名称、启动页必须具有差异化，不得复用。", "enabled": true}, {"id": "no-hardcoded-channel", "description": "禁止硬编码渠道信息、用户 ID、标识符等。", "enabled": true}, {"id": "network-timeout-retry", "description": "网络请求必须添加超时、重试逻辑，避免死循环请求或空白页面。", "enabled": true}, {"id": "ui-error-feedback", "description": "所有错误状态必须反馈给 UI 层并提供用户提示。", "enabled": true}, {"id": "result-wrapper", "description": "所有业务错误统一封装为 Result 或 Resource 类型。", "enabled": true}, {"id": "retrofit-catch", "description": "所有 Retrofit 请求必须包裹 try-catch 并处理 UnknownHostException、HttpException、SocketTimeoutException。", "enabled": true}, {"id": "module-auth-privacy", "description": "登录模块中禁止上传用户手机号、邮箱等隐私信息，除非已显式授权。", "module": "login", "enabled": true}, {"id": "module-me-analytics", "description": "Me 模块中禁止接入非 Google Play 合规的第三方分析 SDK。", "module": "me", "enabled": true}, {"id": "naming-viewmodel", "description": "所有 ViewModel 命名必须以 ViewModel 结尾，如 LoginViewModel。", "category": "naming", "enabled": true}, {"id": "naming-usecase", "description": "UseCase 命名必须为 XXXUseCase，例如 FetchUserUseCase。", "category": "naming", "enabled": true}, {"id": "naming-composable", "description": "所有 Composable 函数命名必须以大写字母开头，不得使用动词+名词结构（避免业务泄露）。", "category": "naming", "enabled": true}, {"id": "security-root-check", "description": "禁止集成任何 Root 检测相关 SDK，避免引发 Google Play 审核误伤。", "severity": "high", "enabled": true}, {"id": "security-clipboard-leak", "description": "禁止读取系统剪贴板内容（容易触发隐私违规）。", "severity": "critical", "enabled": true}, {"id": "dev-allow-logging", "description": "开发阶段允许日志输出（如 Log.d），上线前必须禁用。", "enabled": false}, {"id": "compose-preview", "description": "所有功能模块必须有单独的 Preview 页面用于 Compose UI 调试。", "enabled": false}, {"id": "version-management", "description": "使用 Version Catalog / buildSrc 管理依赖版本，避免重复。", "enabled": true}, {"id": "modular-testable", "description": "所有模块必须可编译、可运行、可独立测试。", "enabled": true}]}