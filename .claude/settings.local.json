{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(./gradlew build:*)", "<PERSON><PERSON>(./gradlew:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rg:*)", "Bash(grep:*)", "Bash(rm:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep -n 'useObfuscatedUrl.*\"\"\"\"rpc_base' /Users/<USER>/AndroidStudioProjects/Puxxi/app/src/main/java/com/liveandroid/puxxi/data/network/PuxxiUrl.kt)", "Bash(git checkout:*)", "Bash(java:*)", "<PERSON><PERSON>(javac:*)", "Bash(ping:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(nslookup:*)", "Bash(telnet:*)", "Bash(nc:*)", "<PERSON><PERSON>(echo:*)", "Bash(export:*)", "Bash(\"/Applications/Android Studio.app/Contents/jbr/Contents/Home/bin/java\" --version)", "WebFetch(domain:github.com)"], "deny": []}}