# Internationalization Guidelines

## Localization Framework

- Use Android's built-in localization system with string resources
- Implement right-to-left (RTL) layout support for Arabic, Hebrew, etc.
- Apply Jetpack Compose's localization capabilities

## String Management

- **NEVER hardcode user-facing strings** in the codebase
- Store all strings in `strings.xml` with descriptive keys:
  ```xml
  <!-- DO -->
  <string name="profile_welcome_message">Welcome back, %1$s</string>
  
  <!-- DON'T -->
  <!-- Direct use of "Welcome back, " + username in code -->
  ```
- Use string formatting for dynamic content:
  ```kotlin
  // DO
  getString(R.string.profile_welcome_message, userName)
  
  // DON'T
  "Welcome back, " + userName
  ```

- Keep translations focused on context:
  ```xml
  <!-- DO: Multiple specific strings -->
  <string name="button_save">Save</string>
  <string name="dialog_save">Save changes</string>
  
  <!-- DON'T: Generic strings that change meaning -->
  <string name="save">Save</string>
  ```

## Language Support

### Primary Markets
- English (en) - Base language
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Korean (ko)

### Regional Variants
- English (UK) - en-rGB
- English (US) - en-rUS
- French (Canada) - fr-rCA
- Portuguese (Brazil) - pt-rBR

## Date & Time

- Never hardcode date/time formats
- Use `DateFormat` and localized patterns:
  ```kotlin
  // DO
  DateFormat.getDateInstance(DateFormat.MEDIUM, locale).format(date)
  
  // DON'T
  "MM/dd/yyyy"
  ```

- Support 12/24 hour time formats based on locale
- Account for different first days of week (Sunday vs Monday)
- Use ISO 8601 for API communication, convert to local format for display

## Numbers & Currency

- Format numbers according to locale:
  ```kotlin
  // DO
  NumberFormat.getInstance(locale).format(number)
  
  // DON'T
  String.format("%.2f", number)
  ```

- Use proper currency formatting:
  ```kotlin
  // DO
  NumberFormat.getCurrencyInstance(locale).format(amount)
  
  // DON'T
  "$" + amount
  ```

- Support different decimal separators (. vs ,)
- Use appropriate measurement units (metric vs imperial)

## UI Considerations

- Design expandable UI elements to accommodate text length variations
- Avoid fixed-width text containers
- Use start/end constraints instead of left/right
- Ensure RTL layout support in all screens
- Support dynamic text sizing for accessibility
- Test with different languages directly in UI

## Images & Icons

- Use culturally neutral imagery
- Avoid text in images
- Replace text-containing images with proper text views and background images
- Consider cultural sensitivity in icon choices

## Localization Workflow

1. Extract all strings using Android's resource system
2. Create base strings.xml in values/ directory
3. Create language-specific strings in values-xx/ directories
4. Use professional translation services
5. Provide contextual information for translators
6. Test with pseudo-localization before release
7. Implement in-app language selection
8. Follow incremental localization based on market priorities

## Code Example

```kotlin
// DO: Proper date formatting
val dateFormat = DateFormat.getDateInstance(DateFormat.MEDIUM, locale)
val formattedDate = dateFormat.format(currentDate)

// DO: Proper number formatting
val numberFormat = NumberFormat.getNumberInstance(locale)
val formattedNumber = numberFormat.format(1234.56)

// DO: Proper currency formatting
val currencyFormat = NumberFormat.getCurrencyInstance(locale)
currencyFormat.currency = Currency.getInstance(currencyCode)
val formattedPrice = currencyFormat.format(19.99)
```

## Testing

- Test UI with all supported languages
- Verify text does not truncate or overflow
- Test RTL layout functionality
- Test date/time/number formats in each locale
- Verify language switching works correctly
- Test integration with input methods for different scripts
- Verify sorting follows locale-specific rules

## Resources

- [Android Localization Documentation](https://developer.android.com/guide/topics/resources/localization)
- [Material Design Internationalization Guidelines](https://m3.material.io/foundations/accessible-design/internationalization)
- [Language Support by Market Share](https://www.statista.com/statistics/262946/share-of-the-most-common-languages-on-the-internet/) 