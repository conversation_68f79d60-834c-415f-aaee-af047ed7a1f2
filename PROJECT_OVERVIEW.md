# Puxxi Android 项目概览

## 项目简介
Puxxi 是一个基于 Android Jetpack Compose 的社交应用，采用 Clean Architecture 架构模式，使用现代化的 Android 开发技术栈构建。

## 技术栈
- **UI 框架**: Jetpack Compose
- **架构模式**: MVVM + Clean Architecture  
- **依赖注入**: Hilt
- **数据库**: Room
- **网络请求**: Retrofit + OkHttp
- **异步处理**: Kotlin Coroutines + Flow
- **图片加载**: Coil
- **实时通信**: Socket.IO
- **数据统计**: Adjust
- **注解处理**: KSP

## 项目架构

### 分层架构
```
presentation/     # 表现层 (UI)
├── ui/          # Compose UI 组件
├── screens/     # 页面级组件
├── viewmodel/   # 视图模型
└── navigation/  # 导航管理

data/            # 数据层
├── network/     # 网络模块
├── db/          # 数据库模块
├── repository/  # 仓库层
└── manager/     # 数据管理器

di/              # 依赖注入模块
common/          # 公共工具类
```

### 核心模块
- **用户认证系统**: 登录、注册、token管理
- **用户资料管理**: 头像上传、个人信息编辑
- **主页导航**: 底部导航栏，多Tab页面
- **网络框架**: 统一的API调用、错误处理、缓存机制
- **数据存储**: Room数据库 + SharedPreferences
- **工具类集合**: 日志、Toast、协程、权限等

## 构建配置
- **编译SDK**: Android 35 (API 35)
- **最低支持**: Android 7.0 (API 24) 
- **目标SDK**: Android 35 (API 35)
- **Java版本**: Java 17
- **Kotlin版本**: 最新稳定版
- **Compose编译器**: 最新稳定版

## 开发环境
- 使用 Android Studio 最新版本
- 配置了 Java 17 环境
- 支持 Debug 和 Release 两种构建模式
- 集成了代码混淆和安全配置

## 项目特性
- 🎨 现代化UI设计 (Material Design 3)
- 🔐 安全的用户认证和数据加密
- 📱 响应式UI适配多种屏幕
- 🌐 完整的网络框架和错误处理
- 💾 多层级数据缓存策略
- 🔧 完善的日志和调试工具
- 📊 用户行为数据统计
- 🚀 协程优化的异步操作