# Kotlin Parcelize 插件别名配置

## 🎯 目标

将 `kotlin-parcelize` 插件配置为使用统一的 `alias` 格式，保持与项目其他插件配置的一致性。

## 🔧 配置步骤

### 1. **在 `gradle/libs.versions.toml` 中添加插件定义**

#### 修改前 ❌：
```toml
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
```

#### 修改后 ✅：
```toml
[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }  # ✅ 添加
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
```

### 2. **在 `app/build.gradle.kts` 中使用别名**

#### 修改前 ❌：
```kotlin
plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
    id("kotlin-parcelize")  // ❌ 不一致的格式
}
```

#### 修改后 ✅：
```kotlin
plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.kotlin.parcelize)  // ✅ 统一的 alias 格式
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
}
```

## ✅ 配置优势

### 1. **代码风格一致性**
- ✅ **统一格式** - 所有插件都使用 `alias(libs.plugins.xxx)` 格式
- ✅ **易于维护** - 插件版本统一在 `libs.versions.toml` 中管理
- ✅ **可读性好** - 代码风格保持一致，更易阅读

### 2. **版本管理优势**
- ✅ **集中管理** - 所有插件版本在一个文件中管理
- ✅ **版本同步** - `kotlin-parcelize` 使用与其他 Kotlin 插件相同的版本
- ✅ **升级便利** - 只需在一个地方修改版本号

### 3. **构建系统优势**
- ✅ **类型安全** - Gradle 版本目录提供类型安全的插件引用
- ✅ **IDE 支持** - 更好的 IDE 自动补全和错误检查
- ✅ **构建缓存** - 更好的构建缓存和性能

## 📋 技术细节

### 1. **插件 ID 说明**
```toml
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
```

- **id**: `org.jetbrains.kotlin.plugin.parcelize` - Kotlin Parcelize 插件的完整 ID
- **version.ref**: `kotlin` - 引用 `[versions]` 部分中定义的 `kotlin` 版本
- **版本同步**: 与其他 Kotlin 插件使用相同的版本，确保兼容性

### 2. **版本引用**
```toml
[versions]
kotlin = "2.0.0"  # 所有 Kotlin 插件使用相同版本

[plugins]
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
```

### 3. **插件应用顺序**
```kotlin
plugins {
    alias(libs.plugins.android.application)    # Android 应用插件
    alias(libs.plugins.kotlin.android)         # Kotlin Android 插件
    alias(libs.plugins.kotlin.compose)         # Kotlin Compose 插件
    alias(libs.plugins.kotlin.parcelize)       # Kotlin Parcelize 插件
    alias(libs.plugins.hilt)                   # Hilt 依赖注入插件
    alias(libs.plugins.ksp)                    # Kotlin Symbol Processing 插件
}
```

## 🎯 最佳实践

### 1. **插件版本管理**
```toml
# ✅ 推荐：相关插件使用相同版本
[versions]
kotlin = "2.0.0"

[plugins]
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }

# ❌ 避免：不同版本可能导致兼容性问题
kotlin-android = { id = "org.jetbrains.kotlin.android", version = "2.0.0" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version = "1.9.0" }
```

### 2. **插件命名规范**
```toml
# ✅ 推荐：使用清晰的命名
kotlin-android = { ... }
kotlin-compose = { ... }
kotlin-parcelize = { ... }

# ❌ 避免：模糊的命名
kotlin1 = { ... }
kotlin2 = { ... }
parcelize = { ... }
```

### 3. **构建文件组织**
```kotlin
plugins {
    // Android 相关
    alias(libs.plugins.android.application)
    
    // Kotlin 相关
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
    alias(libs.plugins.kotlin.parcelize)
    
    // 依赖注入和处理
    alias(libs.plugins.hilt)
    alias(libs.plugins.ksp)
}
```

## 🔧 验证配置

### 1. **同步项目**
- 在 Android Studio 中点击 "Sync Now"
- 确保没有同步错误

### 2. **检查插件应用**
```kotlin
// 验证 Parcelize 插件是否正确应用
@Parcelize
data class TestModel(val id: String) : Parcelable
```

### 3. **构建项目**
```bash
./gradlew build
```

## 🚀 总结

**Kotlin Parcelize 插件别名配置完成！**

### 配置成果：
1. ✅ **统一格式** - 所有插件都使用 `alias(libs.plugins.xxx)` 格式
2. ✅ **版本同步** - Parcelize 插件与其他 Kotlin 插件使用相同版本
3. ✅ **集中管理** - 插件配置集中在 `libs.versions.toml` 中
4. ✅ **代码一致性** - 保持项目代码风格的一致性

### 技术优势：
- 🎯 **类型安全** - Gradle 版本目录提供类型安全的引用
- ⚡ **构建性能** - 更好的构建缓存和性能
- 🔧 **易于维护** - 版本升级只需修改一个地方
- 📱 **IDE 支持** - 更好的自动补全和错误检查

现在项目中的所有插件都使用统一的 `alias` 格式，代码风格保持一致，更易于维护和管理！
