# 主页导航模块文档

## 模块概述
主页导航模块是应用的核心导航中心，提供底部导航栏、多Tab页面管理、页面路由等功能，是用户操作的主要入口。

## 模块位置
```
presentation/ui/screens/main/        # 主页面组件
├── MainScreen.kt                   # 主页面容器
└── MainViewModel.kt                # 主页业务逻辑

presentation/ui/navigation/         # 导航配置
└── PuxxiNavigation.kt             # 全局导航配置

res/drawable/                       # 导航图标
├── ic_home.xml                     # 首页图标
├── ic_home_selected.xml            # 首页选中图标
├── ic_discover.xml                 # 发现图标
├── ic_discover_selected.xml        # 发现选中图标
├── ic_message.xml                  # 消息图标
├── ic_message_selected.xml         # 消息选中图标
├── ic_profile.xml                  # 个人图标
└── ic_profile_selected.xml         # 个人选中图标
```

## 核心功能

### 1. 底部导航栏
- **四个主要Tab**: 首页、发现、消息、个人中心
- **图标状态**: 选中和未选中状态的视觉反馈
- **导航控制**: 平滑的页面切换动画
- **状态保持**: 各Tab页面状态独立保持

### 2. 页面路由管理
- **导航图**: 使用Navigation Compose构建导航图
- **深度链接**: 支持应用内外部链接跳转
- **回退栈**: 智能的页面回退栈管理
- **参数传递**: 页面间数据传递和共享

### 3. 用户界面
- **Material Design 3**: 遵循最新设计规范
- **响应式布局**: 适配不同屏幕尺寸
- **主题支持**: 支持明暗主题切换
- **无障碍访问**: 完整的无障碍功能支持

## 技术实现

### MainScreen.kt
```kotlin
// 主要组件实现
@Composable
fun MainScreen() {
    // 底部导航栏容器
    Scaffold(bottomBar = { BottomNavigationBar() }) {
        // 主内容区域
        NavHost(navController, startDestination) {
            // 各个页面的路由配置
        }
    }
}

// 底部导航栏实现
@Composable
fun BottomNavigationBar() {
    NavigationBar {
        // 首页、发现、消息、个人中心四个Tab
    }
}
```

### 导航Tab配置
```kotlin
enum class MainTab(
    val route: String,
    val title: String,
    val icon: Int,
    val selectedIcon: Int
) {
    HOME("home", "首页", R.drawable.ic_home, R.drawable.ic_home_selected),
    DISCOVER("discover", "发现", R.drawable.ic_discover, R.drawable.ic_discover_selected), 
    MESSAGE("message", "消息", R.drawable.ic_message, R.drawable.ic_message_selected),
    PROFILE("profile", "我的", R.drawable.ic_profile, R.drawable.ic_profile_selected)
}
```

## 页面结构

### 1. 首页 (Home)
- **功能**: 主要内容展示，推荐内容
- **特性**: 支持下拉刷新、无限滚动
- **组件**: 轮播图、内容列表、快捷入口

### 2. 发现 (Discover)  
- **功能**: 内容发现和探索功能
- **特性**: 分类浏览、搜索功能
- **组件**: 分类标签、内容网格、筛选器

### 3. 消息 (Message)
- **功能**: 聊天消息和通知中心
- **特性**: 实时消息、消息分类
- **组件**: 会话列表、未读提示、消息搜索

### 4. 个人中心 (Profile)
- **功能**: 用户个人信息和设置
- **特性**: 个人资料、设置选项、统计信息
- **组件**: 用户头像、菜单列表、快捷操作

## 导航流程

### 基本导航流程
1. **应用启动** → 检查登录状态
2. **首页显示** → 默认显示首页内容
3. **Tab切换** → 用户点击底部导航切换页面
4. **状态保持** → 各页面状态独立维护
5. **深度导航** → 支持页面内二级导航

### 导航状态管理
```kotlin
// 导航状态跟踪
val navBackStackEntry by navController.currentBackStackEntryAsState()
val currentDestination = navBackStackEntry?.destination

// Tab选中状态
val selected = currentDestination?.hierarchy?.any { 
    it.route == tab.route 
} == true
```

## UI设计特性

### 视觉设计
- 🎨 **现代化设计**: Material Design 3设计语言
- 🌈 **品牌色彩**: 统一的品牌色彩体系
- 📱 **响应式**: 适配手机和平板设备
- ✨ **动画效果**: 流畅的切换动画

### 交互设计
- 👆 **直观操作**: 清晰的交互反馈
- 🔄 **手势支持**: 支持滑动切换等手势
- ⚡ **快速响应**: 优化的响应速度
- 🎯 **精准点击**: 合适的点击区域大小

## 性能优化

### 内存管理
- **页面复用**: 已加载页面的内存复用
- **懒加载**: 需要时才加载页面内容
- **状态清理**: 离开页面时清理不必要状态
- **图片缓存**: 图标和图片的智能缓存

### 渲染优化
- **组合优化**: Compose重组最小化
- **列表优化**: LazyColumn/LazyRow优化
- **动画优化**: 硬件加速动画
- **预加载**: 预加载下一页面内容

## 配置选项

### 导航配置
```kotlin
// 导航行为配置
NavigationBarItem(
    selected = selected,
    onClick = {
        navController.navigate(tab.route) {
            // 避免重复导航到同一页面
            popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
            }
            // 恢复状态
            restoreState = true
            // 单实例模式
            launchSingleTop = true
        }
    }
)
```

### 主题配置
- **颜色主题**: 支持浅色/深色主题
- **字体大小**: 系统字体大小适配
- **动画时长**: 可配置动画持续时间
- **导航高度**: 导航栏高度自适应

## 扩展性设计
- 🔧 **模块化**: 各Tab页面独立模块
- 📦 **插件化**: 支持动态添加新Tab
- 🔄 **配置化**: 导航结构可配置
- 🚀 **热更新**: 支持导航配置热更新