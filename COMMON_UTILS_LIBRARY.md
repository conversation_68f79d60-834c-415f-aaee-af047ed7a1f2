# 公共工具类库文档

## 模块概述
公共工具类库提供了应用开发中常用的工具类和辅助功能，包括日志管理、Toast提示、协程工具、权限处理、设备信息、文件操作等核心功能。

## 模块位置
```
common/                             # 公共模块根目录
├── util/                          # 工具类目录
│   ├── coroutine/                 # 协程相关工具
│   ├── device/                    # 设备相关工具
│   ├── log/                       # 日志工具
│   ├── AESUtil.kt                 # 加密工具
│   ├── DeviceInfoUtil.kt          # 设备信息工具
│   ├── FileUploadDownloadUtil.kt  # 文件操作工具
│   ├── ImagePickerUtil.kt         # 图片选择工具
│   ├── NetworkUtils.kt            # 网络工具
│   ├── PermissionUtil.kt          # 权限工具
│   ├── SPUtil.kt                  # SharedPreferences工具
│   ├── ToastUtil.kt               # Toast工具
│   └── UiUtils.kt                 # UI工具
├── constant/                      # 常量定义
└── base/                         # 基础类
```

## 核心工具类

### 1. 日志管理 (LogUtil)
- **多级日志**: DEBUG, INFO, WARN, ERROR, FATAL
- **格式化输出**: 支持JSON格式、边框美化
- **文件日志**: 自动写入日志文件
- **异步处理**: 异步写入提高性能
- **线程信息**: 显示线程和方法信息

```kotlin
// 使用示例
LogUtil.d("Tag", "Debug message")
LogUtil.i("Tag", "Info message") 
LogUtil.w("Tag", "Warning message")
LogUtil.e("Tag", "Error message", exception)

// 配置示例
val logConfig = LogConfig.debugConfig().copy(
    globalTag = "Puxxi",
    showThreadInfo = true,
    enableFileLog = true,
    enableAsync = true
)
```

### 2. Toast提示 (ToastUtil)
- **智能适配**: 根据设备自动选择最佳实现
- **多种样式**: 成功、错误、警告、信息等样式
- **防重复**: 自动防止重复显示相同内容
- **线程安全**: 支持在任意线程调用
- **自定义样式**: 支持自定义Toast样式

```kotlin
// 使用示例  
ToastUtil.showSuccess("操作成功")
ToastUtil.showError("操作失败")
ToastUtil.showWarning("警告信息")
ToastUtil.showInfo("提示信息")
ToastUtil.show("普通消息")
```

### 3. 协程工具 (CoroutineUtils)
- **协程管理器**: 统一的协程作用域管理
- **异常监控**: 全局协程异常监控和处理
- **作用域管理**: 自动管理协程生命周期
- **性能监控**: 协程执行性能监控
- **扩展函数**: 便捷的协程扩展函数

```kotlin
// 协程作用域管理
val scopeManager = CoroutineScopeManager.getInstance()
val scope = scopeManager.createScope("UserScope")

// 异常监控
val monitor = CoroutineExceptionMonitor.getInstance()
monitor.addGlobalExceptionListener { exceptionInfo ->
    LogUtil.e("Coroutine", "Exception: ${exceptionInfo.exceptionType}")
}

// ViewModel扩展
viewModel.launchSafely {
    // 安全的协程执行
}
```

### 4. 权限管理 (PermissionUtil)
- **动态权限**: Android 6.0+ 动态权限申请
- **权限检查**: 便捷的权限状态检查
- **批量申请**: 支持一次申请多个权限
- **权限说明**: 用户友好的权限说明对话框
- **降级处理**: 权限被拒绝时的功能降级

```kotlin
// 权限检查
if (PermissionUtil.hasPermission(context, Manifest.permission.CAMERA)) {
    // 有权限，执行操作
}

// 权限申请
PermissionUtil.requestPermissions(
    activity,
    arrayOf(Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE)
) { granted ->
    if (granted) {
        // 权限获取成功
    } else {
        // 权限被拒绝
    }
}
```

### 5. 加密工具 (AESUtil)
- **AES加密**: 标准AES-256加密算法
- **密钥管理**: 安全的密钥生成和存储
- **数据保护**: 敏感数据加密保护
- **性能优化**: 高效的加密解密实现

```kotlin
// 字符串加密
val encrypted = AESUtil.encrypt("sensitive data", key)
val decrypted = AESUtil.decrypt(encrypted, key)

// 文件加密
AESUtil.encryptFile(inputFile, outputFile, key)
AESUtil.decryptFile(encryptedFile, outputFile, key)
```

### 6. 文件操作 (FileUploadDownloadUtil)
- **文件上传**: 多种上传方式支持
- **断点续传**: 大文件断点续传功能
- **进度回调**: 实时上传下载进度
- **多线程**: 多线程并发上传下载
- **错误重试**: 自动错误重试机制

```kotlin
// 文件上传
FileUploadDownloadUtil.uploadFile(
    file = file,
    url = uploadUrl,
    progressCallback = { progress ->
        // 更新进度UI
    },
    onSuccess = { result ->
        // 上传成功
    },
    onError = { error ->
        // 上传失败
    }
)
```

### 7. 图片选择 (ImagePickerUtil)
- **多种来源**: 相机拍摄、相册选择
- **权限处理**: 自动处理相关权限
- **图片压缩**: 智能图片压缩优化
- **格式转换**: 支持多种图片格式
- **结果回调**: 简单的结果回调接口

```kotlin
// 图片选择
ImagePickerUtil.selectImage(
    activity = activity,
    allowCamera = true,
    allowGallery = true,
    maxSize = 1024 * 1024, // 1MB
    onResult = { uri ->
        // 处理选择的图片
    }
)
```

### 8. 设备信息 (DeviceInfoUtil)
- **设备标识**: 获取唯一设备标识
- **系统信息**: Android版本、厂商等信息
- **硬件信息**: CPU、内存、存储等硬件信息
- **网络信息**: 网络类型、运营商等信息
- **兼容性检查**: 设备兼容性检查

```kotlin
// 设备信息获取
val deviceId = DeviceInfoUtil.getDeviceId(context)
val androidVersion = DeviceInfoUtil.getAndroidVersion()
val manufacturer = DeviceInfoUtil.getManufacturer()
val totalMemory = DeviceInfoUtil.getTotalMemory()
val availableStorage = DeviceInfoUtil.getAvailableStorage()
```

### 9. SharedPreferences工具 (SPUtil)
- **类型安全**: 支持各种数据类型的存储
- **加密存储**: 敏感数据自动加密存储
- **批量操作**: 支持批量读写操作
- **默认值**: 便捷的默认值设置
- **观察者模式**: 数据变化监听

```kotlin
// 普通存储
SPUtil.putString(SPKey.USER_NAME, "张三")
val userName = SPUtil.getString(SPKey.USER_NAME, "")

// 加密存储
SPUtil.putEncryptedString(SPKey.ACCESS_TOKEN, token)
val token = SPUtil.getDecryptedString(SPKey.ACCESS_TOKEN)

// 批量操作
SPUtil.editor {
    putString("key1", "value1")
    putInt("key2", 123)
    putBoolean("key3", true)
}
```

### 10. 网络工具 (NetworkUtils)
- **网络状态**: 实时网络状态监听
- **连接类型**: WiFi、移动网络等类型判断
- **网络质量**: 网络连接质量评估
- **运营商信息**: 移动网络运营商信息

```kotlin
// 网络状态检查
val isConnected = NetworkUtils.isNetworkAvailable(context)
val isWiFi = NetworkUtils.isWiFiConnected(context)
val isMobile = NetworkUtils.isMobileConnected(context)

// 网络监听
NetworkUtils.registerNetworkCallback(context) { isAvailable ->
    if (isAvailable) {
        // 网络可用
    } else {
        // 网络不可用
    }
}
```

## 常量管理

### SPKey - SharedPreferences键名管理
```kotlin
object SPKey {
    const val IS_LOGIN = "is_login"
    const val USER_ID = "user_id"
    const val ACCESS_TOKEN = "access_token"
    const val USER_NAME = "user_name"
    const val USER_AVATAR = "user_avatar"
    // 更多常量...
}
```

### ConstantsConfig - 应用常量配置
```kotlin
object ConstantsConfig {
    // 网络配置
    const val CONNECT_TIMEOUT = 15L
    const val READ_TIMEOUT = 30L
    const val WRITE_TIMEOUT = 30L
    
    // 缓存配置
    const val CACHE_SIZE = 50L * 1024 * 1024 // 50MB
    const val CACHE_MAX_AGE = 7 * 24 * 60 * 60 // 7天
    
    // 文件配置
    const val MAX_FILE_SIZE = 10L * 1024 * 1024 // 10MB
    const val IMAGE_QUALITY = 85 // 图片压缩质量
}
```

## 基础类

### BaseViewModel
```kotlin
abstract class BaseViewModel : ViewModel() {
    // 统一的加载状态管理
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    // 统一的错误处理
    private val _error = MutableLiveData<String>()
    val error: LiveData<String> = _error
    
    // 便捷方法
    protected fun showLoading() { _isLoading.value = true }
    protected fun hideLoading() { _isLoading.value = false }
    protected fun showError(message: String) { _error.value = message }
}
```

## 工具类特性

### 性能优化
- ⚡ **懒加载**: 工具类采用懒加载模式
- 🔄 **对象池**: 复用对象减少GC压力
- 📊 **缓存机制**: 计算结果智能缓存
- 🚀 **异步处理**: 耗时操作异步执行

### 线程安全
- 🔒 **线程安全**: 所有工具类都是线程安全的
- 🎯 **主线程检查**: 自动检查UI操作线程
- ⚙️ **并发控制**: 合理的并发控制机制
- 🛡️ **异常保护**: 完善的异常保护机制

### 错误处理
- 🚨 **异常捕获**: 全面的异常捕获和处理
- 📝 **错误日志**: 详细的错误日志记录
- 🔄 **重试机制**: 智能的错误重试机制
- 💬 **用户提示**: 友好的用户错误提示

## 使用建议
- 📖 **统一使用**: 项目中统一使用这些工具类
- 🔧 **配置优先**: 优先使用配置而非硬编码
- 📊 **性能监控**: 关注工具类的性能表现
- 🧪 **充分测试**: 充分测试各种边界情况