# Puxxi Android 项目文档索引

## 📖 文档导航

本项目包含完整的模块化文档，帮助开发者快速了解和使用Puxxi应用的各个功能模块。

### 📋 主要文档列表

| 文档名称 | 描述 | 位置 |
|---------|------|------|
| [项目概览](PROJECT_OVERVIEW.md) | 项目整体架构和技术栈介绍 | 根目录 |
| [用户认证模块](USER_AUTHENTICATION.md) | 登录、注册、token管理功能 | 根目录 |
| [用户资料管理](USER_PROFILE_MANAGEMENT.md) | 个人信息编辑、头像上传功能 | 根目录 |
| [主页导航模块](MAIN_NAVIGATION.md) | 底部导航、页面路由管理 | 根目录 |
| [数据层架构](DATA_LAYER_ARCHITECTURE.md) | 网络请求、数据库、Repository模式 | 根目录 |
| [公共工具类库](COMMON_UTILS_LIBRARY.md) | 日志、Toast、协程、权限等工具 | 根目录 |

### 🔧 技术专项文档

| 专项文档 | 描述 | 位置 |
|---------|------|------|
| [协程框架](app/src/main/java/com/stargate/pxo/common/util/coroutine/README.md) | 协程工具和最佳实践 | common/util/coroutine/ |
| [网络缓存框架](app/src/main/java/com/stargate/pxo/data/network/README.md) | 网络请求和多级缓存 | data/network/ |
| [头像上传功能](app/src/main/java/com/stargate/pxo/presentation/ui/screens/profile/AvatarUpload_README.md) | 头像上传完整实现 | presentation/ui/screens/profile/ |
| [拦截器说明](app/src/main/java/com/stargate/pxo/data/network/interceptor/README.md) | 网络拦截器配置 | data/network/interceptor/ |
| [存储方案](app/src/main/java/com/stargate/pxo/data/network/storage/README.md) | 多级存储架构 | data/network/storage/ |

### 📋 开发规范文档

| 规范文档 | 描述 | 位置 |
|---------|------|------|
| [编码规范](CODING_STANDARDS.md) | 代码风格和编码标准 | 根目录 |
| [安全检查清单](SECURITY_CHECKLIST.md) | 安全开发指南 | 根目录 |
| [合规规则](COMPLIANCE_RULES.md) | 合规性要求和规则 | 根目录 |
| [国际化指南](INTERNATIONALIZATION_GUIDE.md) | 多语言支持指南 | 根目录 |

## 🏗️ 项目架构总览

```
Puxxi Android App
├── 表现层 (Presentation Layer)
│   ├── UI组件 (Jetpack Compose)
│   ├── 页面 (Screens)
│   ├── 视图模型 (ViewModels)
│   └── 导航 (Navigation)
├── 数据层 (Data Layer)
│   ├── 网络模块 (Network)
│   ├── 数据库 (Room Database)
│   ├── 仓库层 (Repository)
│   └── 缓存管理 (Cache Management)
├── 公共模块 (Common)
│   ├── 工具类 (Utils)
│   ├── 扩展函数 (Extensions)
│   ├── 常量 (Constants)
│   └── 基础类 (Base Classes)
└── 依赖注入 (Dependency Injection)
    └── Hilt模块 (Hilt Modules)
```

## 🚀 快速开始

### 1. 环境配置
```bash
# 设置Java环境
export JAVA_HOME="/Applications/Android Studio.app/Contents/jbr/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"
```

### 2. 构建项目
```bash
# 清理构建
./gradlew clean build

# 调试构建
./gradlew assembleDebug

# 发布构建
./gradlew assembleRelease

# 运行测试
./gradlew test
```

### 3. 主要功能模块

#### 用户认证
- 支持账号密码登录
- 安全的Token管理
- 自动登录状态恢复

#### 用户资料管理
- 个人信息编辑
- 头像上传功能
- 阿里云OSS存储

#### 主页导航
- 四个主要Tab页面
- 底部导航栏
- 页面状态保持

#### 数据管理
- Room数据库存储
- 多级缓存策略
- 网络请求框架

## 📱 技术特性

### 🎨 UI/UX
- **Jetpack Compose**: 现代化声明式UI
- **Material Design 3**: 最新设计规范
- **响应式布局**: 适配各种屏幕尺寸
- **流畅动画**: 优化的用户体验

### 🏗️ 架构
- **MVVM架构**: 清晰的代码分层
- **Clean Architecture**: 可维护的代码结构
- **依赖注入**: Hilt管理依赖关系
- **模块化设计**: 高内聚低耦合

### 🔧 工具链
- **KSP**: 高效的注解处理
- **Room**: 类型安全的数据库
- **Retrofit**: 强大的网络框架
- **Coroutines**: 异步编程支持

### 🛡️ 安全性
- **数据加密**: AES加密敏感数据
- **HTTPS传输**: 安全的网络通信
- **权限管理**: 精确的权限控制
- **代码混淆**: 发布版本代码保护

## 📞 支持与反馈

### 开发团队
- **架构设计**: 基于Android官方架构指南
- **代码规范**: 遵循Kotlin官方编码规范
- **安全标准**: 符合移动应用安全最佳实践

### 问题反馈
- 功能问题请查看对应模块文档
- 技术问题请参考最佳实践章节
- 新功能建议欢迎提出讨论

---

## 📝 文档更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0.0 | 2025-01-26 | 初始版本文档完整发布 |
| | | 包含所有核心模块的详细文档 |
| | | 项目架构和技术栈说明 |
| | | 开发规范和最佳实践指南 |

**持续更新中...** 📚