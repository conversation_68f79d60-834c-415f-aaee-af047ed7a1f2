<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="200dp"
    android:height="200dp"
    android:viewportWidth="200"
    android:viewportHeight="200">
    
    <!-- Circle ring -->
    <path
        android:pathData="M100,100m-80,0a80,80 0,1 1,160 0a80,80 0,1 1,-160 0"
        android:strokeWidth="3"
        android:strokeColor="#F8E9B5" />
    
    <!-- Crown -->
    <path
        android:pathData="M100,30 L110,40 L120,30 L130,40 L120,50 L80,50 L70,40 L80,30 L90,40 L100,30z"
        android:fillColor="#F8E9B5" />
    
    <!-- Letter L -->
    <path
        android:pathData="M80,70 L80,130 L120,130 L120,120 L90,120 L90,70 L80,70z"
        android:fillColor="#F8E9B5" />
    
    <!-- Decorative flourish -->
    <path
        android:pathData="M70,140 C80,150 120,150 130,140 C125,145 120,147 115,148 C110,149 90,149 85,148 C80,147 75,145 70,140z"
        android:fillColor="#F8E9B5" />
    
    <!-- Sparkle effects -->
    <path
        android:pathData="M130,70 L135,65 M70,130 L65,135 M150,100 L155,100 M45,100 L40,100"
        android:strokeWidth="2"
        android:strokeColor="#FFFFFF" />
</vector> 