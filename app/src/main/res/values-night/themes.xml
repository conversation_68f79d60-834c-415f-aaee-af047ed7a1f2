<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- 使用与日间模式相同的主题 -->
    <style name="Theme.Puxxi" parent="android:Theme.Material.NoActionBar">
        <!-- 主要颜色 -->
        <item name="android:colorPrimary">@color/primary</item>
        <item name="android:colorPrimaryDark">@color/primary_dark</item>
        <item name="android:colorAccent">@color/white</item>
        
        <!-- 文字颜色 -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/white_70</item>
        <item name="android:textColorHint">@color/white_50</item>
        
        <!-- 控件颜色 -->
        <item name="android:colorControlNormal">@color/white</item>
        <item name="android:colorControlActivated">@color/white</item>
        <item name="android:colorControlHighlight">@color/white_30</item>
        
        <!-- 输入框光标颜色 -->
        <item name="android:textCursorDrawable">@drawable/white_cursor</item>
        <item name="android:textSelectHandle">@drawable/text_select_handle_white</item>
        <item name="android:textSelectHandleLeft">@drawable/text_select_handle_left_white</item>
        <item name="android:textSelectHandleRight">@drawable/text_select_handle_right_white</item>
        
        <!-- 窗口设置 -->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/background</item>
        
        <!-- 状态栏和导航栏 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item> <!-- 状态栏文字为白色 -->
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">false</item> <!-- 导航栏图标为白色 -->
        
        <!-- 系统栏设置 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>
</resources>