package com.stargate.pxo.data.network.interceptor

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.common.util.AESUtil
import com.stargate.pxo.common.util.DeviceInfoUtil
import com.stargate.pxo.common.util.SPKey
import com.stargate.pxo.common.util.SPUtil
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import com.stargate.pxo.data.network.PuxxiUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 请求头拦截器
 * 负责将请求头信息添加到请求体的http_headers字段中，并加密请求体
 */
@Singleton
class HeaderInterceptor @Inject constructor(
    private val deviceInfoUtil: DeviceInfoUtil,
    private val gson: Gson
) : Interceptor {
    
    companion object {
        private const val TAG = "HeaderInterceptor"
    }

    // 加密密钥
    private var encryptionKey: String = ApiConstants.APP_API_DOMAIN.padEnd(32,'0')
    

    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 处理API请求
        val modifiedRequest = if (originalRequest.method == "POST" || 
                                originalRequest.method == "PUT" || 
                                originalRequest.method == "PATCH") {
            processRequestWithBody(originalRequest)
        } else {
            // 对于GET等请求，保持原样
            originalRequest
        }
        
        return chain.proceed(modifiedRequest)
    }
    
    /**
     * 处理带请求体的请求
     * 将请求头信息添加到请求体的http_headers字段中，并加密整个请求体
     */
    private fun processRequestWithBody(request: Request): Request {
        try {
            // 读取原始请求体
            val originalBody = request.body
            val buffer = Buffer()
            originalBody?.writeTo(buffer)
            val bodyString = buffer.readUtf8()
            
            // 打印原始请求体日志
            LogUtil.d(TAG, "Original request body\n-------------------------")
            LogUtil.json(TAG, bodyString)
            
            // 构建请求头信息
            val headers = buildHeaders()
            
            // 将请求体解析为Map
            val requestMap = if (bodyString.trim().startsWith("{")) {
                // 如果是JSON对象，直接解析
                val type = object : TypeToken<MutableMap<String, Any>>() {}.type
                gson.fromJson<MutableMap<String, Any>>(bodyString, type)
            } else if (bodyString.trim().startsWith("[")) {
                // 如果是JSON数组，包装为list字段
                val data: MutableMap<String, Any> = mutableMapOf()
                val type = object : TypeToken<List<Any>>() {}.type
                data["list"] = gson.fromJson<List<Any>>(bodyString, type)
                data
            } else {
                // 如果不是JSON，创建空Map
                mutableMapOf<String, Any>()
            }
            
            // 添加请求头信息到http_headers字段
            requestMap["http_headers"] = headers
            
            // 转换为JSON
            val jsonBody = gson.toJson(requestMap)
            
            // 记录请求日志
            LogUtil.d(TAG, "Request with headers\n-------------------------\n${request.url}---Request key---$encryptionKey")
            LogUtil.json(TAG, jsonBody)

            // 确定使用的解密密钥
            if (request.url.encodedPath.endsWith(PuxxiUrl.CONFIG_GETAPPCONFIGPOSTV2.substringAfterLast("/"))) {
                // 如果是APP配置接口，使用默认密钥
                encryptionKey
            } else {
                // 其他接口使用APP_SERVER_KEY
                encryptionKey = SPUtil.getString(SPKey.APP_SERVER_KEY).toString()
            }

            val encryptData = AESUtil.encrypt2Base64(
                jsonBody,
                encryptionKey,
            )

            val newRequestBody =
                encryptData?.toRequestBody(request.body?.contentType())

            // 打印加密后的请求体日志
            LogUtil.d(TAG, "Encrypted base64Encode2String request body\n-------------------------\n$encryptData")

            // 构建新的请求
            return request.newBuilder()
                .method(request.method, newRequestBody)
                .build()
                
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error processing request: ${e.message}")
            e.printStackTrace()
            // 发生异常时返回原始请求
            return request
        }
    }
    
    /**
     * 构建请求头信息
     */
    private fun buildHeaders(): Map<String, Any> {
        val headers = mutableMapOf<String, Any>()

        headers["lang"] = deviceInfoUtil.getSystemLanguage()
        headers["sys_lan"] = "en"
        headers["pkg"] = deviceInfoUtil.getPackageName()
        headers["ver"] = deviceInfoUtil.getVersionName()
        headers["device-id"] = deviceInfoUtil.getUniqueDeviceId()
        headers["model"] = deviceInfoUtil.getModel()
        headers["Authorization"] = if (UserManager.token?.isNotEmpty() == true) "Bearer ${UserManager.token}" else ""
        headers["is_anchor"] = "false"
        headers["platform"] = "Android"
        headers["is_field_confuse"] = "true"
        headers["rc_type"] = ""
        headers["google_ad_id"] = ""
        headers["content-type"] = "application/json"

        headers["attribution_sdk"] = ""
        headers["attribution_sdk_ver"] = ""
        headers["utm-source"] = ""
        headers["af_adgroup_id"] = ""
        headers["af_adset"] = ""
        headers["af_adset_id"] = ""
        headers["af_status"] = ""
        headers["af_agency"] = ""
        headers["af_channe"] = ""
        headers["campaign"] = ""
        headers["campaign_id"] = ""
        headers["fbInstallReferrer"] = ""

        return headers
    }
} 