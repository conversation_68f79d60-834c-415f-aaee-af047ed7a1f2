package com.stargate.pxo.data.network.interceptor

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.common.util.AESUtil
import com.stargate.pxo.common.util.SPKey
import com.stargate.pxo.common.util.SPUtil
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.model.BaseResponse
import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Request
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import java.net.SocketException
import java.net.SocketTimeoutException
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 响应拦截器
 * 负责解密响应数据，处理错误码和异常
 */
@Singleton
class ResponseInterceptor @Inject constructor(
    private val gson: Gson
) : Interceptor {
    
    companion object {
        private const val TAG = "ResponseInterceptor"
        
        // HTTP状态码
        private const val HTTP_OK = 200
        
        // 业务状态码
        private const val CODE_SUCCESS = 0
        private const val CODE_TOKEN_INVALID = 401
        private const val CODE_TOKEN_EXPIRED = 402
    }

    // 加密密钥
    private var encryptionKey: String = ApiConstants.APP_API_DOMAIN.padEnd(32,'0')
    
    // 是否记录日志
    private var enableLogging: Boolean = true
    
    // 忽略日志的URL列表
    private val ignoreLogList = mutableListOf<String>()
    

    /**
     * 设置是否启用日志
     */
    fun setEnableLogging(enable: Boolean) {
        enableLogging = enable
    }
    
    /**
     * 添加忽略日志的URL
     */
    fun addIgnoreLogUrl(url: String) {
        ignoreLogList.add(url)
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        try {
            val request = chain.request()
            val response = chain.proceed(request)
            
            // 处理HTTP状态码不为200或响应体为空的情况
            if (response.code != HTTP_OK || response.body == null) {
                LogUtil.e(TAG, "response:${response.code}:${response.message}")
                val errorResponse = createErrorResponse(response.code, "Server error")
                val responseBody = gson.toJson(errorResponse).toResponseBody("application/json".toMediaType())
                return response.newBuilder().body(responseBody).build()
            }
            
            // 读取响应体
            val originalBody = response.body?.string() ?: ""
            
            // 解密响应体
            val decryptedBody = decryptResponseBody(originalBody, request)
            
            // 记录响应日志
            logResponse(request, decryptedBody)
            
            // 解析响应数据
            val baseResponse = parseResponseBody(decryptedBody)
            
            // 处理业务状态码
            handleBusinessCode(baseResponse)
            
            // 构建新的响应体
            val newMediaType = "application/json; charset=utf-8".toMediaType()
            val newBody = decryptedBody.toResponseBody(newMediaType)
            
            // 构建新的响应
            return response.newBuilder()
                .body(newBody)
                .build()
                
        } catch (e: SocketTimeoutException) {
            // 处理超时异常
            LogUtil.e(TAG, "Socket timeout: ${e.message}")
            throw e
        } catch (e: SocketException) {
            // 处理连接异常
            LogUtil.e(TAG, "Socket exception: ${e.message}")
            throw e
        } catch (e: Exception) {
            // 处理其他异常
            LogUtil.e(TAG, "Error in response interceptor: ${e.message}")
            throw e
        }
    }
    
    /**
     * 解密响应体
     * 
     * @param bodyString 原始响应体字符串
     * @param request 请求对象
     * @return 解密后的响应体字符串
     */
    private fun decryptResponseBody(bodyString: String, request: Request): String {
        // 如果响应体已经是JSON格式，不需要解密
        if (bodyString.trim().startsWith("{") && bodyString.trim().endsWith("}")) {
            return bodyString
        }
        
        return try {
            // 获取当前请求的URL路径
            val requestUrl = request.url.encodedPath
            
            // 确定使用的解密密钥
            if (requestUrl.endsWith(PuxxiUrl.CONFIG_GETAPPCONFIGPOSTV2.substringAfterLast("/"))) {
                // 如果是APP配置接口，使用默认密钥
                encryptionKey
            } else {
                // 其他接口使用APP_SERVER_KEY
                encryptionKey = SPUtil.getString(SPKey.APP_SERVER_KEY).toString()
            }
            
            LogUtil.d("response\n-------------------------\n${request.url}")
            
            val deEncryptData = AESUtil.decryptBase64(
                bodyString,
                encryptionKey,
            )

            if (deEncryptData != null) {
                LogUtil.json(deEncryptData)
                deEncryptData
            } else {
                ""
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to decrypt response: ${e.message}")
            // 解密失败，返回错误响应
            gson.toJson(createErrorResponse(-1, "Decryption failed"))
        } as String
    }
    
    /**
     * 记录响应日志
     */
    private fun logResponse(request: Request, responseBody: String) {
        if (!enableLogging) return
        
        val url = request.url.encodedPath
        if (ignoreLogList.any { url.contains(it) }) return
        
        LogUtil.d(TAG, "response\n-------------------------\n$url")
        LogUtil.json(TAG, responseBody)
    }
    
    /**
     * 解析响应体
     */
    private fun parseResponseBody(responseBody: String): BaseResponse<Any> {
        return try {
            if (responseBody.isEmpty()) {
                createErrorResponse(-1, "Response body is empty")
            } else {
                gson.fromJson(responseBody, BaseResponse::class.java)
            }
        } catch (e: JsonSyntaxException) {
            LogUtil.e(TAG, "JSON syntax error: ${e.message}")
            createErrorResponse(-1, "JSON parsing error")
        } as BaseResponse<Any>
    }
    
    /**
     * 处理业务状态码
     */
    private fun handleBusinessCode(response: BaseResponse<Any>) {
        val code = response.code
        
        when (code) {
            CODE_TOKEN_INVALID, CODE_TOKEN_EXPIRED -> {
                // 处理token失效的情况，可以在这里发送事件通知用户登出
                LogUtil.w(TAG, "Token invalid or expired: $code")
            }
            CODE_SUCCESS -> {
                // 成功状态，可以在这里处理缓存等
            }
            else -> {
                // 其他状态码
                if (code != CODE_SUCCESS) {
                    LogUtil.w(TAG, "Business error: code=$code, message=${response.msg}")
                }
            }
        }
    }
    
    /**
     * 创建错误响应
     */
    private fun createErrorResponse(code: Int, message: String): BaseResponse<Any> {
        return BaseResponse(
            code = code,
            msg = message,
            data = null,
            isSuccess = false
        )
    }
} 