package com.stargate.pxo.data.network

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * API请求扩展函数
 * 简化ViewModel中的API调用
 */

/**
 * 执行API请求（使用Flow）
 * @param request API请求函数
 * @param onStart 请求开始时的回调
 * @param onSuccess 请求成功时的回调
 * @param onError 请求失败时的回调
 */
fun <T> ViewModel.apiRequest(
    request: () -> Flow<Resource<T>>,
    onStart: () -> Unit = { },
    onSuccess: (T) -> Unit,
    onError: (String, Int) -> Unit
) {
    viewModelScope.launch {
        request()
            .onStart { onStart() }
            .catch { e -> onError(e.message ?: "Unknown error", -1) }
            .collect { result ->
                when (result) {
                    is Resource.Success -> onSuccess(result.data)
                    is Resource.Error -> onError(result.message, result.code)
                    is Resource.Loading -> onStart()
                }
            }
    }
}

/**
 * 执行同步API请求
 */
fun <T> ViewModel.apiSyncRequest(
    request: suspend () -> Resource<T>,
    onStart: () -> Unit = { },
    onComplete: () -> Unit = { },
    onSuccess: (T) -> Unit,
    onError: (String, Int) -> Unit
) {
    viewModelScope.launch {
        try {
            onStart()
            val result = request()
            when (result) {
                is Resource.Success -> onSuccess(result.data)
                is Resource.Error -> onError(result.message, result.code)
                is Resource.Loading -> { /* 不处理 */ }
            }
        } catch (e: Exception) {
            onError(e.message ?: "Unknown error", -1)
        } finally {
            onComplete()
        }
    }
} 