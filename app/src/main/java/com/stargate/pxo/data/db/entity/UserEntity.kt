package com.stargate.pxo.data.db.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 用户信息数据库实体
 */
@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val userId: String,
    
    // 基本信息
    val nickname: String = "",
    val avatar: String = "",
    val avatarUrl: String = "",
    val gender: Int = 0,
    val age: Int = 0,
    val birthday: String = "",
    val country: String = "",
    
    // 账户信息
    val level: Int = 0,
    val isVip: Boolean = false,
    
    // 统计信息
    val followNum: Int = 0,
    val praiseNum: Int = 0,
    
    // 时间戳
    val createTime: Long = 0L,
    val lastUpdateTime: Long = 0L,
    
    // 额外信息
    val isBlock: Boolean = false,
    val isMultiple: Boolean = false,
    val userType: Int = 0,
    
    // 扩展字段，用于存储其他信息的JSON字符串
    val extraInfo: String = ""
) 