# BroadcasterInfoRepository 使用说明

## 概述

BroadcasterInfoRepository 是根据 BroadcasterInfo 接口创建的 Repository，遵循项目的Repository模式：

1. **Banner接口缓存** - 使用接口URL作为key进行缓存，24小时过期
2. **主播状态获取** - 仅从网络获取，不使用缓存
3. **主播墙数据** - 仅处理网络请求，数据库存储逻辑在ViewModel中处理

## 设计原则

- **Repository只处理网络请求**，不处理具体业务逻辑
- **使用 `.onEach` 进行数据处理**，遵循项目统一模式
- **具体业务逻辑在ViewModel中处理**，包括数据库存储、数据解析等

## 核心功能

### 1. Banner信息获取（带缓存）

```kotlin
// 在ViewModel中使用
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val broadcasterInfoRepository: BroadcasterInfoRepository
) : ViewModel() {

    fun loadBanners() {
        viewModelScope.launch {
            broadcasterInfoRepository.getBannerInfo()
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            updateState { copy(isLoading = true) }
                        }
                        is Resource.Success -> {
                            // Repository只返回网络数据，具体业务逻辑在这里处理
                            val banners = resource.data
                            processBannerData(banners) // 业务逻辑处理
                            updateState { copy(banners = banners, isLoading = false) }
                        }
                        is Resource.Error -> {
                            updateState { copy(error = resource.message, isLoading = false) }
                        }
                    }
                }
        }
    }

    private fun processBannerData(banners: List<BannerInfo>) {
        // 在ViewModel中处理具体的业务逻辑
        // 比如过滤、排序、格式化等
    }
}
```

**缓存策略：**
- 缓存Key: 使用接口URL (`PuxxiUrl.GAME_BANNER_INFO`)
- 缓存时间: 24小时
- 策略: `FetchStrategy.CACHE_FIRST` - 优先使用缓存

### 2. 主播状态获取（仅网络）

```kotlin
fun checkBroadcasterStatus(userIds: List<String>) {
    viewModelScope.launch {
        val requestData = mapOf("userIds" to userIds)
        
        broadcasterInfoRepository.getUserListOnlineStatusPostV3(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        // 处理主播状态数据
                        val statusData = resource.data
                        updateBroadcasterStatus(statusData)
                    }
                    is Resource.Error -> {
                        // 处理错误
                    }
                    is Resource.Loading -> {
                        // 显示加载状态
                    }
                }
            }
    }
}
```

**特点：**
- 每次都从网络获取最新状态
- 不使用任何缓存机制
- 适合实时性要求高的数据

### 3. 主播墙数据（数据库存储）

```kotlin
fun loadBroadcasterWall() {
    viewModelScope.launch {
        val requestData = mapOf(
            "page" to 1,
            "size" to 20,
            "category" to "all"
        )
        
        broadcasterInfoRepository.getBroadcasterWall(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        // 处理主播列表数据
                        val broadcasters = resource.data
                        updateBroadcasterList(broadcasters)
                    }
                    is Resource.Error -> {
                        // 如果网络失败，可能仍有数据库缓存数据
                    }
                    is Resource.Loading -> {
                        // 加载状态（如果数据库有数据，可能不会显示）
                    }
                }
            }
    }
}
```

**数据流程：**
1. 优先从数据库获取缓存数据
2. 同时发起网络请求获取最新数据
3. 网络数据返回后更新数据库
4. 使用userId作为主键，自动去重

## 数据库结构

### BroadcasterEntity 表结构

```sql
CREATE TABLE broadcasters (
    userId TEXT PRIMARY KEY,
    nickname TEXT NOT NULL,
    gender INTEGER NOT NULL,
    age INTEGER DEFAULT 0,
    status TEXT,
    avatar TEXT,
    avatarUrl TEXT,
    country TEXT DEFAULT '',
    isLive INTEGER,
    isFriend INTEGER DEFAULT 0,
    -- ... 其他字段
    createTime INTEGER NOT NULL,
    lastUpdateTime INTEGER NOT NULL
);
```

### CacheEntity 表结构

```sql
CREATE TABLE cache (
    key TEXT PRIMARY KEY,
    data TEXT NOT NULL,
    createTime INTEGER NOT NULL,
    expireTime INTEGER NOT NULL,
    dataType TEXT DEFAULT '',
    extra TEXT DEFAULT ''
);
```

## 依赖注入配置

确保在 Hilt 模块中提供必要的依赖：

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getInstance(context)
    }
    
    @Provides
    fun provideBroadcasterDao(database: AppDatabase): BroadcasterDao {
        return database.broadcasterDao()
    }
    
    @Provides
    fun provideCacheDao(database: AppDatabase): CacheDao {
        return database.cacheDao()
    }
}
```

## 数据转换

使用 BroadcasterMapper 进行数据转换：

```kotlin
// 网络模型 -> 数据库实体
val entity = BroadcasterMapper.fromNetworkModel(broadcasterItem)

// 数据库实体 -> 网络模型
val item = BroadcasterMapper.toNetworkModel(broadcasterEntity)

// 批量转换
val entities = BroadcasterMapper.fromNetworkModels(broadcasterItems)
val items = BroadcasterMapper.toNetworkModels(broadcasterEntities)
```

## 缓存管理

### 清理过期缓存

```kotlin
// 在Application或定期任务中调用
lifecycleScope.launch {
    broadcasterInfoRepository.cleanExpiredCache()
}
```

### 清理主播数据

```kotlin
// 用户登出时清理
lifecycleScope.launch {
    broadcasterInfoRepository.clearAllBroadcasters()
}
```

## 错误处理

Repository 提供了完善的错误处理：

```kotlin
broadcasterInfoRepository.getBroadcasterWall()
    .catch { exception ->
        // 处理异常
        LogUtil.e("HomeViewModel", "获取主播数据失败", exception)
        emit(Resource.Error("网络请求失败: ${exception.message}"))
    }
    .collect { resource ->
        // 处理结果
    }
```

## 性能优化

### 1. 缓存策略
- Banner数据：24小时缓存，减少网络请求
- 主播墙：数据库持久化，离线可用
- 主播状态：实时获取，保证准确性

### 2. 数据库优化
- 使用userId作为主键，确保数据唯一性
- 添加索引提升查询性能
- 定期清理过期数据

### 3. 网络优化
- 自动重试机制（2次重试，间隔1秒）
- 统一的错误处理
- 请求去重和防抖

## 注意事项

1. **数据库版本升级**：添加新实体后需要更新数据库版本号
2. **缓存过期**：定期清理过期缓存，避免存储空间浪费
3. **网络异常**：网络请求失败时优雅降级到缓存数据
4. **数据一致性**：确保网络数据和数据库数据的一致性
5. **内存管理**：大量数据时注意内存使用，考虑分页加载

## 扩展功能

可以基于当前架构扩展以下功能：

1. **分页加载**：支持主播列表分页
2. **搜索功能**：根据关键词搜索主播
3. **筛选功能**：按国家、性别等条件筛选
4. **收藏功能**：收藏喜欢的主播
5. **推荐算法**：基于用户行为推荐主播

这个Repository设计遵循了单一职责原则，提供了清晰的数据流和缓存策略，确保了良好的用户体验和系统性能。
