package com.stargate.pxo.data.network.storage

import android.content.Context
import com.stargate.pxo.data.network.storage.db.CacheDatabase
import dagger.Module
import dagger.Provides
import dagger.Binds
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 存储模块
 * 提供存储相关的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class StorageModule {
    
    /**
     * 提供DataRepository实现
     */
    @Binds
    @Singleton
    abstract fun bindDataRepository(impl: DefaultDataRepository): DataRepository
    
    @Module
    @InstallIn(SingletonComponent::class)
    object StorageProviders {
        /**
         * 提供缓存数据库实例
         */
        @Provides
        @Singleton
        fun provideCacheDatabase(@ApplicationContext context: Context): CacheDatabase {
            return CacheDatabase.getInstance(context)
        }
        
        /**
         * 提供缓存监控
         */
        @Provides
        @Singleton
        fun provideCacheMonitor(): CacheMonitor {
            return DefaultCacheMonitor()
        }
        
        /**
         * 提供内存存储实现
         */
        @Provides
        @Singleton
        fun provideMemoryDataStorage(cacheMonitor: CacheMonitor): MemoryDataStorage {
            return MemoryDataStorage(cacheMonitor)
        }
        
        /**
         * 提供Room数据库存储实现
         */
        @Provides
        @Singleton
        fun provideRoomDataStorage(database: CacheDatabase, cacheMonitor: CacheMonitor): RoomDataStorage {
            return RoomDataStorage(database, cacheMonitor)
        }
        
        /**
         * 提供多级存储实现
         */
        @Provides
        @Singleton
        fun provideMultiLevelDataStorage(
            memoryStorage: MemoryDataStorage,
            roomStorage: RoomDataStorage,
            cacheMonitor: CacheMonitor
        ): MultiLevelDataStorage {
            return MultiLevelDataStorage(memoryStorage, roomStorage, cacheMonitor)
        }
        
        /**
         * 提供存储管理器
         */
        @Provides
        @Singleton
        fun provideStorageManager(
            memoryStorage: MemoryDataStorage,
            roomStorage: RoomDataStorage,
            multiLevelStorage: MultiLevelDataStorage
        ): StorageManager {
            return StorageManager(memoryStorage, roomStorage, multiLevelStorage)
        }
    }
}