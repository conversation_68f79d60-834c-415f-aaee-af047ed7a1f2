package com.stargate.pxo.data.network.storage

import com.stargate.pxo.data.network.Resource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据仓库默认实现
 * 整合内存缓存和数据库存储，提供统一的数据访问接口
 */
@Singleton
class DefaultDataRepository @Inject constructor(
    private val memoryStorage: MemoryDataStorage,
    private val roomStorage: RoomDataStorage
) : DataRepository {
    
    override fun <T : Any> get(
        key: String,
        type: Class<T>,
        fetchStrategy: FetchStrategy
    ): Flow<Resource<T>> = flow {
        // 发送加载状态
        emit(Resource.loading())
        
        when (fetchStrategy) {
            FetchStrategy.CACHE_ONLY -> {
                // 只从缓存获取
                val data = getFromCache(key, type)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    emit(Resource.error("Cache not available for key: $key"))
                }
            }
            
            FetchStrategy.REMOTE_ONLY -> {
                // 只从远程获取，这里不实现具体逻辑，由executeWithCache处理
                emit(Resource.error("Remote only strategy requires executeWithCache method"))
            }
            
            FetchStrategy.CACHE_FIRST -> {
                // 优先从缓存获取
                val data = getFromCache(key, type)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    // 缓存不可用，由调用者处理远程获取
                    emit(Resource.error("Cache not available for key: $key"))
                }
            }
            
            FetchStrategy.REMOTE_FIRST, FetchStrategy.CACHE_AND_REMOTE -> {
                // 这两种策略需要远程请求，由executeWithCache处理
                val data = getFromCache(key, type)
                if (data != null && fetchStrategy == FetchStrategy.CACHE_AND_REMOTE) {
                    emit(Resource.success(data))
                }
                // 其余逻辑由executeWithCache处理
            }
        }
    }.flowOn(Dispatchers.IO)


    /**
     * 获取数据（指定类型）
     * 与getWithType类似但接受显式类型参数
     */
    override fun <T : Any> getWithExplicitType(
        key: String,
        fetchStrategy: FetchStrategy,
        type: Class<T>
    ): Flow<Resource<T>> = get(key, type, fetchStrategy)
    
    override suspend fun <T : Any> save(
        key: String,
        data: T,
        expireTime: Long,
        timeUnit: TimeUnit
    ) {
        val milliseconds = if (expireTime > 0) timeUnit.toMillis(expireTime) else 0
        
        // 同时保存到内存和数据库
        memoryStorage.saveData(key, data, milliseconds)
        roomStorage.saveData(key, data, milliseconds)
    }
    
    override suspend fun hasValidData(key: String): Boolean {
        // 检查内存或数据库中是否有有效数据
        return memoryStorage.hasValidData(key) || roomStorage.hasValidData(key)
    }
    
    override suspend fun removeData(key: String) {
        // 同时从内存和数据库中删除
        memoryStorage.removeData(key)
        roomStorage.removeData(key)
    }
    
    override suspend fun clear() {
        // 同时清除内存和数据库中的所有数据
        memoryStorage.clearAll()
        roomStorage.clearAll()
    }
    
    override suspend fun clearExpired() {
        // 同时清除内存和数据库中的过期数据
        memoryStorage.clearExpired()
        roomStorage.clearExpired()
    }
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : Any> executeWithCache(
        key: String,
        fetchStrategy: FetchStrategy,
        expireTime: Long,
        timeUnit: TimeUnit,
        networkCall: suspend () -> T
    ): Flow<Resource<T>> = flow {
        // 发送加载状态
        emit(Resource.loading())
        
        when (fetchStrategy) {
            FetchStrategy.CACHE_ONLY -> {
                // 只从缓存获取，使用Any::class.java作为类型，并在后续进行类型转换
                // 注意：这可能在某些情况下不安全，但为了向后兼容性，我们保留此实现
                val anyClass = Any::class.java as Class<T>
                val data = getFromCache(key, anyClass)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    emit(Resource.error("Cache not available for key: $key"))
                }
            }
            
            FetchStrategy.REMOTE_ONLY -> {
                // 只从远程获取
                try {
                    val data = networkCall()
                    save(key, data, expireTime, timeUnit)
                    emit(Resource.success(data))
                } catch (e: Exception) {
                    emit(Resource.error(e.message ?: "Unknown error", exception = e))
                }
            }
            
            FetchStrategy.CACHE_FIRST -> {
                // 优先从缓存获取，使用Any::class.java作为类型
                val anyClass = Any::class.java as Class<T>
                val data = getFromCache(key, anyClass)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    try {
                        val remoteData = networkCall()
                        save(key, remoteData, expireTime, timeUnit)
                        emit(Resource.success(remoteData))
                    } catch (e: Exception) {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                }
            }
            
            FetchStrategy.REMOTE_FIRST -> {
                // 优先从远程获取
                try {
                    val remoteData = networkCall()
                    save(key, remoteData, expireTime, timeUnit)
                    emit(Resource.success(remoteData))
                } catch (e: Exception) {
                    // 远程获取失败，尝试从缓存获取
                    val anyClass = Any::class.java as Class<T>
                    val data = getFromCache(key, anyClass)
                    if (data != null) {
                        emit(Resource.success(data))
                    } else {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                }
            }
            
            FetchStrategy.CACHE_AND_REMOTE -> {
                // 先返回缓存，然后请求远程更新
                val anyClass = Any::class.java as Class<T>
                val data = getFromCache(key, anyClass)
                if (data != null) {
                    emit(Resource.success(data))
                }
                
                try {
                    val remoteData = networkCall()
                    save(key, remoteData, expireTime, timeUnit)
                    // 如果缓存数据和远程数据不同，再次发送
                    if (data != remoteData) {
                        emit(Resource.success(remoteData))
                    }
                } catch (e: Exception) {
                    // 如果没有发送过缓存数据，则发送错误
                    if (data == null) {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                    // 否则忽略错误，因为已经发送了缓存数据
                }
            }
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 执行带缓存的网络请求（指定类型）
     */
    override fun <T : Any> executeWithCacheAndType(
        key: String,
        fetchStrategy: FetchStrategy,
        expireTime: Long,
        timeUnit: TimeUnit,
        type: Class<T>,
        networkCall: suspend () -> T
    ): Flow<Resource<T>> = flow {
        // 发送加载状态
        emit(Resource.loading())
        
        when (fetchStrategy) {
            FetchStrategy.CACHE_ONLY -> {
                // 只从缓存获取
                val data = getFromCache(key, type)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    emit(Resource.error("Cache not available for key: $key"))
                }
            }
            
            FetchStrategy.REMOTE_ONLY -> {
                // 只从远程获取
                try {
                    val data = networkCall()
                    save(key, data, expireTime, timeUnit)
                    emit(Resource.success(data))
                } catch (e: Exception) {
                    emit(Resource.error(e.message ?: "Unknown error", exception = e))
                }
            }
            
            FetchStrategy.CACHE_FIRST -> {
                // 优先从缓存获取
                val data = getFromCache(key, type)
                if (data != null) {
                    emit(Resource.success(data))
                } else {
                    try {
                        val remoteData = networkCall()
                        save(key, remoteData, expireTime, timeUnit)
                        emit(Resource.success(remoteData))
                    } catch (e: Exception) {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                }
            }
            
            FetchStrategy.REMOTE_FIRST -> {
                // 优先从远程获取
                try {
                    val remoteData = networkCall()
                    save(key, remoteData, expireTime, timeUnit)
                    emit(Resource.success(remoteData))
                } catch (e: Exception) {
                    // 远程获取失败，尝试从缓存获取
                    val data = getFromCache(key, type)
                    if (data != null) {
                        emit(Resource.success(data))
                    } else {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                }
            }
            
            FetchStrategy.CACHE_AND_REMOTE -> {
                // 先返回缓存，然后请求远程更新
                val data = getFromCache(key, type)
                if (data != null) {
                    emit(Resource.success(data))
                }
                
                try {
                    val remoteData = networkCall()
                    save(key, remoteData, expireTime, timeUnit)
                    // 如果缓存数据和远程数据不同，再次发送
                    if (data != remoteData) {
                        emit(Resource.success(remoteData))
                    }
                } catch (e: Exception) {
                    // 如果没有发送过缓存数据，则发送错误
                    if (data == null) {
                        emit(Resource.error(e.message ?: "Unknown error", exception = e))
                    }
                    // 否则忽略错误，因为已经发送了缓存数据
                }
            }
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * DSL风格的数据存储
     */
    override suspend fun <T : Any> store(data: T, config: StoreConfig.() -> Unit) {
        val storeConfig = StoreConfig().apply(config)
        save(storeConfig.key, data, storeConfig.expireTime, storeConfig.timeUnit)
    }
    
    /**
     * DSL风格的数据获取
     */
    override fun <T : Any> fetch(config: FetchConfig<T>.() -> Unit): Flow<Resource<T>> = flow {
        val fetchConfig = FetchConfig<T>().apply(config)
        
        if (fetchConfig.remote != null) {
            // 如果提供了远程数据源，使用executeWithCache
            executeWithCache(
                key = fetchConfig.key,
                fetchStrategy = fetchConfig.strategy,
                expireTime = 30,
                timeUnit = TimeUnit.MINUTES,
                networkCall = { fetchConfig.remote!!() ?: throw Exception("Remote data is null") }
            ).collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        fetchConfig.onSuccess?.invoke(resource.data)
                        emit(resource)
                    }
                    is Resource.Error -> {
                        fetchConfig.onError?.invoke(resource.message)
                        emit(resource)
                    }
                    is Resource.Loading -> {
                        emit(resource)
                    }
                }
            }
        } else {
            // 如果没有远程数据源，只从缓存获取
            val anyClass = Any::class.java as Class<T>
            get(fetchConfig.key, anyClass, fetchConfig.strategy).collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        fetchConfig.onSuccess?.invoke(resource.data)
                        emit(resource)
                    }
                    is Resource.Error -> {
                        fetchConfig.onError?.invoke(resource.message)
                        emit(resource)
                    }
                    is Resource.Loading -> {
                        emit(resource)
                    }
                }
            }
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 从缓存获取数据（先尝试内存，再尝试数据库）
     */
    private suspend fun <T : Any> getFromCache(key: String, type: Class<T>): T? {
        // 先尝试从内存获取
        val memoryData = memoryStorage.getData(key, type)
        
        if (memoryData != null) {
            return memoryData
        }
        
        // 如果内存中没有，尝试从数据库获取
        val dbData = roomStorage.getData(key, type)
        
        // 如果数据库中有数据，将其加载到内存中以加速后续访问
        if (dbData != null) {
            memoryStorage.saveData(key, dbData, 0)
        }
        
        return dbData
    }
} 