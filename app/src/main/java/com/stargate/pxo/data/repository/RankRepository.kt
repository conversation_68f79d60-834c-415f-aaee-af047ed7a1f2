package com.stargate.pxo.data.repository

import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.api.RankInterface
import com.stargate.pxo.data.network.client.EnhancedNetworkClient
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 排行榜Repository
 * 负责处理排行榜相关的网络请求
 */
@Singleton
class RankRepository @Inject constructor(
    private val rankInterface: RankInterface,
    private val networkClient: EnhancedNetworkClient
) {
    
    companion object {
        private const val TAG = "RankRepository"
    }
    
    /**
     * 获取魅力排行榜
     * 获取主播魅力值排行榜数据
     */
    fun getRankCharm(data: Map<String, Any> = mapOf()): Flow<Resource<List<Any>>> {
        return networkClient.execute(
            apiCall = {
                rankInterface.getRankCharm(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
    
    /**
     * 获取财富排行榜
     * 获取用户财富排行榜数据
     */
    fun getRankRich(data: Map<String, Any> = mapOf()): Flow<Resource<List<Any>>> {
        return networkClient.execute(
            apiCall = {
                rankInterface.getRankRich(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
    
    /**
     * 获取情侣排行榜
     * 获取情侣排行榜数据
     */
    fun getRankCouple(data: Map<String, Any> = mapOf()): Flow<Resource<List<Any>>> {
        return networkClient.execute(
            apiCall = {
                rankInterface.getRankCouple(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
}