# 网络拦截器系统

本模块提供了一套完整的网络拦截器系统，用于处理API请求和响应，包括请求头管理、加密解密、参数注入和日志记录。该系统是 Puxxi 应用安全通信的核心组件，确保所有网络通信都经过加密处理。

## 功能特点

- **请求头管理**：自动添加设备信息、应用信息和认证信息等通用请求头
- **请求体加密**：使用 AES/ECB/PKCS5Padding 算法加密请求体，提高数据传输安全性
- **响应体解密**：自动解密服务器返回的加密数据，支持多种密钥策略
- **参数注入**：自动向请求体中注入固定参数和动态参数
- **日志记录**：详细记录请求和响应信息，方便调试和问题排查
- **智能密钥选择**：根据不同API接口自动选择合适的加解密密钥
- **错误处理**：统一处理网络错误和业务错误，提供友好的错误信息

## 拦截器详细说明

### 1. HeaderInterceptor

`HeaderInterceptor` 负责处理请求头和请求体加密，是请求发送前的最后一道处理环节。

#### 核心功能

1. **请求头构建**：
   - 自动添加设备信息（设备ID、型号、语言等）
   - 添加应用信息（包名、版本号等）
   - 添加认证信息（Bearer Token）
   - 添加平台标识和内容类型

2. **请求体处理**：
   - 仅处理 POST、PUT、PATCH 等带请求体的请求
   - 将请求头信息添加到请求体的 `http_headers` 字段中
   - 使用 AES 加密整个请求体

3. **密钥管理**：
   - 根据请求路径选择合适的加密密钥
   - 对于配置接口使用默认密钥
   - 对于其他接口使用 `APP_SERVER_KEY` 存储的密钥

#### 请求处理流程

```
原始请求
↓
读取请求体
↓
构建请求头信息
↓
将请求头信息添加到请求体的 http_headers 字段
↓
使用 AES 加密整个请求体
↓
构建新的请求（替换请求体）
↓
发送请求
```

#### 加密实现细节

```kotlin
// 加密请求体
val encryptData = AESUtil.encrypt2Base64(
    jsonBody,
    encryptionKey,
)

// 创建新的请求体
val newRequestBody = encryptData?.toRequestBody(request.body?.contentType())

// 构建新的请求
return request.newBuilder()
    .method(request.method, newRequestBody)
    .build()
```

#### 固定参数注入

HeaderInterceptor 会自动向每个请求注入以下固定参数：

```kotlin
private fun buildHeaders(): Map<String, Any> {
    val headers = mutableMapOf<String, Any>()

    headers["lang"] = deviceInfoUtil.getSystemLanguage()
    headers["sys_lan"] = "en"
    headers["pkg"] = deviceInfoUtil.getPackageName()
    headers["ver"] = deviceInfoUtil.getVersionName()
    headers["device-id"] = deviceInfoUtil.getUniqueDeviceId()
    headers["model"] = deviceInfoUtil.getModel()
    headers["Authorization"] = if (UserManager.token?.isNotEmpty() == true) "Bearer ${UserManager.token}" else ""
    headers["is_anchor"] = "false"
    headers["platform"] = "Android"
    headers["is_field_confuse"] = "true"
    headers["rc_type"] = ""
    headers["google_ad_id"] = ""
    headers["content-type"] = "application/json"

    // 广告和归因相关参数
    headers["attribution_sdk"] = ""
    headers["attribution_sdk_ver"] = ""
    headers["utm-source"] = ""
    headers["af_adgroup_id"] = ""
    headers["af_adset"] = ""
    headers["af_adset_id"] = ""
    headers["af_status"] = ""
    headers["af_agency"] = ""
    headers["af_channe"] = ""
    headers["campaign"] = ""
    headers["campaign_id"] = ""
    headers["fbInstallReferrer"] = ""

    return headers
}
```

### 2. ResponseInterceptor

`ResponseInterceptor` 负责处理响应体解密和错误处理，是接收响应后的第一道处理环节。

#### 核心功能

1. **响应体解密**：
   - 检测响应体是否为加密数据
   - 根据请求路径选择合适的解密密钥
   - 使用 AES 解密响应体

2. **错误处理**：
   - 处理 HTTP 状态码错误
   - 处理业务状态码错误
   - 处理 Token 失效/过期情况

3. **日志记录**：
   - 记录原始响应体和解密后的响应体
   - 支持忽略特定 URL 的日志记录
   - 可配置是否启用日志记录

#### 响应处理流程

```
接收响应
↓
检查 HTTP 状态码
↓
读取响应体
↓
检测是否需要解密
↓
选择合适的解密密钥
↓
解密响应体
↓
解析响应数据
↓
处理业务状态码
↓
构建新的响应
↓
返回响应
```

#### 解密实现细节

```kotlin
private fun decryptResponseBody(bodyString: String, request: Request): String {
    // 如果响应体已经是JSON格式，不需要解密
    if (bodyString.trim().startsWith("{") && bodyString.trim().endsWith("}")) {
        return bodyString
    }
    
    return try {
        // 获取当前请求的URL路径
        val requestUrl = request.url.encodedPath
        
        // 确定使用的解密密钥
        if (requestUrl.endsWith(PuxxiUrl.CONFIG_GETAPPCONFIGPOSTV2.substringAfterLast("/"))) {
            // 如果是APP配置接口，使用默认密钥
            encryptionKey
        } else {
            // 其他接口使用APP_SERVER_KEY
            encryptionKey = SPUtil.getString(SPKey.APP_SERVER_KEY).toString()
        }
        
        // 使用AES解密Base64编码的数据
        val deEncryptData = AESUtil.decryptBase64(
            bodyString,
            encryptionKey,
        )

        deEncryptData ?: ""
    } catch (e: Exception) {
        // 解密失败，返回错误响应
        gson.toJson(createErrorResponse(-1, "Decryption failed"))
    }
}
```

#### 业务状态码处理

```kotlin
private fun handleBusinessCode(response: BaseResponse<Any>) {
    val code = response.code
    
    when (code) {
        CODE_TOKEN_INVALID, CODE_TOKEN_EXPIRED -> {
            // 处理token失效的情况
            LogUtil.w(TAG, "Token invalid or expired: $code")
        }
        CODE_SUCCESS -> {
            // 成功状态处理
        }
        else -> {
            // 其他状态码处理
            if (code != CODE_SUCCESS) {
                LogUtil.w(TAG, "Business error: code=$code, message=${response.msg}")
            }
        }
    }
}
```

## 加解密机制详解

### AES 加密算法

Puxxi 应用使用 AES/ECB/PKCS5Padding 算法进行数据加密，具体实现在 `AESUtil` 类中：

```kotlin
object AESUtil {
    private const val AES = "AES"
    private const val TRANSFORMATION = "AES/ECB/PKCS5Padding"
    private val CHARSET: Charset = Charsets.UTF_8

    // AES加密，返回Base64字符串
    fun encrypt2Base64(data: String, key: String): String? {
        val encrypted = encrypt(data.toByteArray(CHARSET), key.toByteArray(CHARSET)) ?: return null
        return Base64.encodeToString(encrypted, Base64.NO_WRAP)
    }

    // AES解密Base64字符串
    fun decryptBase64(data: String, key: String): String? {
        val decoded = Base64.decode(data, Base64.NO_WRAP)
        val decrypted = decrypt(decoded, key.toByteArray(CHARSET)) ?: return null
        return String(decrypted, CHARSET)
    }

    // 核心加密方法
    fun encrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        val cipher = Cipher.getInstance(TRANSFORMATION)
        val keySpec = SecretKeySpec(key, AES)
        cipher.init(Cipher.ENCRYPT_MODE, keySpec)
        return cipher.doFinal(data)
    }

    // 核心解密方法
    fun decrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        val cipher = Cipher.getInstance(TRANSFORMATION)
        val keySpec = SecretKeySpec(key, AES)
        cipher.init(Cipher.DECRYPT_MODE, keySpec)
        return cipher.doFinal(data)
    }
}
```

### 密钥管理策略

Puxxi 应用采用多级密钥管理策略：

1. **默认密钥**：
   - 使用 API 域名填充至 32 字节作为默认密钥
   - 用于首次启动时获取配置信息

2. **动态密钥**：
   - 从服务器获取的配置中提取密钥组件（abc_k2 和 abc_k3）
   - 组合并解码得到实际密钥
   - 存储在 SharedPreferences 中的 APP_SERVER_KEY 键下

3. **密钥选择逻辑**：
   - 配置接口使用默认密钥
   - 其他接口使用动态密钥

```kotlin
// 默认密钥生成
private var encryptionKey: String = ApiConstants.APP_API_DOMAIN.padEnd(32,'0')

// 动态密钥提取（在处理配置响应时）
val appKey = AESUtil.decodeBase64ToString(data.abc_k2.toString())
    .plus(AESUtil.decodeBase64ToString(data.abc_k3.toString()))
SPUtil.putString(SPKey.APP_SERVER_KEY, appKey)
```

## 参数注入机制

### 固定参数

HeaderInterceptor 会自动向每个请求注入以下固定参数：

1. **设备信息**：
   - `device-id`：设备唯一标识符
   - `model`：设备型号
   - `platform`：平台标识（Android）

2. **应用信息**：
   - `pkg`：应用包名
   - `ver`：应用版本号
   - `is_field_confuse`：字段混淆标志

3. **用户信息**：
   - `Authorization`：用户认证 Token
   - `is_anchor`：是否为主播

4. **语言设置**：
   - `lang`：用户语言设置
   - `sys_lan`：系统语言

5. **广告和归因参数**：
   - `attribution_sdk`：归因 SDK 标识
   - `utm-source`：流量来源
   - `campaign`：广告活动标识
   - 其他广告相关参数

### 动态参数

除了固定参数外，还可以在运行时动态添加参数：

```kotlin
// 示例：动态添加参数
val requestMap = gson.fromJson<MutableMap<String, Any>>(bodyString, type)
requestMap["timestamp"] = System.currentTimeMillis()
requestMap["session_id"] = SessionManager.getCurrentSessionId()
```

## 使用示例

### 1. 在 Dagger 模块中提供拦截器

```kotlin
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideHeaderInterceptor(
        deviceInfoUtil: DeviceInfoUtil,
        gson: Gson
    ): HeaderInterceptor {
        return HeaderInterceptor(deviceInfoUtil, gson)
    }
    
    @Provides
    @Singleton
    fun provideResponseInterceptor(
        gson: Gson
    ): ResponseInterceptor {
        return ResponseInterceptor(gson)
    }
    
    @Provides
    @Singleton
    @Named("networkInterceptors")
    fun provideNetworkInterceptors(
        headerInterceptor: HeaderInterceptor,
        responseInterceptor: ResponseInterceptor
    ): List<Interceptor> {
        return listOf(headerInterceptor, responseInterceptor)
    }
}
```

### 2. 在 OkHttpClient 中添加拦截器

```kotlin
@Provides
@Singleton
fun provideOkHttpClient(
    @Named("networkInterceptors") interceptors: List<Interceptor>
): OkHttpClient {
    val builder = OkHttpClient.Builder()
    
    // 添加所有拦截器
    interceptors.forEach { builder.addInterceptor(it) }
    
    return builder
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
}
```

### 3. 配置日志记录

```kotlin
// 配置响应拦截器的日志记录
responseInterceptor.setEnableLogging(BuildConfig.DEBUG)

// 添加忽略日志的URL
responseInterceptor.addIgnoreLogUrl("/api/v1/heartbeat")
responseInterceptor.addIgnoreLogUrl("/api/v1/analytics")
```

## 安全考量

1. **密钥保护**：
   - 密钥不直接硬编码在代码中
   - 密钥组件分散存储在服务器配置中
   - 实际密钥通过组合和解码生成

2. **传输安全**：
   - 所有请求和响应均经过 AES 加密
   - 使用 HTTPS 协议进行传输
   - 请求头信息嵌入请求体中，避免明文传输

3. **防篡改措施**：
   - 请求中包含设备唯一标识和应用版本信息
   - 响应中包含业务状态码和消息，用于验证响应的有效性

4. **错误处理**：
   - 加密/解密失败时提供友好的错误信息
   - 不暴露内部实现细节
   - 记录详细的错误日志，便于排查问题

## 调试技巧

1. **查看原始请求**：
   - 在日志中查找 `Original request body` 标记
   - 使用 JSON 格式化工具查看请求体内容

2. **查看加密请求**：
   - 在日志中查找 `Encrypted base64Encode2String request body` 标记
   - 这是实际发送到服务器的加密数据

3. **查看解密响应**：
   - 在日志中查找 `response` 标记后的 JSON 内容
   - 这是从服务器接收并解密后的响应数据

4. **密钥问题排查**：
   - 检查 `SPKey.APP_SERVER_KEY` 是否正确设置
   - 对于配置接口，确认默认密钥是否正确
   - 尝试手动解密响应数据进行验证

## 扩展与定制

拦截器系统设计为可扩展和可定制的，可以根据需要进行以下定制：

1. **添加新的请求头**：
   - 在 `HeaderInterceptor.buildHeaders()` 方法中添加新的请求头

2. **修改加密算法**：
   - 在 `AESUtil` 中修改 `TRANSFORMATION` 常量
   - 实现新的加密/解密方法

3. **添加新的拦截器**：
   - 实现 `Interceptor` 接口
   - 在 `NetworkModule` 中提供新的拦截器
   - 将新拦截器添加到拦截器列表中

4. **自定义错误处理**：
   - 在 `ResponseInterceptor.handleBusinessCode()` 方法中添加自定义错误处理逻辑
   - 可以发送事件通知其他组件处理特定错误 