package com.stargate.pxo.data.network.api

import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.model.AppConfigData
import com.stargate.pxo.data.network.model.BannerInfo
import com.stargate.pxo.data.network.model.BaseResponse
import com.stargate.pxo.data.network.model.BroadcasterItem
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface BroadcasterInfo {

    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getBannerInfo(
        @Url url: String = PuxxiUrl.GAME_BANNER_INFO,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<List<BannerInfo>>


    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getBroadcasterWall(
        @Url url: String = PuxxiUrl.BROADCASTER_WALL_SEARCH_V2,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<List<BroadcasterItem>>


    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getUserListOnlineStatusPostV3(
        @Url url: String = PuxxiUrl.USER_GETUSERLISTONLINESTATUSPOSTV3,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<Map<String, Any>>

    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getUserOnlineStatusPostV2(
        @Url url: String = PuxxiUrl.USER_GETUSERONLINESTATUSPOSTV2,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<String>



}