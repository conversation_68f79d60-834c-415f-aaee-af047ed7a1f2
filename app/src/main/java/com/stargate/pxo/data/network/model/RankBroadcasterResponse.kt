package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * 主播排行榜数据
 */
data class RankBroadcasterResponse(
    @SerializedName("monthName")
    val monthName: String,
    @SerializedName("sortNo")
    val sortNo: String,
    @SerializedName("rankData")
    val rankData: List<RankBroadcasterItem>
)

/**
 * 主播排行榜项
 */
data class RankBroadcasterItem(
    @SerializedName("sort")
    val sort: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("nickname")
    val nickname: String,
    @SerializedName("avatar")
    val avatar: String,
    @SerializedName("avatarMapPath")
    val avatarMapPath: String,
    @SerializedName("liveLevel")
    val liveLevel: Int? = null,
    @SerializedName("hidingLiveLevel")
    val hidingLiveLevel: Boolean? = null,
    @SerializedName("sets")
    val sets: UserDecorationSets? = null,
    @SerializedName("country")
    val country: String? = null,
    @SerializedName("countryCode")
    val countryCode: String? = null,
    @SerializedName("isOnline")
    val isOnline: Boolean? = null,
    @SerializedName("followCount")
    val followCount: Int? = null,
    @SerializedName("giftValue")
    val giftValue: Long? = null,
    @SerializedName("rankScore")
    val rankScore: Long? = null
)
