package com.stargate.pxo.data.mapper

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.stargate.pxo.data.db.entity.BroadcasterEntity
import com.stargate.pxo.data.network.model.BroadcasterItem
import com.stargate.pxo.data.network.model.VideoPaths

/**
 * 主播数据转换器
 * 用于在网络模型和数据库实体之间进行转换
 */
object BroadcasterMapper {
    
    private val gson = Gson()
    
    /**
     * 将网络模型转换为数据库实体
     */
    fun fromNetworkModel(item: BroadcasterItem): BroadcasterEntity {
        return BroadcasterEntity(
            userId = item.userId,
            nickname = item.nickname,
            gender = item.gender,
            age = item.age,
            status = item.status,
            about = item.about,
            
            // 头像相关
            avatar = item.avatar,
            avatarUrl = item.avatarUrl,
            avatarThumbUrl = item.avatarThumbUrl,
            avatar2 = item.avatar2,
            avatarMapPath = item.avatarMapPath,
            
            // 位置信息
            country = item.country,
            provinceName = item.provinceName,
            distance = item.distance,
            
            // 状态信息
            isLive = item.isLive,
            isFriend = item.isFriend,
            isMultiple = item.isMultiple,
            isNearbyOpen = item.isNearbyOpen,
            isSameLanguage = item.isSameLanguage,
            isSameProvince = item.isSameProvince,
            isSignBroadcaster = item.isSignBroadcaster,
            leave = item.leave,
            
            // 统计信息
            audienceNum = item.audienceNum,
            followingNum = item.followingNum,
            followNum = item.followNum,
            grade = item.grade,
            liveScore = item.liveScore,
            
            // 通话相关
            callCoins = item.callCoins,
            isCalled = item.isCalled,
            
            // 房间信息
            roomNo = item.roomNo,
            roomSessionNo = item.roomSessionNo,
            roomType = item.roomType,
            showRoomNo = item.showRoomNo,
            showRoomVersion = item.showRoomVersion,
            mgRoomNo = item.mgRoomNo,
            
            // 媒体信息
            background = item.background,
            cover = item.cover,
            liveCover = item.liveCover,
            mgRoomBackground = item.mgRoomBackground,
            
            // Token相关
            token = item.token,
            agoraToken = item.agoraToken,
            mgAgoraToken = item.mgAgoraToken,
            sessionNo = item.sessionNo,
            
            // 其他信息
            broadcasterType = item.broadcasterType,
            rtcType = item.rtcType,
            soundStatus = item.soundStatus,
            signPkgMin = item.signPkgMin,
            unit = item.unit,
            topOneImpression = item.topOneImpression,
            analysisLanguage = item.analysisLanguage,
            isHideCountry = item.isHideCountry,
            activityTagUrl = item.activityTagUrl,
            
            // 将List转换为JSON字符串存储
            applicableTags = item.applicableTags?.let { gson.toJson(it) },
            hobbies = item.hobbies?.let { gson.toJson(it) },
            broadcasterOtherLanguage = item.broadcasterOtherLanguage?.let { gson.toJson(it) },
            videoMapPaths = item.videoMapPaths?.let { gson.toJson(it) },
            videoPaths = item.videoPaths?.let { gson.toJson(it) },
            
            // 时间戳
            createTime = System.currentTimeMillis(),
            lastUpdateTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 将数据库实体转换为网络模型
     */
    fun toNetworkModel(entity: BroadcasterEntity): BroadcasterItem {
        return BroadcasterItem(
            userId = entity.userId,
            nickname = entity.nickname,
            gender = entity.gender,
            age = entity.age,
            status = entity.status,
            about = entity.about,
            
            // 头像相关
            avatar = entity.avatar,
            avatarUrl = entity.avatarUrl,
            avatarThumbUrl = entity.avatarThumbUrl,
            avatar2 = entity.avatar2,
            avatarMapPath = entity.avatarMapPath,
            
            // 位置信息
            country = entity.country,
            provinceName = entity.provinceName,
            distance = entity.distance,
            
            // 状态信息
            isLive = entity.isLive,
            isFriend = entity.isFriend,
            isMultiple = entity.isMultiple,
            isNearbyOpen = entity.isNearbyOpen,
            isSameLanguage = entity.isSameLanguage,
            isSameProvince = entity.isSameProvince,
            isSignBroadcaster = entity.isSignBroadcaster,
            leave = entity.leave,
            
            // 统计信息
            audienceNum = entity.audienceNum,
            followingNum = entity.followingNum,
            followNum = entity.followNum,
            grade = entity.grade,
            liveScore = entity.liveScore,
            
            // 通话相关
            callCoins = entity.callCoins,
            isCalled = entity.isCalled,
            
            // 房间信息
            roomNo = entity.roomNo,
            roomSessionNo = entity.roomSessionNo,
            roomType = entity.roomType,
            showRoomNo = entity.showRoomNo,
            showRoomVersion = entity.showRoomVersion,
            mgRoomNo = entity.mgRoomNo,
            
            // 媒体信息
            background = entity.background,
            cover = entity.cover,
            liveCover = entity.liveCover,
            mgRoomBackground = entity.mgRoomBackground,
            
            // Token相关
            token = entity.token,
            agoraToken = entity.agoraToken,
            mgAgoraToken = entity.mgAgoraToken,
            sessionNo = entity.sessionNo,
            
            // 其他信息
            broadcasterType = entity.broadcasterType,
            rtcType = entity.rtcType,
            soundStatus = entity.soundStatus,
            signPkgMin = entity.signPkgMin,
            unit = entity.unit,
            topOneImpression = entity.topOneImpression,
            analysisLanguage = entity.analysisLanguage,
            isHideCountry = entity.isHideCountry,
            activityTagUrl = entity.activityTagUrl,
            
            // 将JSON字符串转换回List
            applicableTags = entity.applicableTags?.let { 
                gson.fromJson(it, object : TypeToken<List<String?>>() {}.type) 
            },
            hobbies = entity.hobbies?.let { 
                gson.fromJson(it, object : TypeToken<List<String?>>() {}.type) 
            },
            broadcasterOtherLanguage = entity.broadcasterOtherLanguage?.let { 
                gson.fromJson(it, object : TypeToken<List<String?>>() {}.type) 
            },
            videoMapPaths = entity.videoMapPaths?.let { 
                gson.fromJson(it, object : TypeToken<List<String?>>() {}.type) 
            },
            videoPaths = entity.videoPaths?.let { 
                gson.fromJson(it, object : TypeToken<List<VideoPaths>>() {}.type) 
            }
        )
    }
    
    /**
     * 批量转换网络模型为数据库实体
     */
    fun fromNetworkModels(items: List<BroadcasterItem>): List<BroadcasterEntity> {
        return items.map { fromNetworkModel(it) }
    }
    
    /**
     * 批量转换数据库实体为网络模型
     */
    fun toNetworkModels(entities: List<BroadcasterEntity>): List<BroadcasterItem> {
        return entities.map { toNetworkModel(it) }
    }
}
