package com.stargate.pxo.data.repository

import com.google.gson.reflect.TypeToken
import com.stargate.pxo.common.util.AESUtil
import com.stargate.pxo.common.util.SPKey
import com.stargate.pxo.common.util.SPUtil
import com.stargate.pxo.common.util.SPUtil.gson
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.api.AppNetInterfaceInfo
import com.stargate.pxo.data.network.client.EnhancedNetworkClient
import com.stargate.pxo.data.network.model.AppConfigData
import com.stargate.pxo.data.network.model.LoginResponse
import com.stargate.pxo.data.network.model.StrategyResponse
import com.stargate.pxo.data.network.storage.CacheKeys
import com.stargate.pxo.data.network.storage.DataRepository
import com.stargate.pxo.data.network.storage.FetchStrategy
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.collections.get

/**
 * 应用配置Repository
 * 负责获取和管理应用配置信息
 */
@Singleton
class AppNetInterfaceRepository @Inject constructor(
    private val appNetInterfaceInfo: AppNetInterfaceInfo,
    private val networkClient: EnhancedNetworkClient,
    private val dataRepository: DataRepository
) {
    
    companion object {
        private const val TAG = "AppConfigRepository"
        const val CONFIG_CACHE_EXPIRE_TIME = 30L // 30分钟
        private const val STRATEGY_CACHE_EXPIRE_TIME = 60L // 60分钟
    }
    
    /**
     * 获取应用配置
     * 使用缓存优先策略，减少网络请求
     */
    fun getAppConfig(): Flow<Resource<AppConfigData>> {
        return networkClient.executeWithCacheTyped(
            cacheKey = CacheKeys.Config.appConfig(),
            fetchStrategy = FetchStrategy.CACHE_FIRST,
            expireTime = CONFIG_CACHE_EXPIRE_TIME,
            timeUnit = TimeUnit.MINUTES,
            type = AppConfigData::class.java,
            apiCall = {
                appNetInterfaceInfo.getAppConfig()
            }
        ).onEach { resource ->
            // 配置获取成功后，可以在这里做一些额外处理
            if (resource is Resource.Success) {
                processAppConfig(resource.data)
            }
        }
    }

    /**
     * 强制刷新应用配置
     * 忽略缓存，直接从服务器获取最新配置
     */
    fun refreshAppConfig(): Flow<Resource<AppConfigData>> {
        return networkClient.execute(
            apiCall = {
                appNetInterfaceInfo.getAppConfig()
            },
            retryCount = 2,
            retryDelay = 1000
        ).onEach { resource ->
            // 刷新成功后更新缓存
            if (resource is Resource.Success) {
                dataRepository.store(resource.data) {
                    key = CacheKeys.Config.appConfig()
                    expireTime = CONFIG_CACHE_EXPIRE_TIME
                    timeUnit = TimeUnit.MINUTES
                }
                processAppConfig(resource.data)
            }
        }
    }


    /**
     * 清除配置缓存
     */
    suspend fun clearConfigCache() {
        dataRepository.clear(CacheKeys.Config.appConfig())
    }


    /**
     * 获取策略配置
     * 使用网络优先策略，确保获取最新配置
     * 当网络请求失败时使用缓存数据
     */
    fun getStrategyPostV2(): Flow<Resource<StrategyResponse>> {
        return networkClient.executeWithCacheTyped(
            cacheKey = CacheKeys.forApi(PuxxiUrl.CONFIG_GETSTRATEGYPOSTV2),
            fetchStrategy = FetchStrategy.REMOTE_FIRST, // 网络优先，确保获取最新配置
            expireTime = STRATEGY_CACHE_EXPIRE_TIME,
            timeUnit = TimeUnit.MINUTES,
            type = StrategyResponse::class.java,
            apiCall = {
                appNetInterfaceInfo.getStrategyPostV2()
            }
        ).onEach { resource ->
            // 配置获取成功后处理数据
            if (resource is Resource.Success) {
                processStrategyPostV2(resource.data, dataRepository,STRATEGY_CACHE_EXPIRE_TIME)
            }
        }
    }

    /**
     * 用户登录
     */
    fun login(data: Map<String, Any>): Flow<Resource<LoginResponse>> {
        return networkClient.execute(
            apiCall = {
                appNetInterfaceInfo.login(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        ).onEach { resource ->
            if (resource is Resource.Success) {
                processLoginResponse(resource.data)
            }
        }
    }

}

/**
 * 处理应用配置
 * 在这里可以解析配置并应用到应用中
 */
private fun processAppConfig(data: AppConfigData) {
    val appKey = AESUtil.decodeBase64ToString(data.abc_k2.toString()).plus(AESUtil.decodeBase64ToString(data.abc_k3.toString()))

    SPUtil.putString(SPKey.APP_SERVER_KEY, appKey)

    LogUtil.d("appKey", appKey)

    val content = AESUtil.decodeBase64ToString(data.abc_k4.toString())
    val dataStr = content?.replace("\r\n", "")
    val datass = AESUtil.decryptBase64(dataStr.toString(), appKey)
    val dataMap: Map<String, Any> =
        gson.fromJson(datass, object : TypeToken<Map<String, Any>>() {}.type)
    LogUtil.d("content", dataMap.toString())

    if (dataMap["items"] != null) {
        val items = dataMap["items"] as ArrayList<Map<*, *>>

        // 定义需要处理的配置项键值对
        val configKeys = mapOf(
            SPKey.ATTRIBUTION_SDK to true,
            SPKey.APP_FB_CLIENT_TOKEN to true,
            SPKey.GLT to true,
            SPKey.RC_APP_KEY to true,
            SPKey.APP_FB_ID to true,
            SPKey.RC_AREA_CODE to true,
            SPKey.TRANSLATE_V2 to true,
            SPKey.GOOGLE_TRANSLATION_KEY to true,
        )

        // 统一处理所有配置项
        configKeys.forEach { (key, shouldSave) ->
            val configItem = items.firstOrNull { it["name"] == key }
            val value = (configItem?.get("data") ?: "") as String
            if (shouldSave) {
                SPUtil.putString(key, value)
                LogUtil.d(key, value)
            }
        }

        val listJsom = gson.toJson(items)
        LogUtil.d("listJsom", listJsom.toString())
    }
}


/**
 * 处理登录响应
 * 保存token和用户信息
 */
private fun processLoginResponse(data: LoginResponse) {
    // 保存 token 到加密的 SharedPreferences
    data.token.let { token ->
        // 敏感信息使用加密存储
        SPUtil.putEncryptedString(SPKey.ACCESS_TOKEN, token)
        SPUtil.putBoolean(SPKey.IS_LOGIN, true)
        SPUtil.putLong(SPKey.LAST_LOGIN_TIME, System.currentTimeMillis())
    }

    UserManager.isFirstRegister = data.isFirstRegister

    // 保存用户信息到 Room 数据库和加密的 SharedPreferences
    data.userInfo?.let { userInfo ->
        // 保存必要的快速访问信息（常用字段，非敏感）
        SPUtil.putString(SPKey.USER_ID, userInfo.userId)

        // 将完整用户信息序列化为JSON并加密存储
        SPUtil.putEncryptedObject("user_info_secure", userInfo)
        
        // 使用 UserManager 保存用户信息到数据库
        UserManager.saveUserInfo(userInfo)
    }
}


/**
 * 处理策略响应
 * 保存策略配置到缓存和SharedPreferences
 */
private suspend fun processStrategyPostV2(
    data: StrategyResponse,
    dataRepository: DataRepository,
    STRATEGY_CACHE_EXPIRE_TIME: Long
) {
    LogUtil.d("处理策略配置数据")
    
    try {
        // 将整个策略响应存储到缓存中
        dataRepository.store(data) {
            key = CacheKeys.forApi(PuxxiUrl.CONFIG_GETSTRATEGYPOSTV2)
            expireTime = STRATEGY_CACHE_EXPIRE_TIME
            timeUnit = TimeUnit.MINUTES
        }
        
        // 将整个策略配置对象序列化并存储到 SharedPreferences，方便快速访问
        data.let { config ->
            // 将完整配置对象序列化为JSON并存储
            SPUtil.putString(SPKey.STRATEGY_CONFIG, gson.toJson(config))

            // 只保存少数关键配置到单独的键值对中，用于快速访问
            SPUtil.putBoolean(SPKey.IS_MATCH_CALL_FREE, config.isMatchCallFree)
            SPUtil.putInt(SPKey.INIT_TAB, config.initTab)
            SPUtil.putBoolean(SPKey.IS_SHOW_MATCH_GENDER, config.isShowMatchGender)
            SPUtil.putBoolean(SPKey.IS_REVIEW_PKG, config.isReviewPkg)
            SPUtil.putBoolean(SPKey.IS_MASK_OPEN, config.isMaskOpen)
            SPUtil.putBoolean(SPKey.IS_AUTO_ACCEPT, config.isAutoAccept)
            SPUtil.putBoolean(SPKey.SHOW_MEET, config.showMeet)
            SPUtil.putBoolean(SPKey.IS_OPEN_FLASH_CHAT, config.isOpenFlashChat)
            SPUtil.putBoolean(SPKey.IS_SHOW_MATCH, config.isShowMatch)

            // 保存时间戳
            if (config.timestamp.isNotEmpty()) {
                SPUtil.putString(SPKey.STRATEGY_TIMESTAMP, config.timestamp)
            }
        }
        
        LogUtil.d("策略配置数据处理完成，已存储到缓存和SharedPreferences")
    } catch (e: Exception) {
        LogUtil.e("处理策略配置数据异常: ${e.message}")
        e.printStackTrace()
    }
}
