package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

data class BannerInfo(
    @SerializedName("type")
    val type: Int,

    @SerializedName("pic")
    val pic: String,

    @SerializedName("jumpUrl")
    val jumpUrl: String?,

    @SerializedName("bizType")
    val bizType: String,

    @SerializedName("position")
    val position: List<Int>,

    @SerializedName("templateName")
    val templateName: String?,

    @SerializedName("bannerPosition")
    val bannerPosition: Int,

    @SerializedName("broadcasterId")
    val broadcasterId: String,
)



