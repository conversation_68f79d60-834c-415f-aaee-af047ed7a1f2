package com.stargate.pxo.data.db.dao

import androidx.room.*
import com.stargate.pxo.data.db.entity.BroadcasterEntity
import kotlinx.coroutines.flow.Flow

/**
 * 主播信息数据访问对象
 */
@Dao
interface BroadcasterDao {
    
    /**
     * 根据用户ID获取主播信息
     */
    @Query("SELECT * FROM broadcasters WHERE userId = :userId")
    suspend fun getBroadcasterById(userId: String): BroadcasterEntity?
    
    /**
     * 获取所有主播信息
     */
    @Query("SELECT * FROM broadcasters ORDER BY lastUpdateTime DESC")
    suspend fun getAllBroadcasters(): List<BroadcasterEntity>
    
    /**
     * 获取所有主播信息（Flow）
     */
    @Query("SELECT * FROM broadcasters ORDER BY lastUpdateTime DESC")
    fun getAllBroadcastersFlow(): Flow<List<BroadcasterEntity>>
    
    /**
     * 根据国家获取主播列表
     */
    @Query("SELECT * FROM broadcasters WHERE country = :country ORDER BY lastUpdateTime DESC")
    suspend fun getBroadcastersByCountry(country: String): List<BroadcasterEntity>
    
    /**
     * 根据在线状态获取主播列表
     */
    @Query("SELECT * FROM broadcasters WHERE isLive = :isLive ORDER BY lastUpdateTime DESC")
    suspend fun getBroadcastersByLiveStatus(isLive: Boolean): List<BroadcasterEntity>
    
    /**
     * 根据性别获取主播列表
     */
    @Query("SELECT * FROM broadcasters WHERE gender = :gender ORDER BY lastUpdateTime DESC")
    suspend fun getBroadcastersByGender(gender: Int): List<BroadcasterEntity>
    
    /**
     * 分页获取主播列表
     */
    @Query("SELECT * FROM broadcasters ORDER BY lastUpdateTime DESC LIMIT :limit OFFSET :offset")
    suspend fun getBroadcastersWithPaging(limit: Int, offset: Int): List<BroadcasterEntity>
    
    /**
     * 搜索主播（根据昵称）
     */
    @Query("SELECT * FROM broadcasters WHERE nickname LIKE '%' || :query || '%' ORDER BY lastUpdateTime DESC")
    suspend fun searchBroadcasters(query: String): List<BroadcasterEntity>
    
    /**
     * 插入单个主播信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBroadcaster(broadcaster: BroadcasterEntity)
    
    /**
     * 批量插入主播信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBroadcasters(broadcasters: List<BroadcasterEntity>)
    
    /**
     * 更新主播信息
     */
    @Update
    suspend fun updateBroadcaster(broadcaster: BroadcasterEntity)
    
    /**
     * 批量更新主播信息
     */
    @Update
    suspend fun updateBroadcasters(broadcasters: List<BroadcasterEntity>)
    
    /**
     * 删除主播信息
     */
    @Delete
    suspend fun deleteBroadcaster(broadcaster: BroadcasterEntity)
    
    /**
     * 根据用户ID删除主播信息
     */
    @Query("DELETE FROM broadcasters WHERE userId = :userId")
    suspend fun deleteBroadcasterById(userId: String)
    
    /**
     * 清空所有主播信息
     */
    @Query("DELETE FROM broadcasters")
    suspend fun deleteAllBroadcasters()
    
    /**
     * 删除过期的主播信息（超过指定时间）
     */
    @Query("DELETE FROM broadcasters WHERE lastUpdateTime < :expireTime")
    suspend fun deleteExpiredBroadcasters(expireTime: Long)
    
    /**
     * 获取主播总数
     */
    @Query("SELECT COUNT(*) FROM broadcasters")
    suspend fun getBroadcasterCount(): Int
    
    /**
     * 获取在线主播数量
     */
    @Query("SELECT COUNT(*) FROM broadcasters WHERE isLive = 1")
    suspend fun getOnlineBroadcasterCount(): Int
    
    /**
     * 更新主播状态
     */
    @Query("UPDATE broadcasters SET status = :status, lastUpdateTime = :updateTime WHERE userId = :userId")
    suspend fun updateBroadcasterStatus(userId: String, status: String?, updateTime: Long = System.currentTimeMillis())
    
    /**
     * 批量更新主播状态
     */
    @Query("UPDATE broadcasters SET status = :status, lastUpdateTime = :updateTime WHERE userId IN (:userIds)")
    suspend fun updateBroadcastersStatus(userIds: List<String>, status: String?, updateTime: Long = System.currentTimeMillis())
    
    /**
     * 更新主播在线状态
     */
    @Query("UPDATE broadcasters SET isLive = :isLive, lastUpdateTime = :updateTime WHERE userId = :userId")
    suspend fun updateBroadcasterLiveStatus(userId: String, isLive: Boolean, updateTime: Long = System.currentTimeMillis())
}
