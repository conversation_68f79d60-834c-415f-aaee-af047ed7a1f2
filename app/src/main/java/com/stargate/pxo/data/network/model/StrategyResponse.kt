package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * 策略响应数据模型
 */
data class StrategyResponse(

    @SerializedName("isMatchCallFree")
    val isMatchCallFree: Boolean = false,

    @SerializedName("initTab")
    val initTab: Int = 0,

    @SerializedName("isShowMatchGender")
    val isShowMatchGender: Boolean = false,

    @SerializedName("genderMatchCoin")
    val genderMatchCoin: GenderMatchCoin? = null,

    @SerializedName("isReviewPkg")
    val isReviewPkg: Boolean = false,

    @SerializedName("isShowLP")
    val isShowLP: Boolean = false,

    @SerializedName("lpDiscount")
    val lpDiscount: Int = 0,

    @SerializedName("lpPromotionDiscount")
    val lpPromotionDiscount: Int = 0,

    @SerializedName("payChannels")
    val payChannels: List<String> = emptyList(),

    @SerializedName("isMaskOpen")
    val isMaskOpen: Boolean = false,

    @SerializedName("isShowBroadcasterRank")
    val isShowBroadcasterRank: Boolean = false,

    @SerializedName("payScriptUserCoins")
    val payScriptUserCoins: Int = 0,

    @SerializedName("payScriptTriggerSecond")
    val payScriptTriggerSecond: Int = 0,

    @SerializedName("fakeBroadcasterPopupSecond")
    val fakeBroadcasterPopupSecond: Int = 0,

    @SerializedName("isAutoAccept")
    val isAutoAccept: Boolean = false,

    @SerializedName("showMeet")
    val showMeet: Boolean = false,

    @SerializedName("broadcasterWallTags")
    val broadcasterWallTags: List<String> = emptyList(),

    @SerializedName("liveWallTagList")
    val liveWallTagList: List<String> = emptyList(),

    @SerializedName("tabType")
    val tabType: Int = 0,

    @SerializedName("isOpenBroacasterInvitation")
    val isOpenBroacasterInvitation: Boolean = false,

    @SerializedName("isOpenFlashChat")
    val isOpenFlashChat: Boolean = false,

    @SerializedName("videoStreamCategory")
    val videoStreamCategory: List<String> = emptyList(),

    @SerializedName("flashChatConfig")
    val flashChatConfig: FlashChatConfig? = null,

    @SerializedName("isShowMatch")
    val isShowMatch: Boolean = false,

    @SerializedName("isNewTppUsable")
    val isNewTppUsable: Boolean = false,

    @SerializedName("userInvitation")
    val userInvitation: UserInvitation? = null,

    @SerializedName("topOfficialUserIds")
    val topOfficialUserIds: List<String> = emptyList(),

    @SerializedName("reviewOfficialBlacklistUserIds")
    val reviewOfficialBlacklistUserIds: List<String> = emptyList(),

    @SerializedName("officialBlacklistUserIds")
    val officialBlacklistUserIds: List<String> = emptyList(),

    @SerializedName("imIncentiveBlacklistUserIds")
    val imIncentiveBlacklistUserIds: List<String> = emptyList(),

    @SerializedName("broadcasterFollowOfficialUserIds")
    val broadcasterFollowOfficialUserIds: List<String> = emptyList(),

    @SerializedName("isDisplayNotDisturbCall")
    val isDisplayNotDisturbCall: Boolean = false,

    @SerializedName("isDisplayNotDisturbIm")
    val isDisplayNotDisturbIm: Boolean = false,

    @SerializedName("imSessionBalance")
    val imSessionBalance: Int = 0,

    @SerializedName("isShowFlowInfo")
    val isShowFlowInfo: Boolean = false,

    @SerializedName("isShowDeletedButton")
    val isShowDeletedButton: Boolean = false,

    @SerializedName("isShowLiveTab")
    val isShowLiveTab: Boolean = false,

    @SerializedName("minLiveUserLevel")
    val minLiveUserLevel: Int = 0,

    @SerializedName("broadcasterWallTagList")
    val broadcasterWallTagList: List<BroadcasterWallTag> = emptyList(),

    @SerializedName("freeUserCallStaySecond")
    val freeUserCallStaySecond: String = "",

    @SerializedName("freeUserImStaySecond")
    val freeUserImStaySecond: String = "",

    @SerializedName("rechargeUserCallStaySecond")
    val rechargeUserCallStaySecond: String = "",

    @SerializedName("rechargeUserImStaySecond")
    val rechargeUserImStaySecond: String = "",

    @SerializedName("isRandomUploadPaidEvents")
    val isRandomUploadPaidEvents: Boolean = false,

    @SerializedName("isSwitchIMLimit")
    val isSwitchIMLimit: Boolean = false,

    @SerializedName("isSwitchOneKeyFollow")
    val isSwitchOneKeyFollow: Boolean = false,

    @SerializedName("isSwitchIMIncentive")
    val isSwitchIMIncentive: Boolean = false,

    @SerializedName("isSwitchClub")
    val isSwitchClub: Boolean = false,

    @SerializedName("isShowRookieGuide")
    val isShowRookieGuide: Boolean = false,

    @SerializedName("isSwitchStrongGuide")
    val isSwitchStrongGuide: Boolean = false,

    @SerializedName("isSwitchAloneGuide")
    val isSwitchAloneGuide: Boolean = false,

    @SerializedName("isCallRearCamera")
    val isCallRearCamera: Boolean = false,

    @SerializedName("isCallCameraClose")
    val isCallCameraClose: Boolean = false,

    @SerializedName("isShowAutoTranslate")
    val isShowAutoTranslate: Boolean = false,

    @SerializedName("isSilence")
    val isSilence: Boolean = false,

    @SerializedName("isRearCamera")
    val isRearCamera: Boolean = false,

    @SerializedName("isCloseCamera")
    val isCloseCamera: Boolean = false,

    @SerializedName("isSwitchInstruct")
    val isSwitchInstruct: Boolean = false,

    @SerializedName("isForceEvaluationInstruct")
    val isForceEvaluationInstruct: Boolean = false,

    @SerializedName("isSwitchExtraCategory")
    val isSwitchExtraCategory: Boolean = false,

    @SerializedName("isSwitchMultipleCall")
    val isSwitchMultipleCall: Boolean = false,

    @SerializedName("timestamp")
    val timestamp: String = "",

    @SerializedName("sayHiMaxCount")
    val sayHiMaxCount: Int = 0,

    @SerializedName("sayHiQuickPhrases")
    val sayHiQuickPhrases: List<String> = emptyList(),

    @SerializedName("userServiceAccountId")
    val userServiceAccountId: String = "",

    @SerializedName("broadcasterWallRegions")
    val broadcasterWallRegions: List<String> = emptyList(),

    @SerializedName("userMultipleLevel")
    val userMultipleLevel: Int = 0,

    @SerializedName("isReportFB")
    val isReportFB: Boolean = false,

    @SerializedName("isEnableGuardian")
    val isEnableGuardian: Boolean = false,

    @SerializedName("isEnableCallCard")
    val isEnableCallCard: Boolean = false,

    @SerializedName("isOpenSpeechToText")
    val isOpenSpeechToText: Boolean = false,

    @SerializedName("voiceToTextConfig")
    val voiceToTextConfig: VoiceToTextConfig? = null,

    @SerializedName("isEnableGroupSend")
    val isEnableGroupSend: Boolean = false,

    @SerializedName("supportShowRoom")
    val supportShowRoom: Boolean = false,

    @SerializedName("newRelationMsgSizeLimit")
    val newRelationMsgSizeLimit: Int = 0,

    @SerializedName("unansweredGreetingExpireTTLHour")
    val unansweredGreetingExpireTTLHour: Int = 0,

    @SerializedName("isOpenFlashChatOnRole")
    val isOpenFlashChatOnRole: Boolean = false,

    @SerializedName("broadcasterOnlineButton")
    val broadcasterOnlineButton: String = "",

    @SerializedName("indiaWallCallButtonUI")
    val indiaWallCallButtonUI: String = "",

    @SerializedName("indiaWallLowCallUI")
    val indiaWallLowCallUI: String = "",

    @SerializedName("indiaRecommendShow")
    val indiaRecommendShow: String = "",

    @SerializedName("indianWallUnlock")
    val indianWallUnlock: String = "",

    @SerializedName("callingSeconds")
    val callingSeconds: Int = 0,

    @SerializedName("isGuideInMode")
    val isGuideInMode: Boolean = false,

    @SerializedName("appPraiseSwitch")
    val appPraiseSwitch: Boolean = false,

    @SerializedName("liveComboLimitTime")
    val liveComboLimitTime: Int = 0,

    @SerializedName("hideCountryFromLive")
    val hideCountryFromLive: Boolean = false,

    @SerializedName("highLightUnreadOfficialUserIds")
    val highLightUnreadOfficialUserIds: List<String> = emptyList(),

    @SerializedName("videoCallConfig")
    val videoCallConfig: VideoCallConfig? = null,

    @SerializedName("liveLevelEquityH5Url")
    val liveLevelEquityH5Url: String = "",

    @SerializedName("activityListH5Url")
    val activityListH5Url: String = "",

    @SerializedName("userSvipEquityH5Url")
    val userSvipEquityH5Url: String = "",

    @SerializedName("blindBoxH5Url")
    val blindBoxH5Url: String = "",

    @SerializedName("isShowWeeklyCard")
    val isShowWeeklyCard: Boolean = false,

    @SerializedName("multiGuestConfig")
    val multiGuestConfig: Map<String, Any> = emptyMap(),

    @SerializedName("isSwitchSubscription")
    val isSwitchSubscription: Boolean = false,

    @SerializedName("openLiveUnderReview")
    val openLiveUnderReview: Boolean = false,

    @SerializedName("h5TaskUrl")
    val h5TaskUrl: String = "",

    @SerializedName("isCR")
    val isCR: Boolean = false,

    @SerializedName("userLiveCountryTagConfigList")
    val userLiveCountryTagConfigList: List<Any> = emptyList(),

    @SerializedName("isNo1V1")
    val isNo1V1: Boolean = false,

    @SerializedName("comboCountdown")
    val comboCountdown: Int = 0,

    @SerializedName("popByGroupCoins")
    val popByGroupCoins: Int = 0,

    @SerializedName("globalNoticeChatRoomConfig")
    val globalNoticeChatRoomConfig: GlobalNoticeChatRoomConfig? = null,

    @SerializedName("vipServiceAccount")
    val vipServiceAccount: String = "",

    @SerializedName("imSessionBroadcasterIds")
    val imSessionBroadcasterIds: List<String> = emptyList()
)


/**
 * 性别匹配币配置
 */
data class GenderMatchCoin(
    @SerializedName("maleCoins")
    val maleCoins: Int = 0,

    @SerializedName("femaleCoins")
    val femaleCoins: Int = 0,

    @SerializedName("bothCoins")
    val bothCoins: Int = 0,

    @SerializedName("vipGoddessCoins")
    val vipGoddessCoins: Int = 0,

    @SerializedName("goddessCoins")
    val goddessCoins: Int = 0
)

/**
 * 闪聊配置
 */
data class FlashChatConfig(
    @SerializedName("isSwitch")
    val isSwitch: Boolean = false,

    @SerializedName("isFreeCall")
    val isFreeCall: Boolean = false,

    @SerializedName("residueFreeCallTimes")
    val residueFreeCallTimes: Int = 0
)

/**
 * 用户邀请配置
 */
data class UserInvitation(
    @SerializedName("tipsTitle")
    val tipsTitle: String = "",

    @SerializedName("tipsContent")
    val tipsContent: String = "",

    @SerializedName("popUpTitle")
    val popUpTitle: String = "",

    @SerializedName("popUpContent")
    val popUpContent: String = "",

    @SerializedName("popUpBottom")
    val popUpBottom: String = "",

    @SerializedName("shareContent")
    val shareContent: String = ""
)

/**
 * 广播员墙标签
 */
data class BroadcasterWallTag(
    @SerializedName("tagName")
    val tagName: String = "",

    @SerializedName("subTagList")
    val subTagList: List<String> = emptyList(),

    @SerializedName("showTagName")
    val showTagName: String = "",

    @SerializedName("subTagInitIndex")
    val subTagInitIndex: Int = 0
)

/**
 * 语音转文本配置
 */
data class VoiceToTextConfig(
    @SerializedName("voiceToTextSwitch")
    val voiceToTextSwitch: Boolean = false,

    @SerializedName("voiceToTextUnitPrice")
    val voiceToTextUnitPrice: Int = 0
)

/**
 * 视频通话配置
 */
data class VideoCallConfig(
    @SerializedName("callNoticeContent")
    val callNoticeContent: List<List<String>> = emptyList(),

    @SerializedName("firstSendDelaySecond")
    val firstSendDelaySecond: Int = 0,

    @SerializedName("sendDelayMinSecond")
    val sendDelayMinSecond: Int = 0,

    @SerializedName("sendDelayMaxSecond")
    val sendDelayMaxSecond: Int = 0
)

/**
 * 全局通知聊天室配置
 */
data class GlobalNoticeChatRoomConfig(
    @SerializedName("broadcasterChatRoom")
    val broadcasterChatRoom: ChatRoom? = null,

    @SerializedName("userChatRoom")
    val userChatRoom: ChatRoom? = null
)

/**
 * 聊天室配置
 */
data class ChatRoom(
    @SerializedName("roomNo")
    val roomNo: String = ""
)