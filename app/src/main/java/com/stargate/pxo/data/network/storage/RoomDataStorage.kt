package com.stargate.pxo.data.network.storage

import com.google.gson.Gson
import com.stargate.pxo.data.network.storage.db.CacheDatabase
import com.stargate.pxo.data.network.storage.db.CacheEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Room数据库存储实现
 * 使用Room数据库持久化存储数据
 */
@Singleton
class RoomDataStorage @Inject constructor(
    private val database: CacheDatabase,
    private val cacheMonitor: CacheMonitor
) : DataStorage {
    
    private val gson = Gson()
    private val dao = database.cacheDao()
    
    override suspend fun <T> saveData(key: String, data: T, expireTime: Long) = withContext(Dispatchers.IO) {
        val json = gson.toJson(data)
        val dataType = data?.let { it::class.java.name } ?: "java.lang.Object"
        
        val entity = CacheEntity(
            key = key,
            data = json,
            dataType = dataType,
            createTime = System.currentTimeMillis(),
            expireTime = expireTime
        )
        
        dao.insert(entity)
        cacheMonitor.onCacheSaved(key, expireTime)
    }
    
    override suspend fun <T : Any> getData(key: String, type: Class<T>): T? = withContext(Dispatchers.IO) {
        val entity = dao.getByKey(key)
        
        if (entity == null || entity.isExpired()) {
            if (entity?.isExpired() == true) {
                // 如果数据已过期，删除它
                dao.deleteByKey(key)
                cacheMonitor.onCacheExpired(key)
            }
            cacheMonitor.onCacheAccessed(key, false)
            null
        } else {
            try {
                cacheMonitor.onCacheAccessed(key, true)
                // 使用显式类型转换确保类型安全
                val jsonData = entity.data
                gson.fromJson(jsonData, type)
            } catch (e: Exception) {
                cacheMonitor.onCacheAccessed(key, false)
                null
            }
        }
    }
    
    override suspend fun hasValidData(key: String): Boolean = withContext(Dispatchers.IO) {
        val entity = dao.getByKey(key)
        val isValid = entity != null && !entity.isExpired()
        cacheMonitor.onCacheAccessed(key, isValid)
        isValid
    }
    
    override suspend fun removeData(key: String): Unit = withContext(Dispatchers.IO) {
        dao.deleteByKey(key)
        cacheMonitor.onCacheRemoved(key)
    }
    
    override suspend fun clearAll() = withContext(Dispatchers.IO) {
        // 获取所有键以便通知监控器
        val keys = dao.getAllKeys()
        dao.deleteAll()
        keys.forEach { key ->
            cacheMonitor.onCacheRemoved(key)
        }
    }
    
    override suspend fun clearExpired() = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = dao.getExpiredKeys(currentTime)
        dao.deleteExpired(currentTime)
        expiredKeys.forEach { key ->
            cacheMonitor.onCacheExpired(key)
        }
    }
} 