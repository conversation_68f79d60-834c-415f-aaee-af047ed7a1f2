package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * 情侣排行榜数据
 */
data class RankCoupleResponse(
    @SerializedName("monthName")
    val monthName: String,
    @SerializedName("sortNo")
    val sortNo: String,
    @SerializedName("rankData")
    val rankData: List<RankCoupleItem>
)

/**
 * 情侣排行榜项
 */
data class RankCoupleItem(
    @SerializedName("sort")
    val sort: Int,
    @SerializedName("userModel")
    val userModel: CoupleUserModel,
    @SerializedName("broadcastetModel")
    val broadcastetModel: CoupleBroadcasterModel
)

/**
 * 情侣排行榜用户模型
 */
data class CoupleUserModel(
    @SerializedName("sort")
    val sort: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("liveLevel")
    val liveLevel: Int,
    @SerializedName("hidingLiveLevel")
    val hidingLiveLevel: Boolean? = null,
    @SerializedName("nickname")
    val nickname: String,
    @SerializedName("avatar")
    val avatar: String,
    @SerializedName("avatarMapPath")
    val avatarMapPath: String,
    @SerializedName("sets")
    val sets: UserDecorationSets
)

/**
 * 情侣排行榜主播模型
 */
data class CoupleBroadcasterModel(
    @SerializedName("sort")
    val sort: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("nickname")
    val nickname: String,
    @SerializedName("avatar")
    val avatar: String,
    @SerializedName("avatarMapPath")
    val avatarMapPath: String
)
