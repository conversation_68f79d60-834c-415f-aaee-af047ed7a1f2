package com.stargate.pxo.data.network.storage.db

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 缓存数据实体
 * 用于在Room数据库中存储缓存数据
 */
@Entity(tableName = "cache_data")
data class CacheEntity(
    /**
     * 缓存键，作为主键
     */
    @PrimaryKey
    val key: String,
    
    /**
     * 缓存的数据，以JSON字符串形式存储
     */
    val data: String,
    
    /**
     * 数据类型的完全限定名，用于反序列化
     */
    val dataType: String,
    
    /**
     * 创建时间（毫秒）
     */
    val createTime: Long,
    
    /**
     * 过期时间（毫秒），0表示永不过期
     */
    val expireTime: Long
) {
    /**
     * 检查数据是否已过期
     * @return 如果数据已过期返回true，否则返回false
     */
    fun isExpired(): Boolean {
        if (expireTime <= 0) {
            return false // 永不过期
        }
        val currentTime = System.currentTimeMillis()
        return currentTime > (createTime + expireTime)
    }
} 