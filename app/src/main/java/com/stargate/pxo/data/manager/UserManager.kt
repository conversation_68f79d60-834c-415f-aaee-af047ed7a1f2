package com.stargate.pxo.data.manager

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.stargate.pxo.common.util.SPKey
import com.stargate.pxo.common.util.SPUtil
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.data.db.dao.UserDao
import com.stargate.pxo.data.db.entity.UserEntity
import com.stargate.pxo.data.network.model.UserInfo
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import com.stargate.pxo.data.repository.UserInfoRepository
import com.stargate.pxo.data.network.Resource
import kotlinx.coroutines.flow.first

/**
 * 用户信息管理类
 * 负责用户信息的存储、更新、获取等操作
 */
object UserManager {


    var isFirstRegister = false
    
    // 当前用户信息 StateFlow (使用UserInfo而非UserEntity)
    private val _currentUser = MutableStateFlow<UserInfo?>(null)
    val currentUser: StateFlow<UserInfo?> = _currentUser
    
    // 是否已登录
    val isLoggedIn: Boolean
        get() = SPUtil.getBoolean(SPKey.IS_LOGIN, false)
    
    // 用户ID
    val userId: String?
        get() = SPUtil.getString(SPKey.USER_ID)
    
    // 用户Token
   val token: String?
        get() = SPUtil.getDecryptedString(SPKey.ACCESS_TOKEN)
    
    // 用户Dao (需要在使用前通过init方法设置)
    private var userDao: UserDao? = null
    
    /**
     * 初始化UserManager
     * @param dao 用户数据访问对象
     */
    fun init(dao: UserDao) {
        userDao = dao
        // 如果已登录，从数据库加载用户信息
        if (isLoggedIn) {
            loadCurrentUser()
        }
    }
    
    /**
     * 从数据库加载当前用户信息
     */
    private fun loadCurrentUser() {
        val uid = userId ?: return
        PuxxiCoroutine.io {
            userDao?.getUserById(uid)?.let { userEntity ->
                // 将UserEntity转换为UserInfo
                val userInfo = convertToUserInfo(userEntity)
                PuxxiCoroutine.withMain {
                    _currentUser.value = userInfo
                }
            }
        }
    }
    
    /**
     * 保存用户信息到数据库
     * @param userInfo API返回的用户信息
     */
    fun saveUserInfo(userInfo: UserInfo) {
        // 保存基本信息到SP
        SPUtil.putString(SPKey.USER_ID, userInfo.userId)

        // 转换为数据库实体
        val userEntity = convertToEntity(userInfo)
        
        // 保存到数据库
        PuxxiCoroutine.io {
            userDao?.insertOrUpdate(userEntity)
            // 更新内存中的用户信息
            PuxxiCoroutine.withMain {
                _currentUser.value = userInfo
            }
        }
    }
    
    /**
     * 将API用户信息转换为数据库实体
     */
    private fun convertToEntity(userInfo: UserInfo): UserEntity {
        return UserEntity(
            userId = userInfo.userId,
            nickname = userInfo.nickname,
            avatar = userInfo.avatar,
            avatarUrl = userInfo.avatarUrl,
            gender = userInfo.gender,
            age = userInfo.age,
            birthday = userInfo.birthday,
            country = userInfo.country,
            level = userInfo.level,
            isVip = userInfo.isVip,
            createTime = userInfo.createTime,
            lastUpdateTime = System.currentTimeMillis()
        )
    }
    
    /**
     * 将数据库实体转换为UserInfo
     */
    private fun convertToUserInfo(entity: UserEntity): UserInfo {
        return UserInfo(
            userId = entity.userId,
            nickname = entity.nickname,
            avatar = entity.avatar,
            avatarUrl = entity.avatarUrl,
            gender = entity.gender,
            age = entity.age,
            birthday = entity.birthday,
            country = entity.country,
            level = entity.level,
            isVip = entity.isVip,
            createTime = entity.createTime
            // 其他字段可以设置默认值或从entity.extraInfo中解析
        )
    }
    
    /**
     * 更新用户信息
     * @param update 更新操作
     */
    fun updateUserInfo(update: (UserInfo) -> UserInfo) {
        val current = _currentUser.value ?: return
        val updated = update(current)
        
        // 转换为数据库实体
        val userEntity = convertToEntity(updated)
        
        PuxxiCoroutine.io {
            userDao?.insertOrUpdate(userEntity)
            PuxxiCoroutine.withMain {
                _currentUser.value = updated
            }
        }
    }
    
    /**
     * 更新用户昵称
     */
    fun updateNickname(nickname: String) {
        updateUserInfo { it.copy(nickname = nickname) }
    }

    /**
     * 更新用户头像
     */
    fun updateAvatar(avatar: String, avatarUrl: String) {
        updateUserInfo { it.copy(avatar = avatar, avatarUrl = avatarUrl) }
    }
    
    /**
     * 更新用户性别
     */
    fun updateGender(gender: Int) {
        updateUserInfo { it.copy(gender = gender) }
    }
    
    /**
     * 更新用户生日
     */
    fun updateBirthday(birthday: String) {
        updateUserInfo { it.copy(birthday = birthday) }
    }
    
    /**
     * 获取用户信息Flow
     */
    fun getUserFlow(userId: String): Flow<UserEntity?>? {
        return userDao?.getUserFlow(userId)
    }
    
    /**
     * 获取用户信息并转换为UserInfo
     */
    suspend fun getUser(userId: String): UserInfo? {
        val entity = userDao?.getUserById(userId) ?: return null
        return convertToUserInfo(entity)
    }
    
    /**
     * 清除用户信息（退出登录）
     */
    fun clearUserInfo() {
        // 清除SP中的用户信息
        // 解决重载歧义问题，明确指定要调用的是第一个remove方法
        val keysToRemove = arrayOf(
            SPKey.IS_LOGIN,
            SPKey.ACCESS_TOKEN,
            SPKey.USER_ID,
        )
        
        // 逐个移除键值
        keysToRemove.forEach { key ->
            SPUtil.remove(key)
        }
        
        SPUtil.putBoolean(SPKey.IS_LOGIN, false)
        
        // 清除内存中的用户信息
        _currentUser.value = null
        
        // 不删除数据库中的用户信息，保留历史记录
    }

    /**
     * 刷新用户信息
     * 调用 getUserInfo 接口并更新本地数据库和内存中的用户数据
     * @param userInfoRepository 用户信息仓库
     * @param onComplete 完成回调，参数为是否成功
     */
    suspend fun refreshUserInfo(userInfoRepository: UserInfoRepository, onComplete: ((Boolean) -> Unit)? = null) {
        try {
            // 调用 UserInfoRepository 的 refreshUserInfo 方法
            val resource = userInfoRepository.refreshUserInfo().first()
            
            when (resource) {
                is Resource.Success -> {
                    // 保存用户信息到数据库和内存
                    saveUserInfo(resource.data)
                    onComplete?.invoke(true)
                }
                is Resource.Error -> {
                    // 获取用户信息失败
                    onComplete?.invoke(false)
                }
                is Resource.Loading -> {
                    // 忽略加载状态
                }
            }
        } catch (e: Exception) {
            // 发生异常
            onComplete?.invoke(false)
        }
    }
} 