# Puxxi 数据存储框架

基于 Room 数据库和内存缓存的数据存储框架，提供统一的数据存储接口和多级缓存策略。

## 核心组件

### DataStorage

定义统一的数据存储接口，包含以下操作：

- `saveData`: 保存数据
- `getData`: 获取数据
- `hasValidData`: 检查数据是否存在且未过期
- `removeData`: 删除指定的数据
- `clearAll`: 清除所有数据
- `clearExpired`: 清除过期数据

### StorageType

定义不同的数据存储方式：

- `MEMORY`: 内存存储，应用重启后数据会丢失
- `DATABASE`: 数据库存储，数据持久化保存
- `MULTI_LEVEL`: 多级存储，结合内存和数据库的优点

### StorageManager

管理不同的存储实现，提供统一的访问接口：

- `getStorage(type: StorageType)`: 获取指定类型的存储实现
- `defaultStorage`: 默认存储实现（多级存储）

## 存储实现

### MemoryDataStorage

基于内存的数据存储实现，特点：

- 访问速度快
- 应用重启后数据会丢失
- 适合存储临时数据或短期缓存

### RoomDataStorage

基于 Room 数据库的数据存储实现，特点：

- 数据持久化保存
- 应用重启后数据仍然存在
- 适合存储重要数据或长期缓存

### MultiLevelDataStorage

结合内存和数据库的多级存储实现，特点：

- 先尝试从内存获取数据，提高访问速度
- 内存中没有数据时，尝试从数据库获取
- 写入时同时写入内存和数据库
- 兼具访问速度和数据持久化的优点

## 与网络框架集成

NetworkClient 提供了 `executeWithCache` 方法，用于执行带缓存的 API 请求：

```kotlin
fun <T> executeWithCache(
    cacheKey: String,
    storageType: StorageType = StorageType.MULTI_LEVEL,
    expireTime: Long = 30,
    timeUnit: TimeUnit = TimeUnit.MINUTES,
    forceRefresh: Boolean = false,
    apiCall: suspend () -> BaseResponse<T>
): Flow<ApiResult<T>>
```

参数说明：

- `cacheKey`: 缓存键，用于标识缓存数据
- `storageType`: 存储类型，默认为多级存储
- `expireTime`: 过期时间，默认为 30
- `timeUnit`: 时间单位，默认为分钟
- `forceRefresh`: 是否强制刷新，默认为 false
- `apiCall`: API 请求函数

## 使用示例

### 1. 在仓库中使用

```kotlin
fun getUserInfo(userId: String): Flow<ApiResult<User>> {
    val cacheKey = "user_info_$userId"
    
    return networkClient.executeWithCache(
        cacheKey = cacheKey,
        storageType = StorageType.MULTI_LEVEL,
        expireTime = 30,
        timeUnit = TimeUnit.MINUTES
    ) {
        userApiService.getUserInfo(userId)
    }.map { result ->
        result.map { it.toDomainModel() }
    }
}
```

### 2. 直接使用存储管理器

```kotlin
class DataRepository @Inject constructor(
    private val storageManager: StorageManager
) {
    suspend fun saveUserPreferences(preferences: UserPreferences) {
        val storage = storageManager.getStorage(StorageType.DATABASE)
        storage.saveData("user_preferences", preferences)
    }
    
    suspend fun getUserPreferences(): UserPreferences? {
        val storage = storageManager.defaultStorage
        return storage.getData("user_preferences", UserPreferences::class.java)
    }
}
```

### 3. 根据数据特性选择不同的存储类型

```kotlin
// 大型列表数据，只缓存在内存中
fun getArticleList(page: Int): Flow<ApiResult<List<Article>>> {
    val cacheKey = "article_list_$page"
    
    return networkClient.executeWithCache(
        cacheKey = cacheKey,
        storageType = StorageType.MEMORY, // 只使用内存缓存
        expireTime = 5,
        timeUnit = TimeUnit.MINUTES
    ) {
        articleApiService.getArticles(page)
    }
}

// 用户配置数据，持久化存储在数据库中
fun getUserSettings(): Flow<ApiResult<UserSettings>> {
    val cacheKey = "user_settings"
    
    return networkClient.executeWithCache(
        cacheKey = cacheKey,
        storageType = StorageType.DATABASE, // 使用数据库存储
        expireTime = 24,
        timeUnit = TimeUnit.HOURS
    ) {
        settingsApiService.getUserSettings()
    }
}
```

## 数据过期策略

- 可以为每种数据设置不同的过期时间
- 过期时间为 0 表示永不过期
- 可以通过 `clearExpired()` 方法清除所有过期数据
- 可以通过 `forceRefresh = true` 强制刷新数据，忽略缓存

## 注意事项

1. 对于频繁变化的数据，应设置较短的过期时间或使用 `forceRefresh = true`
2. 对于大型数据集合，建议使用 `MEMORY` 存储类型，避免大量数据写入数据库
3. 对于重要的用户数据，建议使用 `DATABASE` 或 `MULTI_LEVEL` 存储类型
4. 在数据更新后，应主动清除相关缓存 