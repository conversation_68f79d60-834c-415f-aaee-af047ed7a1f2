package com.stargate.pxo.data.network.storage

import javax.inject.Inject
import javax.inject.Singleton

/**
 * 存储管理器
 * 用于协调不同的存储实现，提供统一的访问接口
 */
@Singleton
class StorageManager @Inject constructor(
    private val memoryStorage: MemoryDataStorage,
    private val roomStorage: RoomDataStorage,
    private val multiLevelStorage: MultiLevelDataStorage
) {
    
    /**
     * 获取指定类型的存储实现
     * @param type 存储类型
     * @return 对应类型的存储实现
     */
    fun getStorage(type: StorageType): DataStorage {
        return when (type) {
            StorageType.MEMORY -> memoryStorage
            StorageType.DATABASE -> roomStorage
            StorageType.MULTI_LEVEL -> multiLevelStorage
        }
    }
    
    /**
     * 默认存储实现（多级存储）
     */
    val defaultStorage: DataStorage
        get() = multiLevelStorage
} 