package com.stargate.pxo.data.network.storage

import java.util.concurrent.TimeUnit
import kotlinx.coroutines.flow.Flow
import com.stargate.pxo.data.network.Resource

/**
 * 数据仓库接口
 * 统一的数据存取接口，支持内存缓存和持久化存储
 */
interface DataRepository {
    /**
     * 获取数据（指定类型）
     * @param key 数据键
     * @param type 数据类型
     * @param fetchStrategy 获取策略
     * @return 数据流
     */
    fun <T : Any> get(
        key: String, 
        type: Class<T>, 
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST
    ): Flow<Resource<T>>
    
    /**
     * 获取数据（指定类型）
     * 新增方法，与getWithType类似但接受显式类型参数，用于非reified上下文
     * @param key 数据键
     * @param fetchStrategy 获取策略
     * @param type 数据类型
     * @return 数据流
     */
    fun <T : Any> getWithExplicitType(
        key: String,
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST,
        type: Class<T>
    ): Flow<Resource<T>>
    
    /**
     * 保存数据
     * @param key 数据键
     * @param data 数据
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     */
    suspend fun <T : Any> save(
        key: String, 
        data: T, 
        expireTime: Long = 0, 
        timeUnit: TimeUnit = TimeUnit.MILLISECONDS
    )
    
    /**
     * 检查数据是否存在且有效
     * @param key 数据键
     * @return 是否存在且有效
     */
    suspend fun hasValidData(key: String): Boolean
    
    /**
     * 删除数据
     * @param key 数据键
     */
    suspend fun removeData(key: String)
    
    /**
     * 清除所有数据
     */
    suspend fun clear()
    
    /**
     * 清除指定键的数据
     * @param key 数据键
     */
    suspend fun clear(key: String) {
        removeData(key)
    }
    
    /**
     * 清除过期数据
     */
    suspend fun clearExpired()
    
    /**
     * 执行带缓存的网络请求
     * @param key 缓存键
     * @param fetchStrategy 获取策略
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param networkCall 网络请求函数
     * @return 数据流
     */
    fun <T : Any> executeWithCache(
        key: String,
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST,
        expireTime: Long = 30,
        timeUnit: TimeUnit = TimeUnit.MINUTES,
        networkCall: suspend () -> T
    ): Flow<Resource<T>>
    
    /**
     * 执行带缓存的网络请求（指定类型）
     * 新增方法，与executeWithCache类似但接受显式类型参数，用于非reified上下文
     * @param key 缓存键
     * @param fetchStrategy 获取策略
     * @param expireTime 过期时间
     * @param timeUnit 时间单位
     * @param type 数据类型
     * @param networkCall 网络请求函数
     * @return 数据流
     */
    fun <T : Any> executeWithCacheAndType(
        key: String,
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST,
        expireTime: Long = 30,
        timeUnit: TimeUnit = TimeUnit.MINUTES,
        type: Class<T>,
        networkCall: suspend () -> T
    ): Flow<Resource<T>>
    
    /**
     * DSL风格的数据存储
     * @param data 要存储的数据
     * @param config 存储配置
     */
    suspend fun <T : Any> store(data: T, config: StoreConfig.() -> Unit)
    
    /**
     * DSL风格的数据获取
     * @param config 获取配置
     * @return 数据流
     */
    fun <T : Any> fetch(config: FetchConfig<T>.() -> Unit): Flow<Resource<T>>
}

/**
 * 存储配置类
 */
class StoreConfig {
    var key: String = ""
    var expireTime: Long = 0
    var timeUnit: TimeUnit = TimeUnit.MILLISECONDS
}

/**
 * 获取配置类
 */
class FetchConfig<T : Any> {
    var key: String = ""
    var strategy: FetchStrategy = FetchStrategy.CACHE_FIRST
    var remote: (suspend () -> T?)? = null
    var onSuccess: ((T) -> Unit)? = null
    var onError: ((String) -> Unit)? = null
}

/**
 * 数据获取策略
 */
enum class FetchStrategy {
    /**
     * 只从远程获取
     */
    REMOTE_ONLY,
    
    /**
     * 只从缓存获取
     */
    CACHE_ONLY,
    
    /**
     * 优先缓存，缓存不可用时请求远程
     */
    CACHE_FIRST,
    
    /**
     * 优先远程，远程失败时使用缓存
     */
    REMOTE_FIRST,
    
    /**
     * 先返回缓存，然后请求远程更新
     */
    CACHE_AND_REMOTE
} 