package com.stargate.pxo.data.network.storage

/**
 * 数据存储接口
 * 定义统一的数据存储操作，可以有不同的实现（内存缓存、文件缓存、数据库等）
 */
interface DataStorage {
    /**
     * 保存数据
     * @param key 数据的唯一标识
     * @param data 要保存的数据
     * @param expireTime 过期时间（毫秒），默认为0表示永不过期
     */
    suspend fun <T> saveData(key: String, data: T, expireTime: Long = 0)
    
    /**
     * 获取数据
     * @param key 数据的唯一标识
     * @param type 数据类型
     * @return 存储的数据，如果不存在或已过期则返回null
     */
    suspend fun <T : Any> getData(key: String, type: Class<T>): T?
    
    /**
     * 检查数据是否存在且未过期
     * @param key 数据的唯一标识
     * @return 如果数据存在且未过期返回true，否则返回false
     */
    suspend fun hasValidData(key: String): Boolean
    
    /**
     * 删除指定的数据
     * @param key 数据的唯一标识
     */
    suspend fun removeData(key: String)
    
    /**
     * 清除所有数据
     */
    suspend fun clearAll()
    
    /**
     * 清除过期数据
     */
    suspend fun clearExpired()
} 