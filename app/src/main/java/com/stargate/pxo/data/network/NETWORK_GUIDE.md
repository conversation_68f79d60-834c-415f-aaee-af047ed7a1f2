# Puxxi 网络框架完整使用指南

一个基于Retrofit + Hilt + Flow的现代化Android网络框架，提供完整的网络请求、缓存管理和错误处理解决方案。

## 目录

1. [框架概述](#框架概述)
2. [配置说明](#配置说明)
3. [URL管理](#url管理)
4. [API接口定义](#api接口定义)
5. [网络请求调用](#网络请求调用)
6. [响应处理](#响应处理)
7. [缓存策略](#缓存策略)
8. [错误处理](#错误处理)
9. [完整示例](#完整示例)
10. [最佳实践](#最佳实践)

## 框架概述

### 核心组件

```
NetworkModule (DI配置)
├── NetworkConfig (网络配置)
├── PuxxiUrl (URL管理)
├── ApiClient (Retrofit客户端)
├── EnhancedNetworkClient (增强网络客户端)
├── DataRepository (缓存仓库)
└── GlobalErrorHandler (全局错误处理)
```

### 主要特性

- **类型安全**：完全基于Kotlin类型系统
- **响应式编程**：基于Kotlin Flow的异步处理
- **多级缓存**：内存缓存 + Room数据库持久化
- **智能重试**：支持指数退避算法的自动重试
- **统一错误处理**：标准化的错误处理机制
- **DSL风格API**：简洁直观的链式调用
- **URL混淆**：支持开发/生产环境URL切换

## 配置说明

### 1. 网络配置 (NetworkConfig)

```kotlin
// 位置：data/network/config/NetworkConfig.kt
@Singleton
class NetworkConfig @Inject constructor() {
    // 基础配置
    val baseUrl: String = "https://test-app.puxxi.club"
    val isDebug: Boolean = true
    
    // 超时配置
    val connectTimeout: Long = 30 // 连接超时(秒)
    val readTimeout: Long = 30    // 读取超时(秒)
    val writeTimeout: Long = 30   // 写入超时(秒)
    
    // 缓存配置
    val maxCacheSize: Long = 10 * 1024 * 1024 // 10MB
    val cacheExpiration: Long = 60 * 60       // 1小时
    
    // 重试配置
    val retryCount: Int = 3
}
```

### 2. 依赖注入配置 (NetworkModule)

```kotlin
// 位置：data/network/NetworkModule.kt
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideNetworkClient(dataRepository: DataRepository): EnhancedNetworkClient {
        return EnhancedNetworkClient(dataRepository)
    }
    
    @Provides
    @Singleton
    fun provideUserApiService(apiClient: ApiClient): UserApiService {
        return apiClient.create(UserApiService::class.java)
    }
}
```

## URL管理

### 1. URL配置 (PuxxiUrl)

```kotlin
// 位置：data/network/PuxxiUrl.kt
object PuxxiUrl {
    
    // 设置URL模式
    fun setUrlMode(useObfuscated: Boolean) {
        useObfuscatedUrl = useObfuscated
    }
    
    // URL常量（自动根据模式返回对应URL）
    val USER_LOGIN: String
        get() = if (useObfuscatedUrl) {
            "rpc_base/v2/doc_max_value/del"  // 混淆URL
        } else {
            "/user/login"                    // 真实URL
        }
    
    val USER_REGISTER: String
        get() = if (useObfuscatedUrl) {
            "rpc_base/v0/game_max_result/add"
        } else {
            "/security/register"
        }
}
```

### 2. URL模式切换

```kotlin
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 开发环境：使用真实URL
        if (BuildConfig.DEBUG) {
            PuxxiUrl.setUrlMode(false)
        } else {
            // 生产环境：使用混淆URL
            PuxxiUrl.setUrlMode(true)
        }
    }
}
```

## API接口定义

### 1. 基础响应模型 (BaseResponse)

```kotlin
// 位置：data/network/model/BaseResponse.kt
data class BaseResponse<T>(
    val code: Int = 0,           // 状态码，0表示成功
    val msg: String = "",        // 错误信息
    val data: T? = null,         // 实际数据
    val key: String = ""         // 加密密钥
) {
    val isSuccess: Boolean
        get() = code == SUCCESS_CODE
    
    companion object {
        const val SUCCESS_CODE = 0
    }
}
```

### 2. API服务接口定义

```kotlin
// 位置：data/network/api/UserApiService.kt
interface UserApiService {
    
    /**
     * 用户登录
     * 使用PuxxiUrl动态获取URL
     */
    @POST
    suspend fun login(
        @Url url: String = PuxxiUrl.USER_LOGIN,
        @Body request: LoginRequest
    ): Response<BaseResponse<UserDto>>
    
    /**
     * 获取用户信息
     */
    @GET
    suspend fun getUserInfo(
        @Url url: String = PuxxiUrl.USER_GET_INFO,
        @Query("userId") userId: String
    ): Response<BaseResponse<UserDto>>
    
    /**
     * 更新用户信息
     */
    @POST
    suspend fun updateUserInfo(
        @Url url: String = PuxxiUrl.USER_SAVE_USER_INFO,
        @Body request: UpdateUserRequest
    ): Response<BaseResponse<UserDto>>
}
```

### 3. 数据传输对象

```kotlin
// 请求模型
data class LoginRequest(
    val username: String,
    val password: String
)

data class UpdateUserRequest(
    val userId: String,
    val nickname: String? = null,
    val avatar: String? = null
)

// 响应模型
data class UserDto(
    val id: String,
    val username: String,
    val nickname: String?,
    val email: String?,
    val avatar: String?,
    val createTime: String
)
```

## 网络请求调用

### 1. 在Repository中使用

```kotlin
@Singleton
class UserRepository @Inject constructor(
    private val userApiService: UserApiService,
    private val networkClient: EnhancedNetworkClient
) {
    
    /**
     * 用户登录
     */
    suspend fun login(username: String, password: String): Flow<Resource<UserDto>> {
        return networkClient.execute(
            apiCall = {
                userApiService.login(
                    request = LoginRequest(username, password)
                )
            },
            retryCount = 2,      // 重试2次
            retryDelay = 1000    // 重试间隔1秒
        )
    }
    
    /**
     * 获取用户信息（带缓存）
     */
    suspend fun getUserInfo(userId: String): Flow<Resource<UserDto>> {
        return networkClient.executeWithCache(
            cacheKey = "user_info_$userId",
            apiCall = {
                userApiService.getUserInfo(userId)
            },
            cacheStrategy = FetchStrategy.CACHE_FIRST,
            expireTime = 30,
            timeUnit = TimeUnit.MINUTES
        )
    }
}
```

### 2. 在ViewModel中使用

```kotlin
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    
    private val _loginState = MutableStateFlow<Resource<UserDto>>(Resource.idle())
    val loginState: StateFlow<Resource<UserDto>> = _loginState.asStateFlow()
    
    /**
     * 执行登录
     */
    fun login(username: String, password: String) {
        viewModelScope.launch {
            userRepository.login(username, password)
                .collect { resource ->
                    _loginState.value = resource
                }
        }
    }
    
    /**
     * 获取用户信息
     */
    fun getUserInfo(userId: String) {
        viewModelScope.launch {
            userRepository.getUserInfo(userId)
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            // 显示加载状态
                        }
                        is Resource.Success -> {
                            // 处理成功数据
                            val user = resource.data
                        }
                        is Resource.Error -> {
                            // 处理错误
                            val errorMsg = resource.message
                        }
                    }
                }
        }
    }
}
```

### 3. DSL风格API调用

```kotlin
// 使用DSL风格进行网络请求
viewModelScope.launch {
    networkClient.request<UserDto> {
        // API调用
        api { userApiService.getUserInfo(userId) }
        
        // 缓存配置
        cache {
            key = "user_info_$userId"
            strategy = FetchStrategy.CACHE_FIRST
            expireTime = 30
            timeUnit = TimeUnit.MINUTES
        }
        
        // 重试配置
        retry {
            count = 2
            delay = 1000
        }
        
        // 状态回调
        onStart { 
            showLoading() 
        }
        onSuccess { user -> 
            hideLoading()
            updateUI(user) 
        }
        onError { error -> 
            hideLoading()
            showError(error.message) 
        }
    }.collect()
}
```

## 响应处理

### 1. Resource状态包装

```kotlin
// 位置：data/network/Resource.kt
sealed class Resource<out T> {
    data class Success<T>(val data: T) : Resource<T>()
    data class Error(val message: String, val code: Int = -1) : Resource<Nothing>()
    data class Loading(val isLoading: Boolean = true) : Resource<Nothing>()
    
    // 便捷方法
    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error
    val isLoading: Boolean get() = this is Loading
    
    companion object {
        fun <T> success(data: T): Resource<T> = Success(data)
        fun error(message: String, code: Int = -1): Resource<Nothing> = Error(message, code)
        fun loading(): Resource<Nothing> = Loading()
    }
}
```

### 2. 在UI中处理响应

```kotlin
@Composable
fun UserScreen(viewModel: UserViewModel = hiltViewModel()) {
    val loginState by viewModel.loginState.collectAsState()
    
    // 根据状态渲染UI
    when (loginState) {
        is Resource.Loading -> {
            CircularProgressIndicator()
        }
        
        is Resource.Success -> {
            val user = loginState.data
            UserProfile(user = user)
        }
        
        is Resource.Error -> {
            ErrorMessage(
                message = loginState.message,
                onRetry = { viewModel.retryLogin() }
            )
        }
    }
}
```

## 缓存策略

### 1. 缓存策略类型

```kotlin
enum class FetchStrategy {
    CACHE_ONLY,        // 只从缓存获取
    REMOTE_ONLY,       // 只从远程获取
    CACHE_FIRST,       // 优先缓存，不存在时请求远程
    REMOTE_FIRST,      // 优先远程，失败时使用缓存
    CACHE_AND_REMOTE   // 先返回缓存，然后更新远程
}
```

### 2. 缓存配置

```kotlin
// 使用数据仓库进行缓存操作
viewModelScope.launch {
    dataRepository.fetch<UserDto> {
        key = "user_profile_$userId"
        strategy = FetchStrategy.CACHE_FIRST
        expireTime = 30
        timeUnit = TimeUnit.MINUTES
        
        // 远程获取逻辑
        remote {
            userApiService.getUserInfo(userId).body()?.data
        }
        
        onSuccess { user -> updateUI(user) }
        onError { error -> showError(error.message) }
    }.collect()
}
```

### 3. 缓存键管理

```kotlin
// 位置：data/network/storage/CacheKeys.kt
object CacheKeys {
    object User {
        fun profile(userId: String = "") = "user_profile_$userId"
        fun settings() = "user_settings"
        fun friends(userId: String) = "user_friends_$userId"
    }
    
    object Content {
        fun detail(articleId: String) = "article_detail_$articleId"
        fun feed(category: String) = "content_feed_$category"
    }
    
    // 自定义缓存键
    fun custom(vararg parts: String) = parts.joinToString("_")
}
```

## 错误处理

### 1. 网络异常类型

```kotlin
// 位置：data/network/exception/NetworkException.kt
sealed class NetworkException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    
    // 网络连接异常
    class NetworkError(cause: Throwable) : NetworkException("网络连接失败", cause)
    
    // 服务器异常
    class ServerError(val code: Int, message: String) : NetworkException("服务器错误: $message")
    
    // 超时异常
    class TimeoutError(cause: Throwable) : NetworkException("请求超时", cause)
    
    // 解析异常
    class ParseError(cause: Throwable) : NetworkException("数据解析失败", cause)
    
    // 业务异常
    class BusinessError(val code: Int, message: String) : NetworkException(message)
}
```

### 2. 全局错误处理

```kotlin
// 位置：data/network/client/GlobalErrorHandler.kt
class GlobalErrorHandler : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        
        return try {
            val response = chain.proceed(request)
            
            // 检查响应状态
            if (!response.isSuccessful) {
                handleHttpError(response)
            }
            
            response
        } catch (e: Exception) {
            handleNetworkError(e)
            throw e
        }
    }
    
    private fun handleHttpError(response: Response) {
        when (response.code) {
            401 -> {
                // 处理未授权
                LogUtil.w(TAG, "Unauthorized access")
                // 可以发送事件通知UI跳转到登录页
            }
            403 -> {
                // 处理禁止访问
                LogUtil.w(TAG, "Forbidden access")
            }
            500 -> {
                // 处理服务器内部错误
                LogUtil.e(TAG, "Server internal error")
            }
        }
    }
}
```

### 3. 错误处理最佳实践

```kotlin
class UserRepository @Inject constructor(
    private val userApiService: UserApiService,
    private val networkClient: EnhancedNetworkClient
) {
    
    suspend fun getUserInfo(userId: String): Flow<Resource<UserDto>> {
        return networkClient.execute(
            apiCall = { userApiService.getUserInfo(userId) }
        ).catch { exception ->
            // 统一错误处理
            val errorResource = when (exception) {
                is NetworkException.NetworkError -> {
                    Resource.error("网络连接失败，请检查网络设置")
                }
                is NetworkException.TimeoutError -> {
                    Resource.error("请求超时，请稍后重试")
                }
                is NetworkException.ServerError -> {
                    Resource.error("服务器繁忙，请稍后重试")
                }
                is NetworkException.BusinessError -> {
                    Resource.error(exception.message)
                }
                else -> {
                    Resource.error("未知错误，请稍后重试")
                }
            }
            emit(errorResource)
        }
    }
}
```

## 完整示例

### 1. 用户登录完整流程

```kotlin
// 1. API接口定义
interface AuthApiService {
    @POST
    suspend fun login(
        @Url url: String = PuxxiUrl.SECURITY_LOGIN,
        @Body request: LoginRequest
    ): Response<BaseResponse<AuthResponse>>
}

// 2. Repository实现
@Singleton
class AuthRepository @Inject constructor(
    private val authApiService: AuthApiService,
    private val networkClient: EnhancedNetworkClient,
    private val dataRepository: DataRepository
) {
    
    suspend fun login(username: String, password: String): Flow<Resource<AuthResponse>> {
        return networkClient.execute(
            apiCall = {
                authApiService.login(
                    request = LoginRequest(username, password)
                )
            },
            retryCount = 1,
            retryDelay = 2000
        ).onEach { resource ->
            // 登录成功后缓存用户信息
            if (resource is Resource.Success) {
                dataRepository.store(resource.data) {
                    key = CacheKeys.User.profile()
                    expireTime = 7
                    timeUnit = TimeUnit.DAYS
                }
            }
        }
    }
    
    suspend fun logout() {
        // 清除缓存
        dataRepository.clear(CacheKeys.User.profile())
    }
}

// 3. ViewModel处理
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _loginState = MutableStateFlow<Resource<AuthResponse>>(Resource.idle())
    val loginState: StateFlow<Resource<AuthResponse>> = _loginState.asStateFlow()
    
    fun login(username: String, password: String) {
        if (username.isBlank() || password.isBlank()) {
            _loginState.value = Resource.error("用户名和密码不能为空")
            return
        }
        
        viewModelScope.launch {
            authRepository.login(username, password)
                .collect { resource ->
                    _loginState.value = resource
                }
        }
    }
}

// 4. UI层使用
@Composable
fun LoginScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onLoginSuccess: () -> Unit
) {
    var username by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    val loginState by viewModel.loginState.collectAsState()
    
    Column(
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 用户名输入
        OutlinedTextField(
            value = username,
            onValueChange = { username = it },
            label = { Text("用户名") },
            enabled = loginState !is Resource.Loading
        )
        
        // 密码输入
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("密码") },
            visualTransformation = PasswordVisualTransformation(),
            enabled = loginState !is Resource.Loading
        )
        
        // 登录按钮
        Button(
            onClick = { viewModel.login(username, password) },
            enabled = loginState !is Resource.Loading,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (loginState is Resource.Loading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
            } else {
                Text("登录")
            }
        }
        
        // 错误信息显示
        if (loginState is Resource.Error) {
            Text(
                text = loginState.message,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
    
    // 监听登录成功
    LaunchedEffect(loginState) {
        if (loginState is Resource.Success) {
            onLoginSuccess()
        }
    }
}
```

## 最佳实践

### 1. 项目结构组织

```
data/network/
├── api/                    # API接口定义
│   ├── UserApiService.kt
│   ├── AuthApiService.kt
│   └── ...
├── client/                 # 网络客户端
│   ├── ApiClient.kt
│   ├── EnhancedNetworkClient.kt
│   └── GlobalErrorHandler.kt
├── config/                 # 配置相关
│   └── NetworkConfig.kt
├── exception/              # 异常定义
│   └── NetworkException.kt
├── model/                  # 数据模型
│   ├── BaseResponse.kt
│   └── ...
├── storage/                # 缓存相关
│   ├── DataRepository.kt
│   ├── CacheKeys.kt
│   └── ...
├── NetworkModule.kt        # DI配置
├── PuxxiUrl.kt            # URL管理
└── README.md              # 文档
```

### 2. 使用建议

1. **统一使用Repository模式**
   ```kotlin
   // ✅ 推荐：在Repository中封装网络请求
   class UserRepository @Inject constructor(
       private val userApiService: UserApiService,
       private val networkClient: EnhancedNetworkClient
   ) {
       suspend fun getUser(id: String) = networkClient.execute {
           userApiService.getUserInfo(id)
       }
   }
   
   // ❌ 不推荐：在ViewModel中直接调用API
   viewModel.launch {
       val response = userApiService.getUserInfo(id)
   }
   ```

2. **合理设置缓存策略**
   ```kotlin
   // 用户信息：优先缓存，过期时间较长
   strategy = FetchStrategy.CACHE_FIRST
   expireTime = 30
   timeUnit = TimeUnit.MINUTES
   
   // 实时数据：优先远程，短时间缓存
   strategy = FetchStrategy.REMOTE_FIRST
   expireTime = 5
   timeUnit = TimeUnit.MINUTES
   
   // 静态配置：只用缓存，手动更新
   strategy = FetchStrategy.CACHE_ONLY
   expireTime = 1
   timeUnit = TimeUnit.DAYS
   ```

3. **统一错误处理**
   ```kotlin
   // 在Repository层统一处理业务错误
   .catch { exception ->
       when (exception) {
           is NetworkException.BusinessError -> {
               when (exception.code) {
                   1001 -> emit(Resource.error("用户名不存在"))
                   1002 -> emit(Resource.error("密码错误"))
                   else -> emit(Resource.error(exception.message))
               }
           }
           else -> emit(Resource.error("网络请求失败"))
       }
   }
   ```

4. **URL管理**
   ```kotlin
   // Application中设置URL模式
   class MyApplication : Application() {
       override fun onCreate() {
           super.onCreate()
           
           // 根据构建类型设置URL模式
           PuxxiUrl.setUrlMode(!BuildConfig.DEBUG)
       }
   }
   ```

5. **日志和监控**
   ```kotlin
   // 在Repository中添加日志
   suspend fun login(username: String, password: String): Flow<Resource<AuthResponse>> {
       LogUtil.d(TAG, "开始登录: username=$username")
       
       return networkClient.execute {
           authApiService.login(LoginRequest(username, password))
       }.onEach { resource ->
           when (resource) {
               is Resource.Success -> LogUtil.d(TAG, "登录成功")
               is Resource.Error -> LogUtil.e(TAG, "登录失败: ${resource.message}")
           }
       }
   }
   ```

### 3. 性能优化

1. **避免重复请求**
   ```kotlin
   // 使用缓存避免重复请求
   private val userCache = ConcurrentHashMap<String, Flow<Resource<UserDto>>>()
   
   fun getUserInfo(userId: String): Flow<Resource<UserDto>> {
       return userCache.getOrPut(userId) {
           networkClient.executeWithCache(
               cacheKey = CacheKeys.User.profile(userId),
               apiCall = { userApiService.getUserInfo(userId) }
           ).shareIn(
               scope = CoroutineScope(Dispatchers.IO),
               started = SharingStarted.WhileSubscribed(5000),
               replay = 1
           )
       }
   }
   ```

2. **合理使用重试机制**
   ```kotlin
   // 对于重要的请求，设置重试
   networkClient.execute(
       apiCall = { userApiService.uploadAvatar(file) },
       retryCount = 3,
       retryDelay = 2000
   )
   
   // 对于实时性要求高的请求，不设置重试
   networkClient.execute(
       apiCall = { chatApiService.sendMessage(message) },
       retryCount = 0
   )
   ```

这个框架提供了完整的网络请求解决方案，通过合理配置和使用，可以大大简化Android应用的网络层开发工作。