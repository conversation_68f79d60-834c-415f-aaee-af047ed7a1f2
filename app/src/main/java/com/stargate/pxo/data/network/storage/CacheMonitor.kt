package com.stargate.pxo.data.network.storage

import android.util.Log
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 缓存监控接口
 * 用于监控缓存的访问、保存和过期情况
 */
interface CacheMonitor {
    /**
     * 缓存过期时调用
     * @param key 缓存键
     */
    fun onCacheExpired(key: String)
    
    /**
     * 缓存访问时调用
     * @param key 缓存键
     * @param isHit 是否命中缓存
     */
    fun onCacheAccessed(key: String, isHit: Boolean)
    
    /**
     * 缓存保存时调用
     * @param key 缓存键
     * @param expireTime 过期时间（毫秒）
     */
    fun onCacheSaved(key: String, expireTime: Long)
    
    /**
     * 缓存清除时调用
     * @param key 缓存键
     */
    fun onCacheRemoved(key: String)
    
    /**
     * 缓存统计
     * @return 缓存统计信息
     */
    fun getCacheStats(): CacheStats
    
    /**
     * 缓存统计信息
     */
    data class CacheStats(
        val totalAccess: Int = 0,
        val hits: Int = 0,
        val misses: Int = 0,
        val hitRate: Float = 0f,
        val totalSaved: Int = 0,
        val totalExpired: Int = 0,
        val totalRemoved: Int = 0
    )
}

/**
 * 默认缓存监控实现
 * 记录缓存操作日志和统计信息
 */
@Singleton
class DefaultCacheMonitor @Inject constructor() : CacheMonitor {
    private val TAG = "CacheMonitor"
    
    // 统计数据
    private var totalAccess = 0
    private var hits = 0
    private var misses = 0
    private var totalSaved = 0
    private var totalExpired = 0
    private var totalRemoved = 0
    
    override fun onCacheExpired(key: String) {
        totalExpired++
        Log.d(TAG, "Cache expired: $key")
    }
    
    override fun onCacheAccessed(key: String, isHit: Boolean) {
        totalAccess++
        if (isHit) {
            hits++
        } else {
            misses++
        }
        Log.d(TAG, "Cache accessed: $key, hit: $isHit")
    }
    
    override fun onCacheSaved(key: String, expireTime: Long) {
        totalSaved++
        val expirationInfo = if (expireTime > 0) {
            "expires in ${expireTime}ms"
        } else {
            "never expires"
        }
        Log.d(TAG, "Cache saved: $key, $expirationInfo")
    }
    
    override fun onCacheRemoved(key: String) {
        totalRemoved++
        Log.d(TAG, "Cache removed: $key")
    }
    
    override fun getCacheStats(): CacheMonitor.CacheStats {
        val hitRate = if (totalAccess > 0) {
            hits.toFloat() / totalAccess
        } else {
            0f
        }
        
        return CacheMonitor.CacheStats(
            totalAccess = totalAccess,
            hits = hits,
            misses = misses,
            hitRate = hitRate,
            totalSaved = totalSaved,
            totalExpired = totalExpired,
            totalRemoved = totalRemoved
        )
    }
} 