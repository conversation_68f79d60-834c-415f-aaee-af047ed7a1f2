package com.stargate.pxo.data.db.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * 主播信息数据库实体
 * 用于存储主播墙列表数据
 */
@Entity(tableName = "broadcasters")
data class BroadcasterEntity(
    @PrimaryKey
    val userId: String,
    
    // 基本信息
    val nickname: String = "",
    val gender: Int = 0,
    val age: Int = 0,
    val status: String? = null,
    val about: String? = null,
    
    // 头像相关
    val avatar: String? = null,
    val avatarUrl: String? = null,
    val avatarThumbUrl: String? = null,
    val avatar2: String? = null,
    val avatarMapPath: String? = null,
    
    // 位置信息
    val country: String = "",
    val provinceName: String? = null,
    val distance: String? = null,
    
    // 状态信息
    val isLive: Boolean? = null,
    val isFriend: Boolean = false,
    val isMultiple: Boolean? = null,
    val isNearbyOpen: Boolean? = null,
    val isSameLanguage: Boolean? = null,
    val isSameProvince: Boolean? = null,
    val isSignBroadcaster: Boolean? = null,
    val leave: Boolean? = null,
    
    // 统计信息
    val audienceNum: Int? = null,
    val followingNum: Int? = null,
    val followNum: Int? = null,
    val grade: Int? = null,
    val liveScore: Double? = null,
    
    // 通话相关
    val callCoins: Int? = null,
    val isCalled: Boolean? = null,
    
    // 房间信息
    val roomNo: String? = null,
    val roomSessionNo: String? = null,
    val roomType: Int? = null,
    val showRoomNo: String? = null,
    val showRoomVersion: Int? = null,
    val mgRoomNo: String? = null,
    
    // 媒体信息
    val background: String? = null,
    val cover: String? = null,
    val liveCover: String? = null,
    val mgRoomBackground: String? = null,
    
    // Token相关
    val token: String? = null,
    val agoraToken: String? = null,
    val mgAgoraToken: String? = null,
    val sessionNo: String? = null,
    
    // 其他信息
    val broadcasterType: Int? = null,
    val rtcType: Int? = null,
    val soundStatus: Int? = null,
    val signPkgMin: Int? = null,
    val unit: String? = null,
    val topOneImpression: String? = null,
    val analysisLanguage: String? = null,
    val isHideCountry: Boolean? = null,
    val activityTagUrl: String? = null,
    
    // 标签和爱好（JSON字符串存储）
    val applicableTags: String? = null, // List<String> 的JSON
    val hobbies: String? = null, // List<String> 的JSON
    val broadcasterOtherLanguage: String? = null, // List<String> 的JSON
    val videoMapPaths: String? = null, // List<String> 的JSON
    val videoPaths: String? = null, // List<VideoPaths> 的JSON
    
    // 时间戳
    val createTime: Long = System.currentTimeMillis(),
    val lastUpdateTime: Long = System.currentTimeMillis()
)
