package com.stargate.pxo.data.network

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.data.network.api.AppNetInterfaceInfo
import com.stargate.pxo.data.network.api.BroadcasterInfo
import com.stargate.pxo.data.network.api.RankInterface
import com.stargate.pxo.data.network.api.UserInfoNetInterface
import com.stargate.pxo.data.network.client.EnhancedNetworkClient
import com.stargate.pxo.data.network.interceptor.HeaderInterceptor
import com.stargate.pxo.data.network.interceptor.ResponseInterceptor
import com.stargate.pxo.data.network.storage.DataRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.ConnectionPool
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton

/**
 * 网络模块
 * 提供网络相关依赖
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    // 超时时间
    private const val CONNECT_TIMEOUT = 60L
    private const val READ_TIMEOUT = 60L
    private const val WRITE_TIMEOUT = 60L
    
    private const val TAG = "NetworkModule"
    
    /**
     * 提供Gson实例
     */
    @Provides
    @Singleton
    fun provideGson(): Gson {
        return GsonBuilder()
            .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
            .serializeNulls()
            .create()
    }
    
    /**
     * 提供OkHttpClient实例
     * 直接添加所有必要的拦截器
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(
        headerInterceptor: HeaderInterceptor,
        responseInterceptor: ResponseInterceptor,
    ): OkHttpClient {
        val builder = OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true) // 启用连接失败重试
            .connectionPool(ConnectionPool(5, 5, TimeUnit.MINUTES)) // 连接池配置

        // 直接添加拦截器
        builder.addInterceptor(headerInterceptor)
        builder.addInterceptor(responseInterceptor)

        return builder.build()
    }
    
    /**
     * 提供Retrofit实例
     */
    @Provides
    @Singleton
    fun provideRetrofit(
        okHttpClient: OkHttpClient,
        gson: Gson,
        @Named("baseUrl") baseUrl: String
    ): Retrofit {
        return Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build()
    }
    
    /**
     * 提供基础URL
     */
    @Provides
    @Singleton
    @Named("baseUrl")
    fun provideBaseUrl(): String {
        // 实际应用中可能会根据不同环境提供不同的URL
        return ApiConstants.BASE_URL
    }
    
    /**
     * 提供AppConfigInfo API服务
     */
    @Provides
    @Singleton
    fun provideAppConfigInfo(retrofit: Retrofit): AppNetInterfaceInfo {
        return retrofit.create(AppNetInterfaceInfo::class.java)
    }


    /**
     * UserInfoNetInterface API服务
     */
    @Provides
    @Singleton
    fun provideUserInfoNetInterface(retrofit: Retrofit): UserInfoNetInterface {
        return retrofit.create(UserInfoNetInterface::class.java)
    }

    /**
     * BroadcasterInfo API服务
     */
    @Provides
    @Singleton
    fun provideBroadcasterInfo(retrofit: Retrofit): BroadcasterInfo {
        return retrofit.create(BroadcasterInfo::class.java)
    }

    /**
     * RankInterface API服务
     */
    @Provides
    @Singleton
    fun provideRankInterface(retrofit: Retrofit): RankInterface {
        return retrofit.create(RankInterface::class.java)
    }
    
    /**
     * 提供EnhancedNetworkClient
     */
    @Provides
    @Singleton
    fun provideEnhancedNetworkClient(dataRepository: DataRepository): EnhancedNetworkClient {
        return EnhancedNetworkClient(dataRepository)
    }
    
    /**
     * 提供加密密钥
     */
    @Provides
    @Singleton
    @Named("encryptionKey")
    fun provideEncryptionKey(): String {
        return ApiConstants.APP_API_DOMAIN.padEnd(32, '0')
    }
}