package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * 登录响应数据
 */
data class LoginResponse(
    @SerializedName("aaaa")
    val aaaa: Int = 0,
    
    @SerializedName("isFirstRegister")
    val isFirstRegister: Boolean = false,
    
    @SerializedName("token")
    val token: String = "",
    
    @SerializedName("userInfo")
    val userInfo: UserInfo? = null
)

/**
 * 用户信息
 */
data class UserInfo(
    @SerializedName("age")
    val age: Int = 0,
    
    @SerializedName("analysisLanguage")
    val analysisLanguage: String = "",
    
    @SerializedName("auditStatus")
    val auditStatus: Int = 0,
    
    @SerializedName("availableCoins")
    val availableCoins: Int = 0,
    
    @SerializedName("avatar")
    val avatar: String = "",
    
    @SerializedName("avatarMiddleThumbUrl")
    val avatarMiddleThumbUrl: String = "",
    
    @SerializedName("avatarStatus")
    val avatarStatus: Int = 0,
    
    @SerializedName("avatarThumbUrl")
    val avatarThumbUrl: String = "",
    
    @SerializedName("avatarUrl")
    val avatarUrl: String = "",
    
    @SerializedName("birthday")
    val birthday: String = "",
    
    @SerializedName("broadcasterType")
    val broadcasterType: Int = 0,
    
    @SerializedName("country")
    val country: String = "",
    
    @SerializedName("createTime")
    val createTime: Long = 0L,
    
    @SerializedName("followNum")
    val followNum: Int = 0,
    
    @SerializedName("gender")
    val gender: Int = 0,
    
    @SerializedName("hasEquity")
    val hasEquity: Boolean = false,
    
    @SerializedName("hasIllegalAvatar")
    val hasIllegalAvatar: Int = 0,
    
    @SerializedName("isAnswer")
    val isAnswer: Boolean = false,
    
    @SerializedName("isBlock")
    val isBlock: Boolean = false,
    
    @SerializedName("isFakeBroadcaster")
    val isFakeBroadcaster: Boolean = false,
    
    @SerializedName("isGreenMode")
    val isGreenMode: Boolean = false,
    
    @SerializedName("isHavePassword")
    val isHavePassword: Boolean = false,
    
    @SerializedName("isInternal")
    val isInternal: Boolean = false,
    
    @SerializedName("isMultiple")
    val isMultiple: Boolean = false,
    
    @SerializedName("isRealFriend")
    val isRealFriend: Boolean = false,
    
    @SerializedName("isRecharge")
    val isRecharge: Boolean = false,
    
    @SerializedName("isReview")
    val isReview: Boolean = false,
    
    @SerializedName("isSwitchNotDisturbCall")
    val isSwitchNotDisturbCall: Boolean = false,
    
    @SerializedName("isSwitchNotDisturbIm")
    val isSwitchNotDisturbIm: Boolean = false,
    
    @SerializedName("isVip")
    val isVip: Boolean = false,
    
    @SerializedName("level")
    val level: Int = 0,
    
    @SerializedName("loginPkgName")
    val loginPkgName: String = "",
    
    @SerializedName("mysteriousInfo")
    val mysteriousInfo: MysteriousInfo? = null,
    
    @SerializedName("nickname")
    val nickname: String = "",
    
    @SerializedName("praiseNum")
    val praiseNum: Int = 0,
    
    @SerializedName("registerCountry")
    val registerCountry: String = "",
    
    @SerializedName("registerPkgName")
    val registerPkgName: String = "",
    
    @SerializedName("tagDetails")
    val tagDetails: List<TagDetail> = emptyList(),
    
    @SerializedName("tagsList")
    val tagsList: List<String> = emptyList(),
    
    @SerializedName("userId")
    val userId: String = "",
    
    @SerializedName("userType")
    val userType: Int = 0
)

/**
 * 用户神秘信息
 */
data class MysteriousInfo(
    @SerializedName("effective")
    val effective: Boolean = false
)

/**
 * 标签详情
 */
data class TagDetail(
    @SerializedName("tag")
    val tag: String = "",
    
    @SerializedName("tagColor")
    val tagColor: String = "",
    
    @SerializedName("tagTip")
    val tagTip: String = ""
) 