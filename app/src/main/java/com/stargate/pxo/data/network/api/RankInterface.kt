package com.stargate.pxo.data.network.api

import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.model.BannerInfo
import com.stargate.pxo.data.network.model.BaseResponse
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface RankInterface {

    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getRankCharm(
        @Url url: String = PuxxiUrl.BROADCASTER_RANK_SEARCH,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<List<Any>>


    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getRankRich(
        @Url url: String = PuxxiUrl.USER_RANK_SEARCH,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<List<Any>>

    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getRankCouple(
        @Url url: String = PuxxiUrl.USER_RANK_SEARCHCOUPLE,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<List<Any>>
}