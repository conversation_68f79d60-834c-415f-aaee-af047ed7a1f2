package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

data class BroadcasterItem(
    @SerializedName("userId")
    val userId: String,

    @SerializedName("nickname")
    val nickname: String,

    @SerializedName("gender")
    val gender: Int,

    @SerializedName("age")
    val age: Int = 0,

    @SerializedName("status")
    var status: String? = null,

    @SerializedName("about")
    val about: String? = null,

    @SerializedName("activityTagUrl")
    val activityTagUrl: String? = null,

    @SerializedName("agoraToken")
    val agoraToken: String? = null,

    @SerializedName("analysisLanguage")
    val analysisLanguage: String? = null,

    @SerializedName("applicableTags")
    val applicableTags: List<String?>? = null,

    @SerializedName("audienceNum")
    val audienceNum: Int? = null,

    @SerializedName("avatar")
    val avatar: String? = null,

    @SerializedName("avatarUrl")
    val avatarUrl: String? = null,

    @SerializedName("avatarThumbUrl")
    val avatarThumbUrl: String? = null,

    @SerializedName("avatar2")
    val avatar2: String? = null,

    @SerializedName("avatarMapPath")
    val avatarMapPath: String? = null,

    @SerializedName("background")
    val background: String? = null,

    @SerializedName("broadcasterOtherLanguage")
    val broadcasterOtherLanguage: List<String?>? = null,

    @SerializedName("broadcasterType")
    val broadcasterType: Int? = null,

    @SerializedName("callCoins")
    val callCoins: Int? = null,

    @SerializedName("country")
    val country: String = "",

    @SerializedName("cover")
    val cover: String? = null,

    @SerializedName("distance")
    val distance: String? = null,

    @SerializedName("followingNum")
    val followingNum: Int? = null,

    @SerializedName("followNum")
    val followNum: Int? = null,

    @SerializedName("grade")
    val grade: Int? = null,

    @SerializedName("hobbies")
    val hobbies: List<String?>? = null,

    @SerializedName("isCalled")
    val isCalled: Boolean? = null,

    @SerializedName("isFriend")
    var isFriend: Boolean = false,

    @SerializedName("isHideCountry")
    val isHideCountry: Boolean? = null,

    @SerializedName("isLive")
    val isLive: Boolean? = null,

    @SerializedName("isMultiple")
    val isMultiple: Boolean? = null,

    @SerializedName("isNearbyOpen")
    val isNearbyOpen: Boolean? = null,

    @SerializedName("isSameLanguage")
    val isSameLanguage: Boolean? = null,

    @SerializedName("isSameProvince")
    val isSameProvince: Boolean? = null,

    @SerializedName("isSignBroadcaster")
    val isSignBroadcaster: Boolean? = null,

    @SerializedName("leave")
    val leave: Boolean? = null,

    @SerializedName("liveCover")
    val liveCover: String? = null,

    @SerializedName("liveScore")
    val liveScore: Double? = null,

    @SerializedName("mgAgoraToken")
    val mgAgoraToken: String? = null,

    @SerializedName("mgRoomBackground")
    val mgRoomBackground: String? = null,

    @SerializedName("mgRoomNo")
    val mgRoomNo: String? = null,

    @SerializedName("provinceName")
    val provinceName: String? = null,

    @SerializedName("roomNo")
    val roomNo: String? = null,

    @SerializedName("roomSessionNo")
    val roomSessionNo: String? = null,

    @SerializedName("roomType")
    var roomType: Int? = null,

    @SerializedName("rtcType")
    val rtcType: Int? = null,

    @SerializedName("sessionNo")
    val sessionNo: String? = null,

    @SerializedName("showRoomNo")
    val showRoomNo: String? = null,

    @SerializedName("showRoomVersion")
    val showRoomVersion: Int? = null,

    @SerializedName("signPkgMin")
    val signPkgMin: Int? = null,

    @SerializedName("soundStatus")
    val soundStatus: Int? = null,

    @SerializedName("token")
    val token: String? = null,

    @SerializedName("topOneImpression")
    val topOneImpression: String? = null,

    @SerializedName("unit")
    val unit: String? = null,

    @SerializedName("videoMapPaths")
    val videoMapPaths: List<String?>? = null,

    @SerializedName("videoPaths")
    val videoPaths: List<VideoPaths>? = null,
)

data class VideoPaths(
    @SerializedName("thumbUrl")
    val thumbUrl: String?,

    @SerializedName("middleThumbUrl")
    val middleThumbUrl: String?,

    @SerializedName("mediaUrl")
    val mediaUrl: String
)
