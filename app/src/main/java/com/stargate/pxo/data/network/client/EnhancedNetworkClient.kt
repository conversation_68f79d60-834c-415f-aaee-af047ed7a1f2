package com.stargate.pxo.data.network.client

import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.exception.NetworkException
import com.stargate.pxo.data.network.model.BaseResponse
import com.stargate.pxo.data.network.storage.DataRepository
import com.stargate.pxo.data.network.storage.FetchStrategy
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.HttpException
import retrofit2.Response
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 增强的网络客户端
 * 使用统一的Resource封装和DataRepository进行缓存
 */
@Singleton
class EnhancedNetworkClient @Inject constructor(
    private val dataRepository: DataRepository
) {
    
    companion object {
        private const val TAG = "EnhancedNetworkClient"
    }
    
    /**
     * 执行API请求并返回Flow<Resource<T>>
     */
    fun <T : Any> execute(
        apiCall: suspend () -> BaseResponse<T>,
        retryCount: Int = 0,
        retryDelay: Long = 1000
    ): Flow<Resource<T>> = flow {
        // 发送加载状态
        emit(Resource.loading())
        
        try {
            val result = executeWithRetry(retryCount, retryDelay) { 
                // 执行网络请求
                val response = apiCall()
                
                // 处理响应
                handleResponse(response)
            }
            
            emit(result)
            } catch (e: Exception) {
                    emit(handleException<T>(e))
        }
    }
    
    /**
     * 执行API请求并直接返回结果（不通过Flow）
     */
    suspend fun <T : Any> executeSync(
        apiCall: suspend () -> BaseResponse<T>,
        retryCount: Int = 0,
        retryDelay: Long = 1000
    ): Resource<T> {
        return try {
            executeWithRetry(retryCount, retryDelay) { 
                // 执行网络请求
                val response = apiCall()
                
                // 处理响应
                handleResponse(response)
            }
            } catch (e: Exception) {
            handleException(e)
                }
    }
    
    /**
     * 执行带缓存的API请求
     * 
     * 对于需要精确类型信息的场景，请考虑使用带有type参数的重载方法。
     */
    fun <T : Any> executeWithCache(
        cacheKey: String,
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST,
        expireTime: Long = 30,
        timeUnit: TimeUnit = TimeUnit.MINUTES,
        retryCount: Int = 0,
        retryDelay: Long = 1000,
        apiCall: suspend () -> BaseResponse<T>
    ): Flow<Resource<T>> {
        return dataRepository.executeWithCache(
            key = cacheKey,
            fetchStrategy = fetchStrategy,
            expireTime = expireTime,
            timeUnit = timeUnit
        ) {
            try {
                executeWithRetry(retryCount, retryDelay) {
                    // 执行网络请求
                    val response = apiCall()
                    val result = handleResponse(response)
                    
                    if (result is Resource.Error) {
                        LogUtil.e(TAG, "API error: ${result.message}")
                            throw Exception(result.message)
                    } else if (result is Resource.Success) {
                        // 成功时，直接返回数据对象（不是Resource对象）
                        result.data
                    } else {
                        LogUtil.e(TAG, "Unexpected resource state")
                            throw Exception("Unexpected resource state")
                        }
                    }
                } catch (e: Exception) {
                LogUtil.e(TAG, "Error executing request: ${e.message}")
                        throw e
            }
        }
    }
    
    /**
     * 执行带缓存的API请求（显式指定类型）
     *
     * 此方法适用于需要精确类型信息的场景。
     *
     * 示例：
     * ```
     * networkClient.executeWithCacheTyped(
     *     cacheKey = "user_profile",
     *     type = UserProfile::class.java
     * ) {
     *     apiService.getUserProfile()
     * }
     * ```
     */
    fun <T : Any> executeWithCacheTyped(
        cacheKey: String,
        fetchStrategy: FetchStrategy = FetchStrategy.CACHE_FIRST,
        expireTime: Long = 30,
        timeUnit: TimeUnit = TimeUnit.MINUTES,
        retryCount: Int = 0,
        retryDelay: Long = 1000,
        type: Class<T>,
        apiCall: suspend () -> BaseResponse<T>
    ): Flow<Resource<T>> {
        return dataRepository.executeWithCacheAndType(
            key = cacheKey,
            fetchStrategy = fetchStrategy,
            expireTime = expireTime,
            timeUnit = timeUnit,
            type = type,
            networkCall = {
                try {
                    executeWithRetry(retryCount, retryDelay) {
                        // 执行网络请求
                        val response = apiCall()
                        val result = handleResponse(response)

                        if (result is Resource.Error) {
                            LogUtil.e(TAG, "API error: ${result.message}")
                                throw Exception(result.message)
                        } else if (result is Resource.Success) {
                            // 成功时，直接返回数据对象（不是Resource对象）
                            result.data
                        } else {
                            LogUtil.e(TAG, "Unexpected resource state")
                                throw Exception("Unexpected resource state")
                            }
                        }
                    } catch (e: Exception) {
                    LogUtil.e(TAG, "Error executing request: ${e.message}")
                            throw e
                }
            }
        )
    }


    /**
     * 处理网络响应
     */
    private fun <T : Any> handleResponse(response: BaseResponse<T>): Resource<T> {
        if (!response.isSuccess) {
            // HTTP错误
            val errorMessage = response.msg.ifEmpty { "Unknown error" }
            return Resource.error(
                message = errorMessage,
                code = response.code,
                exception = NetworkException.HttpError(response.code, errorMessage)
            )
        }
        
        // 获取响应体
        val body = response.data

        return if (body != null) {
            // 业务成功，直接使用响应数据，避免不安全的类型转换
            Resource.success(body)
        } else {
            // 响应体为空
            Resource.error(
                message = "Empty response body",
                code = -1,
                exception = NetworkException.ParseError("Empty response body")
            )
        }
    }
    
    /**
     * 处理网络异常
     */
    private fun <T : Any> handleException(exception: Throwable): Resource<T> {
        return when (exception) {
            // HTTP异常
            is HttpException -> {
                val code = exception.code()
                val message = exception.message() ?: "HTTP Error"
                Resource.error(
                    message = message,
                    code = code,
                    exception = NetworkException.HttpError(code, message)
                )
            }
            
            // 连接超时
            is SocketTimeoutException -> {
                Resource.error(
                    message = "Connection timed out",
                    code = NetworkException.ERROR_TIMEOUT,
                    exception = NetworkException.TimeoutError(cause = exception)
                )
            }
            
            // 未知主机
            is UnknownHostException -> {
                Resource.error(
                    message = "Network unavailable",
                    code = NetworkException.ERROR_NETWORK,
                    exception = NetworkException.NetworkError(cause = exception)
                )
            }
            
            // IO异常
            is IOException -> {
                Resource.error(
                    message = exception.message ?: "Network error",
                    code = NetworkException.ERROR_NETWORK,
                    exception = NetworkException.NetworkError(
                        message = exception.message ?: "Network error",
                        cause = exception
                    )
                )
            }
            
            // 其他异常
            else -> {
                Resource.error(
                    message = exception.message ?: "Unknown error",
                    code = NetworkException.ERROR_UNKNOWN,
                    exception = NetworkException.UnknownError(
                        message = exception.message ?: "Unknown error",
                        cause = exception
                    )
                )
            }
        }
    }

    /**
     * 带重试逻辑的执行函数
     * 
     * @param retryCount 重试次数
     * @param retryDelay 初始重试延迟（毫秒）
     * @param block 要执行的代码块
     */
    private suspend fun <T> executeWithRetry(
        retryCount: Int,
        retryDelay: Long,
        block: suspend () -> T
    ): T {
        var currentDelay = retryDelay
        var lastException: Exception? = null
        
        for (attempt in 0..retryCount) {
            try {
                // 如果不是第一次尝试，则延迟
                if (attempt > 0) {
                    delay(currentDelay)
                    currentDelay = (currentDelay * 1.5).toLong() // 指数退避
                }
                
                // 执行代码块
                return block()
            } catch (e: Exception) {
                lastException = e
                // 如果是最后一次尝试，则抛出异常
                if (attempt == retryCount) {
                    throw e
                }
                // 否则记录异常并继续重试
                LogUtil.w(TAG, "Attempt ${attempt+1} failed: ${e.message}. Retrying...")
            }
        }
        
        // 理论上不会执行到这里
        throw lastException ?: Exception("Unknown error")
    }
} 