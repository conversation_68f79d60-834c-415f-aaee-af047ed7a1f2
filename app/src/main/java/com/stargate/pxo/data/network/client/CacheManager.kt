package com.stargate.pxo.data.network.client

import android.content.Context
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.io.File
import java.lang.reflect.Type
import java.util.concurrent.TimeUnit

/**
 * 缓存管理器
 * 用于缓存API响应数据
 */
class CacheManager(context: Context) {
    
    /**
     * 缓存目录
     */
    val cacheDir: File = File(context.cacheDir, "api_cache")
    
    /**
     * JSON序列化工具
     */
    private val gson = Gson()
    
    init {
        // 确保缓存目录存在
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
    }
    
    /**
     * 保存数据到缓存
     */
    fun <T> saveToCache(key: String, data: T) {
        try {
            val cacheFile = File(cacheDir, generateFileName(key))
            val json = gson.toJson(data)
            cacheFile.writeText(json)
            
            // 保存缓存元数据
            val metadataFile = File(cacheDir, "${generateFileName(key)}.meta")
            val metadata = CacheMetadata(System.currentTimeMillis())
            metadataFile.writeText(gson.toJson(metadata))
        } catch (e: Exception) {
            // 缓存失败，忽略异常
        }
    }
    
    /**
     * 从缓存中读取数据
     */
    fun <T> getFromCache(key: String, type: Type): T? {
        try {
            val cacheFile = File(cacheDir, generateFileName(key))
            if (!cacheFile.exists()) {
                return null
            }
            
            val json = cacheFile.readText()
            return gson.fromJson(json, type)
        } catch (e: Exception) {
            // 读取缓存失败，返回null
            return null
        }
    }
    
    /**
     * 从缓存中读取数据（使用内联泛型）
     */
    inline fun <reified T> getFromCache(key: String): T? {
        return getFromCache(key, object : TypeToken<T>() {}.type)
    }
    
    /**
     * 检查缓存是否过期
     */
    fun isCacheExpired(key: String, maxAge: Long, timeUnit: TimeUnit): Boolean {
        try {
            val metadataFile = File(cacheDir, "${generateFileName(key)}.meta")
            if (!metadataFile.exists()) {
                return true
            }
            
            val json = metadataFile.readText()
            val metadata = gson.fromJson(json, CacheMetadata::class.java)
            val currentTime = System.currentTimeMillis()
            val expirationTime = metadata.timestamp + timeUnit.toMillis(maxAge)
            
            return currentTime > expirationTime
        } catch (e: Exception) {
            // 读取元数据失败，视为缓存过期
            return true
        }
    }
    
    /**
     * 清除指定缓存
     */
    fun clearCache(key: String) {
        val cacheFile = File(cacheDir, generateFileName(key))
        val metadataFile = File(cacheDir, "${generateFileName(key)}.meta")
        
        cacheFile.delete()
        metadataFile.delete()
    }
    
    /**
     * 清除所有缓存
     */
    fun clearAllCache() {
        cacheDir.listFiles()?.forEach { it.delete() }
    }
    
    /**
     * 生成缓存文件名
     */
    fun generateFileName(key: String): String {
        return key.hashCode().toString()
    }
    
    /**
     * 缓存元数据
     */
    private data class CacheMetadata(
        val timestamp: Long
    )
    
    /**
     * 缓存策略
     */
    sealed class CacheStrategy {
        /**
         * 优先使用缓存，如果缓存不存在或过期则请求网络
         */
        class CacheFirst(val maxAge: Long, val timeUnit: TimeUnit) : CacheStrategy()
        
        /**
         * 优先请求网络，如果网络请求失败则使用缓存
         */
        class NetworkFirst(val maxAge: Long, val timeUnit: TimeUnit) : CacheStrategy()
        
        /**
         * 同时从缓存和网络获取数据，先返回缓存数据，然后返回网络数据
         */
        class CacheThenNetwork(val maxAge: Long, val timeUnit: TimeUnit) : CacheStrategy()
        
        /**
         * 只使用网络数据，但将结果缓存
         */
        object NetworkOnly : CacheStrategy()
        
        /**
         * 只使用缓存数据，不请求网络
         */
        object CacheOnly : CacheStrategy()
    }
    
    /**
     * 执行带缓存策略的请求
     */
    inline fun <reified T> executeWithCache(
        key: String,
        strategy: CacheStrategy,
        crossinline networkCall: suspend () -> T
    ): Flow<T> = flow {
        when (strategy) {
            is CacheStrategy.CacheFirst -> {
                // 检查缓存
                val cachedData = getFromCache<T>(key)
                val isCacheValid = cachedData != null && !isCacheExpired(key, strategy.maxAge, strategy.timeUnit)
                
                if (isCacheValid && cachedData != null) {
                    // 缓存有效，直接返回
                    emit(cachedData)
                } else {
                    // 缓存无效，请求网络
                    val networkData = networkCall()
                    saveToCache(key, networkData)
                    emit(networkData)
                }
            }
            
            is CacheStrategy.NetworkFirst -> {
                try {
                    // 优先请求网络
                    val networkData = networkCall()
                    saveToCache(key, networkData)
                    emit(networkData)
                } catch (e: Exception) {
                    // 网络请求失败，尝试使用缓存
                    val cachedData = getFromCache<T>(key)
                    if (cachedData != null) {
                        emit(cachedData)
                    } else {
                        // 缓存也不可用，抛出原始异常
                        throw e
                    }
                }
            }
            
            is CacheStrategy.CacheThenNetwork -> {
                // 先尝试返回缓存
                val cachedData = getFromCache<T>(key)
                if (cachedData != null) {
                    emit(cachedData)
                }
                
                // 然后请求网络
                val networkData = networkCall()
                saveToCache(key, networkData)
                emit(networkData)
            }
            
            is CacheStrategy.NetworkOnly -> {
                // 只请求网络，但缓存结果
                val networkData = networkCall()
                saveToCache(key, networkData)
                emit(networkData)
            }
            
            is CacheStrategy.CacheOnly -> {
                // 只使用缓存
                val cachedData = getFromCache<T>(key)
                if (cachedData != null) {
                    emit(cachedData)
                } else {
                    throw Exception("No cache available for key: $key")
                }
            }
        }
    }
} 