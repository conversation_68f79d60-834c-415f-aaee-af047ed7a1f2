package com.stargate.pxo.data.network.client

import com.stargate.pxo.data.network.exception.NetworkException
import okhttp3.Interceptor
import okhttp3.Response
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 全局错误处理拦截器
 * 用于统一处理网络请求中的错误
 */
class GlobalErrorHandler : Interceptor {
    
    /**
     * 错误处理回调接口
     */
    interface ErrorHandler {
        /**
         * 处理网络错误
         */
        fun onNetworkError(exception: NetworkException.NetworkError)
        
        /**
         * 处理超时错误
         */
        fun onTimeoutError(exception: NetworkException.TimeoutError)
        
        /**
         * 处理HTTP错误
         */
        fun onHttpError(exception: NetworkException.HttpError)
        
        /**
         * 处理服务器错误
         */
        fun onServerError(exception: NetworkException.ServerError)
        
        /**
         * 处理未知错误
         */
        fun onUnknownError(exception: NetworkException.UnknownError)
    }
    
    /**
     * 错误处理器
     */
    private var errorHandler: ErrorHandler? = null
    
    /**
     * 设置错误处理器
     */
    fun setErrorHandler(handler: ErrorHandler) {
        this.errorHandler = handler
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        
        try {
            val response = chain.proceed(request)
            
            // 处理HTTP错误
            if (!response.isSuccessful) {
                val code = response.code
                val message = response.message
                
                // 5xx错误视为服务器错误
                if (code in 500..599) {
                    errorHandler?.onServerError(
                        NetworkException.ServerError("Server error: $code $message")
                    )
                } else {
                    errorHandler?.onHttpError(
                        NetworkException.HttpError(code, "HTTP error: $code $message")
                    )
                }
            }
            
            return response
        } catch (e: Exception) {
            // 处理各种网络异常
            when (e) {
                is SocketTimeoutException -> {
                    val exception = NetworkException.TimeoutError(cause = e)
                    errorHandler?.onTimeoutError(exception)
                    throw exception
                }
                is UnknownHostException -> {
                    val exception = NetworkException.NetworkError(cause = e)
                    errorHandler?.onNetworkError(exception)
                    throw exception
                }
                else -> {
                    val exception = NetworkException.UnknownError(
                        message = e.message ?: "Unknown error",
                        cause = e
                    )
                    errorHandler?.onUnknownError(exception)
                    throw exception
                }
            }
        }
    }
} 