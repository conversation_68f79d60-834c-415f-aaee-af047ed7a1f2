package com.stargate.pxo.data.network.api

import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.model.AppConfigData
import com.stargate.pxo.data.network.model.BaseResponse
import com.stargate.pxo.data.network.model.LoginResponse
import com.stargate.pxo.data.network.model.StrategyResponse
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface AppNetInterfaceInfo {

    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getAppConfig(
        @Url url: String = PuxxiUrl.CONFIG_GETAPPCONFIGPOSTV2,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<AppConfigData>


    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun getStrategyPostV2(
        @Url url: String = PuxxiUrl.CONFIG_GETSTRATEGYPOSTV2,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<StrategyResponse>


    /**
     * 用户登录
     */
    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun login(
        @Url url: String = PuxxiUrl.SECURITY_OAUTH,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<LoginResponse>



}