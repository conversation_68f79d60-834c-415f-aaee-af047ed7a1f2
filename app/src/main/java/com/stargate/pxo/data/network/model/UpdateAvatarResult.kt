package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

data class UpdateAvatarResult (
    @SerializedName("mediaId")
    val mediaId: String,
    
    @SerializedName("userId")
    val userId: String,
    
    @SerializedName("mediaType")
    val mediaType: String,
    
    @SerializedName("mediaPath")
    val mediaPath: String,
    
    @SerializedName("thumbUrl")
    val thumbUrl: String,
    
    @SerializedName("middleThumbUrl")
    val middleThumbUrl: String,
    
    @SerializedName("mediaUrl")
    val mediaUrl: String,
    
    @SerializedName("sort")
    val sort: Int,
    
    @SerializedName("coins")
    val coins: Int,
  )

