# Puxxi 网络与缓存框架

一个高效、灵活的Android网络请求和数据缓存框架，支持多级缓存策略和DSL风格API。

## 特性

- **DSL风格API**：简洁直观的链式调用
- **多级缓存**：内存缓存 + 数据库持久化
- **灵活的缓存策略**：支持多种数据获取策略
- **类型安全**：完全利用Kotlin类型系统
- **响应式编程**：基于Kotlin Flow
- **统一的错误处理**：标准化的错误处理机制
- **自动重试**：支持失败重试和指数退避

## 快速开始

### 1. 网络请求

使用DSL风格API进行网络请求：

```kotlin
viewModelScope.launch {
    networkClient.request<UserProfile> {
        api { apiService.getUserProfile() }
        cache {
            key = CacheKeys.User.profile()
            strategy = FetchStrategy.CACHE_FIRST
            expireTime = 30
            timeUnit = TimeUnit.MINUTES
        }
        retry {
            count = 2
            delay = 1000
        }
        onStart { showLoading() }
        onSuccess { profile -> 
            hideLoading()
            updateUI(profile) 
        }
        onError { error -> 
            hideLoading()
            showError(error.message) 
        }
    }.collect()
}
```

### 2. 数据缓存操作

使用DSL风格API进行数据缓存操作：

```kotlin
// 获取缓存数据
viewModelScope.launch {
    dataRepository.fetch<UserProfile> {
        key = CacheKeys.User.profile()
        strategy = FetchStrategy.CACHE_FIRST
        remote {
            // 远程获取逻辑，在缓存不可用时调用
            apiService.getUserProfile().body()?.data
        }
        onSuccess { profile -> updateUI(profile) }
        onError { error -> showError(error.message) }
    }.collect()
}

// 保存数据到缓存
viewModelScope.launch {
    dataRepository.store(userProfile) {
        key = CacheKeys.User.profile()
        expireTime = 30
        timeUnit = TimeUnit.MINUTES
    }
}
```

## 缓存策略

框架支持以下缓存策略：

- **CACHE_ONLY**：只从缓存获取，缓存不可用时返回错误
- **REMOTE_ONLY**：只从远程获取，忽略缓存
- **CACHE_FIRST**：优先从缓存获取，缓存不可用时从远程获取
- **REMOTE_FIRST**：优先从远程获取，远程获取失败时尝试从缓存获取
- **CACHE_AND_REMOTE**：先返回缓存数据，然后请求远程更新（双重更新）

## 高级用法

### 1. 自定义缓存键

使用`CacheKeys`工具类生成标准化的缓存键：

```kotlin
// 预定义的缓存键
val profileKey = CacheKeys.User.profile("user123")
val settingsKey = CacheKeys.User.settings()
val articleKey = CacheKeys.Content.detail("article456")
val feedKey = CacheKeys.Content.feed("hot")

// 自定义缓存键
val customKey = CacheKeys.custom("payment", "history", userId, date)
```

### 2. 直接使用底层API

对于需要更多控制的场景，可以直接使用底层API：

```kotlin
// 使用EnhancedNetworkClient
val result = networkClient.executeSync(
    apiCall = { apiService.getUserProfile() }
)

// 使用DataRepository
dataRepository.getWithType<UserProfile>(
    key = "user_profile",
    fetchStrategy = FetchStrategy.CACHE_FIRST
).collect { resource ->
    when (resource) {
        is Resource.Success -> handleSuccess(resource.data)
        is Resource.Error -> handleError(resource.message)
        is Resource.Loading -> showLoading()
    }
}
```

## 最佳实践

1. **使用Repository模式**：在Repository层中使用此框架，对ViewModel隐藏实现细节
2. **合理设置缓存过期时间**：根据数据的实时性要求设置合适的过期时间
3. **使用标准化的缓存键**：始终使用`CacheKeys`工具类生成缓存键
4. **处理所有状态**：确保处理加载、成功和错误状态
5. **避免重复请求**：合理使用缓存策略避免不必要的网络请求

## 架构说明

该框架基于以下核心组件：

- **Resource**：统一的数据包装类，表示加载、成功或错误状态
- **EnhancedNetworkClient**：增强的网络客户端，处理网络请求和错误重试
- **DataRepository**：数据仓库接口，定义数据存取操作
- **MemoryDataStorage**：内存数据存储实现
- **RoomDataStorage**：基于Room的持久化存储实现
- **MultiLevelDataStorage**：多级缓存实现，结合内存和数据库存储

## 扩展与定制

框架设计为可扩展的，可以通过以下方式进行定制：

1. 实现自定义的`DataStorage`接口
2. 扩展`Resource`类添加更多功能
3. 创建特定领域的Repository扩展
4. 自定义缓存策略和过期机制