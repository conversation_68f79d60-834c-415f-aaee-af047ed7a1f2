package com.stargate.pxo.data.network.storage.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

/**
 * 缓存数据库
 * Room数据库实现，用于持久化存储缓存数据
 */
@Database(entities = [CacheEntity::class], version = 1, exportSchema = false)
abstract class CacheDatabase : RoomDatabase() {
    
    /**
     * 获取缓存数据访问对象
     */
    abstract fun cacheDao(): CacheDao
    
    companion object {
        private const val DATABASE_NAME = "puxxi_cache.db"
        
        @Volatile
        private var INSTANCE: CacheDatabase? = null
        
        /**
         * 获取数据库实例，如果不存在则创建
         */
        fun getInstance(context: Context): CacheDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CacheDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration() // 在升级数据库时简单地删除并重建表
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
} 