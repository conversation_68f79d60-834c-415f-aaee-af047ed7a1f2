package com.stargate.pxo.data.db.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.stargate.pxo.data.db.entity.UserEntity
import kotlinx.coroutines.flow.Flow

/**
 * 用户数据访问对象
 */
@Dao
interface UserDao {
    
    /**
     * 插入用户信息，如果已存在则替换
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(user: UserEntity)
    
    /**
     * 更新用户信息
     */
    @Update
    suspend fun update(user: UserEntity)
    
    /**
     * 插入或更新用户信息
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdate(user: UserEntity)
    
    /**
     * 根据用户ID获取用户信息
     */
    @Query("SELECT * FROM users WHERE userId = :userId")
    suspend fun getUserById(userId: String): UserEntity?
    
    /**
     * 根据用户ID获取用户信息Flow
     */
    @Query("SELECT * FROM users WHERE userId = :userId")
    fun getUserFlow(userId: String): Flow<UserEntity?>
    
    /**
     * 获取所有用户信息
     */
    @Query("SELECT * FROM users")
    suspend fun getAllUsers(): List<UserEntity>
    
    /**
     * 获取所有用户信息Flow
     */
    @Query("SELECT * FROM users")
    fun getAllUsersFlow(): Flow<List<UserEntity>>
    
    /**
     * 根据用户ID删除用户信息
     */
    @Query("DELETE FROM users WHERE userId = :userId")
    suspend fun deleteUserById(userId: String)
    
    /**
     * 删除所有用户信息
     */
    @Query("DELETE FROM users")
    suspend fun deleteAllUsers()
    
    /**
     * 获取用户数量
     */
    @Query("SELECT COUNT(*) FROM users")
    suspend fun getUserCount(): Int
    
    /**
     * 根据昵称搜索用户
     */
    @Query("SELECT * FROM users WHERE nickname LIKE '%' || :keyword || '%'")
    suspend fun searchUsersByNickname(keyword: String): List<UserEntity>
    
    /**
     * 获取最近更新的用户
     */
    @Query("SELECT * FROM users ORDER BY lastUpdateTime DESC LIMIT :limit")
    suspend fun getRecentUpdatedUsers(limit: Int): List<UserEntity>
} 