package com.stargate.pxo.data.network.exception

import java.io.IOException

/**
 * 网络异常基类
 */
sealed class NetworkException(
    override val message: String,
    val errorCode: Int,
    override val cause: Throwable? = null
) : IOException(message, cause) {
    
    /**
     * 服务器返回的业务错误
     */
    class ApiError(
        message: String,
        val code: Int
    ) : NetworkException(message, code)
    
    /**
     * HTTP错误
     */
    class HttpError(
        val httpCode: Int,
        message: String
    ) : NetworkException(message, httpCode)
    
    /**
     * 网络连接错误
     */
    class NetworkError(
        message: String = "Network connection failed",
        cause: Throwable? = null
    ) : NetworkException(message, ERROR_NETWORK, cause)
    
    /**
     * 请求超时错误
     */
    class TimeoutError(
        message: String = "Request timed out",
        cause: Throwable? = null
    ) : NetworkException(message, ERROR_TIMEOUT, cause)
    
    /**
     * 服务器内部错误
     */
    class ServerError(
        message: String = "Server internal error",
        cause: Throwable? = null
    ) : NetworkException(message, ERROR_SERVER, cause)
    
    /**
     * 数据解析错误
     */
    class ParseError(
        message: String = "Data parsing failed",
        cause: Throwable? = null
    ) : NetworkException(message, ERROR_PARSE, cause)
    
    /**
     * 未知错误
     */
    class UnknownError(
        message: String = "Unknown error occurred",
        cause: Throwable? = null
    ) : NetworkException(message, ERROR_UNKNOWN, cause)
    
    companion object {
        /**
         * 错误码定义
         */
        const val ERROR_NETWORK = -100
        const val ERROR_TIMEOUT = -101
        const val ERROR_SERVER = -102
        const val ERROR_PARSE = -103
        const val ERROR_UNKNOWN = -999
    }
} 