package com.stargate.pxo.data.network

/**
 * 网络请求结果封装类
 * 用于统一处理网络请求的不同状态
 */
sealed class ApiResult<out T> {
    /**
     * 请求成功，包含响应数据
     */
    data class Success<T>(val data: T) : ApiResult<T>()
    
    /**
     * 请求失败，包含错误信息
     */
    data class Error(
        val code: Int = -1,
        val message: String = "Unknown error",
        val exception: Throwable? = null
    ) : ApiResult<Nothing>()
    
    /**
     * 请求加载中
     */
    object Loading : ApiResult<Nothing>()
    
    /**
     * 判断是否成功
     */
    val isSuccess: Boolean get() = this is Success
    
    /**
     * 判断是否失败
     */
    val isError: Boolean get() = this is Error
    
    /**
     * 判断是否加载中
     */
    val isLoading: Boolean get() = this is Loading
    
    /**
     * 获取成功数据，如果不是成功状态则返回null
     */
    fun getOrNull(): T? = when (this) {
        is Success -> data
        else -> null
    }
    
    /**
     * 获取错误信息，如果不是错误状态则返回null
     */
    fun errorOrNull(): Error? = when (this) {
        is Error -> this
        else -> null
    }
    
    /**
     * 转换数据类型
     */
    fun <R> map(transform: (T) -> R): ApiResult<R> = when (this) {
        is Success -> Success(transform(data))
        is Error -> this
        is Loading -> Loading
    }
    
    companion object {
        /**
         * 创建成功结果
         */
        fun <T> success(data: T): ApiResult<T> = Success(data)
        
        /**
         * 创建错误结果
         */
        fun error(code: Int = -1, message: String, exception: Throwable? = null): ApiResult<Nothing> = 
            Error(code, message, exception)
        
        /**
         * 创建加载中结果
         */
        fun loading(): ApiResult<Nothing> = Loading
    }
} 