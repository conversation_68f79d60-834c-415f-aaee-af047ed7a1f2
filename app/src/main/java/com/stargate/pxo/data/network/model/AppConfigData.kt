package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

data class AppConfigData(
    @SerializedName("abc_k1")
    val abc_k1: String?,
    
    @SerializedName("source")
    val source: String?,
    
    @SerializedName("abc_k3")
    val abc_k3: String?,
    
    @SerializedName("lang")
    val lang: String?,
    
    @SerializedName("isWebApp")
    val isWebApp: Boolean?,
    
    @SerializedName("abc_k2")
    val abc_k2: String?,
    
    @SerializedName("abc_k4")
    val abc_k4: String?
)