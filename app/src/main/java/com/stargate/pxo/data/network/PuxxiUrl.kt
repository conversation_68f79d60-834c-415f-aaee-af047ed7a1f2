package com.stargate.pxo.data.network

/**
 * 接口URL常量类
 * 根据环境参数直接返回对应的URL，不使用映射方式
 */
object PuxxiUrl {
    
    // 是否使用混淆URL（true=混淆URL，false=真实URL）
    private var useObfuscatedUrl = false
    
    /**
     * 设置URL模式
     * @param useObfuscated true=使用混淆URL，false=使用真实URL
     */
    fun setUrlMode(useObfuscated: Boolean) {
        useObfuscatedUrl = useObfuscated
    }
    
    /**
     * 获取当前URL模式
     */
    fun isUsingObfuscatedUrl(): Boolean = useObfuscatedUrl
    
    val BROADCASTER_GETCUSTOMIZEPRICEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/doc_max_value/del" else "/broadcaster/getCustomizePricePostV2"
    
    val CONFIG_GETDEFAULTAVATARINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_last_group/post" else "/config/getDefaultAvatarInfo"
    
    val DRESSUP_USER_ON: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userorder_minimum_list/list" else "/dressup/user/on"
    
    val USER_CONSUMED_POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/userorder_max_key/get" else "/user/consumed/postV2"
    
    val LIVEINTERACTIVEGIFT_TEMPLATE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/promotion_average_price/post" else "/LiveInteractiveGift/template/list"
    
    val USER_GETUSERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_first_group/patch" else "/user/getUserInfo"
    
    val VIDEOCALLV2_POPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userinfo_rank/list" else "/videoCallV2/popUp"

    val BROADCASTER_NEWUSERTASK_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/harddisk_minimum_group/get" else "/broadcaster/newUserTask/delete"

    val CALLBACK_ZEGO_CLOUDPLAYER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/msg_table/set" else "/callback/zego/cloudPlayer"

    val BROADCASTER_GROUPMESSAGESEND: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/config_top_hash/delete" else "/broadcaster/groupMessageSend"

    val USER_GAME_SIGN: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_average_key/add" else "/user/game/sign"

    val USER_UNLOCKMASK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_top_price/get" else "/user/unlockMask"

    val USER_GETWATCHADSCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/coin_price/patch" else "/user/getWatchAdsConfig"

    val VIDEOCALLV2_PICKUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/mem_array/list" else "/videoCallV2/pickUp"

    val SHOWROOMUSER_STATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_minimum_table/remove" else "/showRoomUser/status"

    val GIFT_GIFTCALLGIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/message_min_result/update" else "/gift/giftCallGive"

    val DRESSUP_USER_SETS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_last_result/get" else "/dressup/user/sets"

    val TEST_NIUBI: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/goods_group/del" else "/test/niubi"

    val USER_LEVELPOWER_UPDATESPECIALEFFECTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_first_item/upload" else "/user/levelPower/updateSpecialEffects"

    val BROADCASTER_INVITATION_INFO_V2: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_min_result/post" else "/broadcaster/invitation/info/v2"

    val USER_FREEUSERWATCHADSAWARD: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userinfo_first_hash/download" else "/user/freeUserWatchAdsAward"

    val MG_ROOM_MIC_SPEAK_HANDLE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_first_json/add" else "/mg-room/mic/speak/handle"

    val BROADCASTER_FREECALLPOPUPCLOSE: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_second_json/post" else "/broadcaster/freeCallPopupClose"

    val ACTIVITY_GETROULETTEPARTAWARD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/disk_min_value/download" else "/activity/getRoulettePartAward"

    val REVIEW_IOS_BERRYLIVE_VIDEOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userorder_second_json/remove" else "/review/ios/berrylive/videoPostV2"

    val USER_ISFOLLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_maximum_item/search" else "/user/isFollow"

    val MG_ROOM_COINTASKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_min_result/set" else "/mg-room/coinTaskInfo"

    val USER_GETUSERLISTONLINESTATUSFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/goods_last_hash/upload" else "/user/getUserListOnlineStatusForH5"

    val COIN_GIVEINDIACALLCARD: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_rank/set" else "/coin/giveIndiaCallCard"

    val GIFT_LIVE_LIST_V2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/link_min_group/list" else "/gift/live/list/v2"

    val VIDEO_CALL_DURATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/msg_minimum_map/list" else "/video-call/duration"

    val GIFT_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/money_maximum_table/patch" else "/gift/listPostV2"

    val SHOWROOM_ROOMDAYRANKTOPPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_top_string/update" else "/showRoom/roomDayRankTopPostV2"

    val GAME_BANNER_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_min_table/list" else "/game/banner/info"

    val COIN_RECHARGE_BROADCASTERINVITATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_top_hash/add" else "/coin/recharge/broadcasterInvitation"

    val INFO_FLOW_GETUSERLISTNEWINFOFLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/coin_average_list/set" else "/info-flow/getUserListNewInfoFlow"

    val EVENT_SHOPPING_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/userinfo_min_json/delete" else "/event/shopping/submit"

    val BROADCASTER_DAYSTATISTIC: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_second_list/update" else "/broadcaster/dayStatistic"

    val USER_RECOMMENDBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_minimum_result/search" else "/user/RecommendBroadcaster"

    val BROADCASTER_CHECKBROADCASTERCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/gift_average_array/post" else "/broadcaster/checkBroadcasterConfig"

    val GIFT_SPECIALEFFECTSLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/agent_max_array/post" else "/gift/specialEffectsList"

    val TEST_FIELD_ENCRYPT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/memory_minimum_item/upload" else "/test/field/encrypt"

    val BROADCASTERVIDEO_TEACH_LANGUAGELISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/promotion_second_item/upload" else "/broadcasterVideo/teach/languageListPostV2"

    val USER_LISTRELATEUSERINFOS: String
        get() = if (useObfuscatedUrl) "/api_prod/user_maximum_hash/set" else "/user/listRelateUserInfos"

    val GAME_SLOTMACHINE_RANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/coin_max_value/set" else "/game/slotMachine/rank"

    val BROADCASTER_IMINCENTIVE_RECEIVECOINS: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_max_rank/delete" else "/broadcaster/IMIncentive/receiveCoins"

    val LIVE_ACTIVITY_CONFESSIONWALL_PAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/product_max_result/upload" else "/live/activity/confessionWall/page"

    val COIN_RECHARGE_CHECKBROADCASTERINVITATIONPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_last_list/set" else "/coin/recharge/checkBroadcasterInvitationPostV2"

    val USER_GUARDIANRANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/msg_max_string/add" else "/user/guardianRank"

    val UNITY_USER_UPDATEUSEREVALUATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/game_second_value/get" else "/unity/user/updateUserEvaluation"

    val SHOWROOM_GETCONSECUTIVESTREAMING: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_second_string/upload" else "/showRoom/getConsecutiveStreaming"

    val USER_VISITOR_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/document_second_price/remove" else "/user/visitor/listPostV2"

    val VIDEO_CALL_BROADCASTER_SETTLEMENTINCOME: String
        get() = if (useObfuscatedUrl) "/api_prod/message_group/remove" else "/video-call/broadcaster/settlementIncome"

    val TEST_IMG_POLICY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_max_item/patch" else "/test/img/policy"

    val RISK_INFO_UPLOAD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_first_table/download" else "/risk/info/upload"

    val GIFT_LIVE_LIST_V3: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_maximum_list/set" else "/gift/live/list/v3"

    val COIN_GETINDIACALLCARDINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/mem_max_rank/remove" else "/coin/getIndiaCallCardInfo"

    val DRESSUP_USER_OFF: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_value/del" else "/dressup/user/off"

    val COMMON_APPLOG_UPLOAD: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_last_list/add" else "/common/appLog/upload"

    val USER_BROADCASTERCHANGEMASK: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/doc_second_array/remove" else "/user/broadcasterChangeMask"

    val COMMON_USER_IDLE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/goods_top_table/set" else "/common/user/idle"

    val SOUL_USER_SWITCHIMTIPSHOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/report_minimum_price/search" else "/soul/user/switchImTipShow"

    val SHOWROOMUSER_LIVEGIFTLISTTHISSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/device_last_result/download" else "/showRoomUser/liveGiftListThisSession"

    val SHOWROOMUSER_RECOMMENDS: String
        get() = if (useObfuscatedUrl) "/api_prod/followlist_value/delete" else "/showRoomUser/recommends"

    val USER_INVITATION_GETCODE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_maximum_item/upload" else "/user/invitation/getCode"

    val USER_GUARDIANBANNER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_first_key/set" else "/user/guardianBanner"

    val BROADCASTER_STICKTOP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/config_top_list/update" else "/broadcaster/stickTop"

    val SHOWROOM_STATISTICS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/game_first_value/search" else "/showRoom/statistics"

    val INFO_FLOW_TOPOPTION: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/order_max_result/patch" else "/info-flow/topOption"

    val COIN_TPPRECHARGE_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_top_result/list" else "/coin/tppRecharge/info"

    val USER_INCENTIVE_GET_DETAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_first_array/set" else "/user/incentive/get/detail"

    val DRESSUP_CENTER_SETS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/document_min_group/delete" else "/dressup/center/sets"

    val MG_ROOM_CALLPKGINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_average_list/del" else "/mg-room/callPkgInfo"

    val BROADCASTER_AUTOCALL_TRIGGER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/goods_last_price/del" else "/broadcaster/autoCall/trigger"

    val MG_ROOM_BOSS_ENTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_min_rank/get" else "/mg-room/boss/enter"

    val API_CHANNEL_RECHARGEORDERCREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_last_list/get" else "/api/channel/rechargeOrderCreate"

    val ADS_DESCUNLOCKTIMES: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/followlist_min_group/list" else "/ads/descUnlockTimes"

    val USER_INVITATION_CODEANDAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_second_map/search" else "/user/invitation/codeAndAvatar"

    val ACTIVITY_GETGIFTSCORESUMMARY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_maximum_value/search" else "/activity/getGiftScoreSummary"

    val BROADCASTER_GROUP_GETMESSAGENUM: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_max_string/get" else "/broadcaster/group/getMessageNum"

    val COIN_REGISTER_FREE: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_last_hash/download" else "/coin/register/free"

    val USER_GETUSERLISTONLINESTATUSFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/promotion_top_rank/post" else "/user/getUserListOnlineStatusForH5PostV2"

    val EQUITY_USER_LEVEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/mem_min_result/list" else "/equity/user/level"

    val USER_RONGCLOUD_TOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/link_first_rank/list" else "/user/rongcloud/token"

    val COIN_RECHARGE_CREATECOINLINK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userinfo_min_table/add" else "/coin/recharge/createCoinLink"

    val USER_ACCEPTFRIENDAPPLYINREVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/network_list/delete" else "/user/acceptFriendApplyInReview"

    val SHOWROOM_UPLOADCOVER: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_average_item/upload" else "/showRoom/uploadCover"

    val SHOWROOMUSER_EXCEPTION_FEEDBACK: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/message_first_string/set" else "/showRoomUser/exception/feedback"

    val USER_UPDATEAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_average_key/remove" else "/user/updateAvatar"

    val INFO_FLOW_LIKECOMMENT_UPDATESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/disk_top_array/add" else "/info-flow/likeComment/updateStatus"

    val CONFIG_GETIOSCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/promotion_max_item/search" else "/config/getIOSConfig"

    val GIFT_LIMITVIPGIFT_LOGIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/activity_hash/update" else "/gift/limitVIPGift/login"

    val BROADCASTER_TOPGIFTERS_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/disk_maximum_string/update" else "/broadcaster/topGifters/search"

    val USER_FUNCTIONINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_rank/search" else "/user/functionInfoPostV2"

    val API_CHANNEL_WITHDRAWORDERSTATUSCHANGE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/promotion_minimum_array/add" else "/api/channel/withdrawOrderStatusChange"

    val USER_CLOSECAMERA_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_maximum_item/remove" else "/user/closeCamera/config"

    val GIFT_V2_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/disk_average_rank/list" else "/gift/v2/listPostV2"

    val USER_ROBOTCALL: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/device_first_rank/set" else "/user/robotCall"

    val PIPLINE_VSHOTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/money_minimum_rank/add" else "/pipline/vShotPostV2"

    val INFO_FLOW_USER_RESIDUETIMES: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userorder_average_list/set" else "/info-flow/user/residueTimes"

    val VIDEO_CALL_FLASH_CHAT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/agent_first_hash/set" else "/video-call/flash/chat"

    val COIN_RECHARGE_BROADCASTERINVITATIONPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_map/list" else "/coin/recharge/broadcasterInvitationPostV2"

    val USER_SEARCHGIFTFLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_max_value/list" else "/user/searchGiftFlow"

    val RETRIEVE_CUSTOM_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_minimum_array/del" else "/retrieve/custom/info"
    
    val USER_BACKPACK_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/followlist_maximum_item/add" else "/user/backpack/list"

    val USER_SELECTGUILDWITHDRAWSTATUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/user_last_hash/set" else "/user/selectGuildWithdrawStatusPostV2"

    val SECURITY_OTPLOGIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_maximum_table/list" else "/security/otpLogin"

    val MG_ROOM_MIC_QUEUE_DETAILS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/mem_max_list/update" else "/mg-room/mic/queue/details"

    val MG_ROOM_ADMIN_CLOSE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_array/get" else "/mg-room/admin/close"

    val TEST_MILES_SWIPE_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_maximum_list/search" else "/test/miles/swipe/config"

    val USER_GETUSERMASKLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_min_item/del" else "/user/getUserMaskListPostV2"

    val VIDEO_CALL_BROADCASTER_INCOME: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_second_string/download" else "/video-call/broadcaster/income"

    val SHORTLINK_ASCRIBE_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/doc_last_map/del" else "/shortLink/ascribe/submit"

    val MG_ROOM_MIC_DOWN: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_minimum_map/del" else "/mg-room/mic/down"

    val INFO_FLOW_INFOFLOWALBUMLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/document_second_hash/download" else "/info-flow/infoFlowAlbumList"

    val ACTIVITY_LIVCHATSTAR: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/order_maximum_price/search" else "/activity/livChatStar"

    val BROADCASTER_INSTRUCT_UPDATE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/product_second_list/remove" else "/broadcaster/instruct/update"

    val COIN_GOODS_SEARCHOVERMAXGEAR: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_maximum_json/list" else "/coin/goods/searchOverMaxGear"

    val USER_LEVELPOWER_NOTICECONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/order_maximum_list/del" else "/user/levelPower/noticeConfirm"

    val USER_LANGUAGESELECT: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/game_group/upload" else "/user/languageSelect"

    val PIPLINE_CONTENTLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_maximum_rank/search" else "/pipline/contentList"

    val COMMON_ALL_GETHANDLERMETHOD: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_last_hash/get" else "/common/all/getHandlerMethod"

    val MG_ROOM_MIC_UP_EXPEDITE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_top_rank/delete" else "/mg-room/mic/up/expedite"

    val BROADCASTER_NETWORK_GETQUALITYPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/device_first_group/add" else "/broadcaster/network/getQualityPostV2"

    val VIDEO_CALL_AUTOCALLV2_EVENT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/product_group/list" else "/video-call/autoCallV2/event"

    val USER_GETUSERONLINESTATUSFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/network_min_group/upload" else "/user/getUserOnlineStatusForH5PostV2"

    val TEST_FIELD_MAP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userorder_first_key/delete" else "/test/field/map"

    val SHORTLINK_DEEPLINK_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/activity_last_array/download" else "/shortLink/deeplink/config"

    val BROADCASTER_ONCALLSTRATEGYCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/memory_array/update" else "/broadcaster/onCallStrategyConfigPostV2"

    val USER_ISSENDIMTO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/game_max_map/remove" else "/user/isSendImTo"

    val CONFIG_GETBEAUTYDATAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/message_min_map/add" else "/config/getBeautyDataPostV2"

    val SHOWROOMUSER_AUDIENCES: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/device_top_rank/patch" else "/showRoomUser/audiences"

    val SHOWROOM_ROOM_RANK_DAY_TOP3: String
        get() = if (useObfuscatedUrl) "/api_prod/coin_first_value/download" else "/showRoom/room/rank/day/top3"

    val REVIEW_PURCHASEMEDIA: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/user_second_string/update" else "/review/purchaseMedia"

    val VIDEO_CALL_MATCH_CLEAN_HISTORYPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_max_rank/add" else "/video-call/match/clean/historyPostV2"

    val ACTIVITY_ASSEMBLEAWARDRECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/order_key/delete" else "/activity/assembleAwardRecord"

    val SECURITY_CUSTOMCODE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/activity_top_key/del" else "/security/customCode"

    val BROADCASTER_GETCUSTOMIZEPRICE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_last_string/download" else "/broadcaster/getCustomizePrice"

    val SHOWROOM_STATUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_second_map/set" else "/showRoom/statusPostV2"

    val USER_LISTRANDOMBROADCASTERPOSTV3: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_min_group/delete" else "/user/listRandomBroadcasterPostV3"

    val USER_VISITOR_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_max_item/get" else "/user/visitor/list"

    val BROADCASTER_GROUP_MESSAGE_DEL: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_average_result/search" else "/broadcaster/group/message/del"

    val USER_GETBROADCASTERSTATISTICS: String
        get() = if (useObfuscatedUrl) "/api_prod/harddisk_min_hash/add" else "/user/getBroadcasterStatistics"

    val ACTIVITY_ACTIVE_REPORT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_second_table/add" else "/activity/active/report"

    val INFO_FLOW_USER_DETAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/res/network_table/get" else "/info-flow/user/detail"

    val GIFT_BROADCASTERMASK_FINDLOCKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/doc_minimum_key/remove" else "/gift/broadcasterMask/findLockInfo"

    val BROADCASTER_BANNER_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_second_array/search" else "/broadcaster/banner/list"

    val USER_GETHOBBIES: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_average_price/delete" else "/user/getHobbies"

    val GAME_SLOTMACHINE_RADIOINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/diagram_minimum_price/remove" else "/game/slotMachine/radioInfo"

    val ACTIVITY_GETENTRANCE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_second_value/search" else "/activity/getEntrance"

    val VIDEO_CALL_CHANNEL_LATELYRECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_table/patch" else "/video-call/channel/latelyRecord"

    val SECURITY_CLEANOTPPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_first_result/set" else "/security/cleanOTPPostV2"

    val COIN_RECHARGE_GETCOINLINKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_max_array/set" else "/coin/recharge/getCoinLinkInfo"

    val USER_GETUSERONLINESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_min_string/upload" else "/user/getUserOnlineStatus"

    val VIDEO_CALL_ZEGO_RTCTOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/res/game_max_rank/search" else "/video-call/zego/rtcToken"

    val VIDEO_CALL_MATCHREAL: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/memory_min_list/get" else "/video-call/matchReal"

    val ROBOTSCRIPT_FREESCRIPT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_minimum_json/delete" else "/robotScript/freeScript"

    val SHORTLINK_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/network_first_key/search" else "/shortLink/get"

    val COIN_PAYPAL_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_average_key/upload" else "/coin/paypal/create"

    val SHOWROOM_STATISTICSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/doc_second_map/upload" else "/showRoom/statisticsPostV2"

    val VIDEO_CALL_BROADCASTER_ONLINE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_last_key/del" else "/video-call/broadcaster/online"

    val SHOWROOMUSER_UPLOADINFOFLOWCOVER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_maximum_json/set" else "/showRoomUser/uploadInfoFlowCover"

    val EQUITY_USER: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_maximum_map/search" else "/equity/user"

    val SECURITY_REGISTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_max_result/add" else "/security/register"

    val CONFIG_GETIOSCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_first_hash/get" else "/config/getIOSConfigPostV2"

    val USER_GETCHARGEMEDIA: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_average_result/remove" else "/user/getChargeMedia"

    val GIFT_MASKCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/msg_average_group/post" else "/gift/maskConfig"

    val GIFT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/diagram_minimum_hash/add" else "/gift/list"

    val USER_LISTUSERINFOS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/msg_min_string/delete" else "/user/listUserInfos"

    val TEST_TESTPUSH: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userinfo_first_value/set" else "/test/testPush"

    val SOULVIDEOCALL_SOUL_BROADCASTER_MATCH_EXIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_second_hash/add" else "/soulVideoCall/soul/broadcaster/match/exit"

    val USER_GUIDEIN_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_max_group/delete" else "/user/guideIn/info"

    val BROADCASTER_FREECALLTRIGGER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_maximum_price/delete" else "/broadcaster/freeCallTrigger"

    val USER_GETUSERNAMEANDAVATARPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/order_top_json/delete" else "/user/getUsernameAndAvatarPostV2"

    val COIN_DAILY_LOGIN_TASKLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_minimum_list/post" else "/coin/daily-login/taskListPostV2"

    val SHOWROOMUSER_LIVEGIVEUSERGIFTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_max_key/post" else "/showRoomUser/liveGiveUserGifts"

    val VIDEO_CALL_GORA_RTMTOKENPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/report_minimum_group/update" else "/video-call/gora/rtmTokenPostV2"

    val BROADCASTER_GROUP_SENDMESSAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_minimum_group/set" else "/broadcaster/group/sendMessage"

    val LIVE_QUESTIONNAIRE_BYNO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_last_list/download" else "/live/questionnaire/byNo"

    val SHOWROOM_UPSERTSIGNPKGPRICE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_min_item/get" else "/showRoom/upsertSignPkgPrice"

    val USER_GETUSERINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_first_key/search" else "/user/getUserInfoPostV2"

    val USER_REARCAMERA_OPEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/message_second_value/post" else "/user/rearCamera/open"

    val SHOWROOM_GETUSERMUTESTATUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/activity_minimum_array/download" else "/showRoom/getUserMuteStatusPostV2"

    val SHOWROOM_QUITSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_top_key/upload" else "/showRoom/quitSession"

    val SHOWVIDEO_GETBROADCASTERVIDEO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_minimum_value/del" else "/showVideo/getBroadcasterVideo"

    val SHOWROOM_PREPARE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/memory_top_item/add" else "/showRoom/prepare"

    val USER_CANCELSPECIALFOLLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/mem_first_result/update" else "/user/cancelSpecialFollow"

    val GIFT_LIVE_LIST_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_value/add" else "/gift/live/list/get"

    val TEST_SENTINEL: String
        get() = if (useObfuscatedUrl) "/api_prod/config_price/download" else "/test/sentinel"

    val SHOWROOM_COINTASKINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/harddisk_top_item/update" else "/showRoom/coinTaskInfoPostV2"

    val SHOWROOMGOLDCONTEST_ACTIVITYINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_group/get" else "/showRoomGoldContest/activityInfo"

    val BROADCASTER_CHECKBROADCASTERCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userinfo_result/download" else "/broadcaster/checkBroadcasterConfigPostV2"

    val USER_GETBROADCASTERCOINSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_maximum_string/update" else "/user/getBroadcasterCoinsPostV2"

    val ACTIVITY_GETACTIVITYLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/order_top_group/delete" else "/activity/getActivityList"

    val USER_SWITCHNOTDISTURB: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_top_rank/remove" else "/user/switchNotDisturb"

    val USER_FAVORITE_ADDCANCEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/goods_maximum_key/upload" else "/user/favorite/addCancel"

    val USER_SKIPPAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/config_first_group/update" else "/user/skipPage"

    val USER_DINGTALKROBOT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_average_rank/search" else "/user/dingTalkRobot"
    
    val BROADCASTER_WALL_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/order_top_table/del" else "/broadcaster/wall/search"
    
    val WIGO_INFO_FLOW_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_first_hash/upload" else "/wigo/info-flow/list"

    val TEST_FIELD_DECODE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/diagram_last_json/post" else "/test/field/decode"

    val REVIEW_ICOOL_BROADCASTERWALL: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_last_rank/add" else "/review/icool/broadcasterWall"

    val VIDEO_CALL_POPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_second_hash/add" else "/video-call/popUp"

    val GAME_ROOM_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_last_array/patch" else "/game/room/info"

    val USER_GETFRIENDSLISTPAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_minimum_price/list" else "/user/getFriendsListPage"

    val REVIEW_CHERRY_TOP_PICS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/goods_minimum_value/update" else "/review/cherry/top-pics"

    val ACTIVITY_LIVCHATSTARPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_last_array/update" else "/activity/livChatStarPostV2"

    val CONFIG_GETSTRATEGYPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/product_maximum_hash/patch" else "/config/getStrategyPostV2"

    val BROADCASTER_WALL_SEARCHFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_top_list/download" else "/broadcaster/wall/searchForH5"

    val BROADCASTER_BROADCASTERINFO_UPLOADMEDIUMAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/res/report_group/del" else "/broadcaster/broadcasterInfo/uploadMediumAvatar"

    val SHOWROOMUSER_EDITNOTICE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_min_json/list" else "/showRoomUser/editNotice"

    val BROADCASTER_EXCLUSIVE_WEEKLYTASKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/product_minimum_result/set" else "/broadcaster/exclusive/weeklyTaskList"

    val GIFT_LIVE_SMALL: String
        get() = if (useObfuscatedUrl) "/api_prod/device_min_key/remove" else "/gift/live/small"

    val VIDEO_CALL_ROBOTCALLCLICK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_min_string/del" else "/video-call/robotCallClick"

    val USER_LEVELPOWER_SERVICEIM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_last_string/list" else "/user/levelPower/serviceIM"

    val BROADCASTER_WALL_COMPREHENDLANGUAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_last_key/set" else "/broadcaster/wall/comprehendLanguage"

    val GAME_BROADCASTER_ACTIVITY_SEND: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/config_list/add" else "/game/broadcaster/activity/send"

    val USER_GUARDBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_top_string/set" else "/user/guardBroadcaster"

    val COIN_CALLCARDWITHEX_RECEIVEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/followlist_maximum_table/delete" else "/coin/callCardWithEx/receivePostV2"

    val INFO_FLOW_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/userorder_array/del" else "/info-flow/publish"

    val LIVEINTERACTIVEGIFT_THESWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/money_top_table/get" else "/LiveInteractiveGift/theSwitch"

    val MG_ROOM_MIC_KICK: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/diagram_average_hash/search" else "/mg-room/mic/kick"

    val USER_SETGIFTWALLACTION: String
        get() = if (useObfuscatedUrl) "/api_prod/config_top_map/upload" else "/user/setGiftWallAction"

    val CALLBACK_AGORA_REQUEST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/diagram_max_map/post" else "/callback/agora/request"

    val SOULVIDEOCALL_V2_PICKUP: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_second_value/set" else "/soulVideoCall/v2/pickUp"

    val USER_GETUSERONLINESTATUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_group/get" else "/user/getUserOnlineStatusPostV2"
    
    val BROADCASTER_TASK_SETTLEMENT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/device_average_string/download" else "/broadcaster/task/settlement"

    val SHOWROOM_FRAMEUPLOAD: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_value/delete" else "/showRoom/frameUpload"

    val REPORT_COMPLAIN_INSERTRECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/link_maximum_rank/remove" else "/report/complain/insertRecord"

    val SHOWROOM_ALLLIVETASKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_average_rank/set" else "/showRoom/allLiveTaskInfo"

    val USER_GETALLFRIENDSLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/doc_second_result/get" else "/user/getAllFriendsList"

    val BROADCASTER_DAYSTATISTICPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_maximum_rank/patch" else "/broadcaster/dayStatisticPostV2"

    val SHOWROOM_COINTASKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/msg_first_array/delete" else "/showRoom/coinTaskInfo"

    val USER_UPDATEAGORAUID: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_first_group/search" else "/user/updateAgoraUid"

    val USER_CLUB_SURVEY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/disk_top_list/list" else "/user/club/survey"

    val RANKACTIVITY_RANKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/order_max_value/upload" else "/rankActivity/rankInfo"

    val BROADCASTER_IMINCENTIVE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_first_result/search" else "/broadcaster/IMIncentive/list"

    val CONFIG_GETAPPCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_first_json/patch" else "/config/getAppConfig"

    val TEST_BATCHSETLINE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/gift_min_array/patch" else "/test/batchSetLine"

    val USER_GETRANDOMBROADCASTERPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_rank/get" else "/user/getRandomBroadcasterPostV2"
    
    val BROADCASTER_INVITATION_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_top_result/download" else "/broadcaster/invitation/search"

    val SKIRESORT_GETINFOFLOWANDFRIENDNUMPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_top_list/add" else "/skiResort/getInfoFlowAndFriendNumPostV2"

    val WIGO_INFO_FLOW_COMMENT_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/res/product_maximum_array/remove" else "/wigo/info-flow/comment-publish"

    val SHOWROOMUSER_UPSERTUSERSPECIALDAY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_average_rank/search" else "/showRoomUser/upsertUserSpecialDay"

    val USER_ROBOTIM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_min_hash/set" else "/user/robotIM"

    val GIFT_ACTIVITY_ONLY_SHOWLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_first_array/del" else "/gift/activity-only/showList"

    val USER_RANK_SEARCHV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_maximum_item/search" else "/user/rank/searchV2"

    val USER_RANK_SEARCHFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_top_table/update" else "/user/rank/searchForH5"

    val SHOWROOMTESTENV_OPEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/product_minimum_result/patch" else "/showRoomTestEnv/open"

    val TEST_RECHARGE_SUCCESS_TRIGGER: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_maximum_map/delete" else "/test/recharge-success-trigger"

    val BROADCASTER_NEWUSERTASK_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/game_minimum_group/download" else "/broadcaster/newUserTask/listPostV2"

    val BROADCASTER_BROADCASTERINFO_UPLOADCLUBAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/res/activity_top_item/del" else "/broadcaster/broadcasterInfo/uploadClubAvatar"

    val SHOWROOM_STATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/user_average_rank/remove" else "/showRoom/status"

    val UNITY_USER_GETUNITYTOPIC: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_last_array/search" else "/unity/user/getUnityTopic"

    val VIDEO_CALL_NEWMATCH: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/coin_minimum_string/patch" else "/video-call/newMatch"

    val USER_LANGUAGESELECTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_top_group/set" else "/user/languageSelectPostV2"

    val SHOWROOMUSER_QUOTARANK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_average_table/set" else "/showRoomUser/quotaRank"

    val SOUL_USER_SWITCHMATCHGENDER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_average_group/update" else "/soul/user/switchMatchGender"

    val MG_ROOM_MIC_LAST_ACTIVE_DETAILS: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_max_price/add" else "/mg-room/mic/last-active/details"

    val ACTIVITY_GETACTIVITYROUNDRANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/memory_max_array/download" else "/activity/getActivityRoundRank"

    val BROADCASTER_GROUPMESSAGECONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_minimum_result/download" else "/broadcaster/groupMessageConfig"

    val SOULVIDEOCALL_V2_CHANNEL_JOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_json/remove" else "/soulVideoCall/v2/channel/join"

    val RISK_BROADCASTER_REVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_first_json/download" else "/risk/broadcaster/review"

    val COMMON_USER_PASSWORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/diagram_max_price/get" else "/common/user/password"

    val USER_UPDATEANSWER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_min_json/list" else "/user/updateAnswer"

    val ACTIVITY_GIFTUPGRADERANKDATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_max_result/post" else "/activity/giftUpgradeRankData"

    val CONFIG_SYS_NOTICEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_min_list/del" else "/config/sys/noticePostV2"

    val CONFIG_GETDEFAULTAVATARINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_top_group/delete" else "/config/getDefaultAvatarInfoPostV2"

    val USER_AVATAR_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userinfo_first_hash/delete" else "/user/avatar/search"

    val WEEK_CARD_PURCHASE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_min_json/del" else "/week-card/purchase"

    val SHOWROOM_OPEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/coin_average_table/patch" else "/showRoom/open"

    val USER_BACKPACK_GIFTSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_min_list/list" else "/user/backpack/giftsPostV2"

    val USER_BROADCASTERVISITORSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/report_maximum_map/update" else "/user/broadcasterVisitorsPostV2"

    val REVIEW_IOS_VIDEOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/link_min_price/patch" else "/review/ios/videoPostV2"

    val USER_WALL_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/mem_top_result/delete" else "/user/wall/search"

    val USER_OSS_POLICY: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/agent_second_string/add" else "/user/oss/policy"

    val BROADCASTER_AUTOCALL_AUTOCALLSWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/harddisk_maximum_table/update" else "/broadcaster/autoCall/autoCallSwitch"

    val MSG_MATCH_QUESTION_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_max_array/add" else "/msg/match/question/list"

    val BROADCASTER_SWIPE_LIKE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/message_maximum_table/set" else "/broadcaster/swipe/like"

    val ACTIVITY_GETROULETTEWINNER: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_maximum_price/patch" else "/activity/getRouletteWinner"

    val COIN_VSHOT_RECHARGE_RECHARGE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_array/delete" else "/coin/vshot-recharge/recharge"

    val LIVEROOM_BULLETSCREEN_SEND: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/report_minimum_table/search" else "/liveRoom/bulletScreen/send"

    val CONFIG_SH: String
        get() = if (useObfuscatedUrl) "/api_prod/meminfo_maximum_map/add" else "/config/sh"

    val USER_RONGCLOUD_TOKENPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_average_key/search" else "/user/rongcloud/tokenPostV2"

    val USER_ISSENDGIFT2POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_max_list/delete" else "/user/isSendGift2PostV2"

    val SHOWROOMUSER_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/agent_max_group/update" else "/showRoomUser/data"

    val USER_GETUSERNAMEANDAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/promotion_top_price/get" else "/user/getUsernameAndAvatar"

    val USER_NEW_SUBSCRIPTION_POPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_second_price/post" else "/user/new-subscription/popup"
    
    val VIDEO_CALL_CHANNEL_LOG: String
        get() = if (useObfuscatedUrl) "/api_prod/res/game_second_result/add" else "/video-call/channel/log"
    
    val COIN_CALLCARDWITHEX_RECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_table/set" else "/coin/callCardWithEx/receive"
    
    val VIDEO_CALL_CHANNEL_LOGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/order_average_json/add" else "/video-call/channel/logPostV2"
    
    val COIN_REVIEWMODECONSUME: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/user_array/del" else "/coin/reviewModeConsume"
    
    val COIN_RECHARGE_SUBSCRIBEVIPSIGN: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_min_map/list" else "/coin/recharge/subscribeVipSign"
    
    val BROADCASTERVIDEO_TEACH_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_price/patch" else "/broadcasterVideo/teach/list"
    
    val USER_ALIAS_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/activity_min_hash/del" else "/user/alias/create"
    
    val USER_GETMEDIAURL: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_top_map/post" else "/user/getMediaUrl"
    
    val BROADCASTER_EXPOSURE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_min_item/post" else "/broadcaster/exposure"
    
    val VIDEO_CALL_MATCHPOPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/res/coin_last_string/get" else "/video-call/matchPopup"
    
    val USER_RANK_SEARCHCOUPLE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/harddisk_average_list/del" else "/user/rank/searchCouple"
    
    val INFO_FLOW_GIFTS_GIVEUSERALBUMGIFTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_minimum_item/search" else "/info-flow/Gifts/giveUserAlbumGifts"
    
    val SECURITY_EXCEPTIONCODE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/coin_average_value/search" else "/security/exceptionCode"
    
    val COIN_PAYCHANNEL_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/activity_last_hash/del" else "/coin/payChannel/get"
    
    val USER_SUGGESTION_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/promotion_first_item/list" else "/user/suggestion/submit"
    
    val SECURITY_OAUTH: String
        get() = if (useObfuscatedUrl) "/api_prod/res/msg_average_key/list" else "/security/oauth"
    
    val REPORT_COMPLAIN_APPLY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/agent_max_value/add" else "/report/complain/apply"
    
    val BROADCASTER_RANK_SEARCHV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/gift_top_array/add" else "/broadcaster/rank/searchV2"
    
    val TEST_TESTZSET: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/mem_max_json/remove" else "/test/testZset"
    
    val LIVEKIT_USER_BIND: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_max_list/download" else "/livekit/user/bind"

    val TEST_STATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/config_minimum_list/update" else "/test/status"

    val REVIEW_MULTISINGSONGROOM: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/report_last_item/update" else "/review/multiSingSongRoom"

    val VIDEO_CALL_HANGUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_top_table/update" else "/video-call/hangUp"

    val GIFT_GETGIFTCOUNT: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/game_average_string/del" else "/gift/getGiftCount"

    val USER_FEEDBACK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_min_json/get" else "/user/feedback"

    val RANKACTIVITY_SEATRANKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_top_string/remove" else "/rankActivity/seatRankInfo"

    val USER_GETAVATARLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/diagram_price/set" else "/user/getAvatarListPostV2"

    val USER_FEEDBACKTYPE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/product_second_string/download" else "/user/feedbackType"

    val PIPLINE_SWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/document_first_value/add" else "/pipline/switch"

    val VIDEO_CALL_CHANNEL_JOINCONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/document_second_item/update" else "/video-call/channel/joinConfirm"

    val REVIEW_CHERRY_INFO_FLOWSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_first_list/del" else "/review/cherry/info-flowsPostV2"

    val CONFIG_GETSTRATEGY: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_maximum_hash/list" else "/config/getStrategy"

    val PIPLINE_LIVSTARPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/config_max_result/upload" else "/pipline/livStarPostV2"

    val TEST_ENCRYPT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/agent_min_rank/get" else "/test/encrypt"

    val BROADCASTER_MEDIASCORE_DESC: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/mem_hash/set" else "/broadcaster/mediaScore/desc"

    val MG_ROOM_MIC_UP_QUEUE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_minimum_json/patch" else "/mg-room/mic/up/queue"

    val COIN_TPPRECHARGE_INFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_last_result/delete" else "/coin/tppRecharge/infoPostV2"

    val USER_GETBROADCASTEREXTRAINFOFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/user_last_array/upload" else "/user/getBroadcasterExtraInfoForH5"

    val BROADCASTER_INVITE_LINK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_first_table/search" else "/broadcaster/invite/link"

    val VIDEO_CALL_MATCH_EXIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/diagram_second_hash/search" else "/video-call/match/exit"

    val GAME_GAMEENTRY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_minimum_hash/get" else "/game/gameEntry"

    val SKIRESORT_COMMENT_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_average_list/add" else "/skiResort/comment/publish"

    val SCRIPTKILL_JOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_second_group/list" else "/scriptKill/join"

    val USER_SHOWROOMGIFTFLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/meminfo_first_map/list" else "/user/showRoomGiftFlow"

    val SECURITY_TIME_PROOF: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/diagram_max_json/post" else "/security/time/proof"

    val SOUL_USER_GETCOMMONCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_average_table/del" else "/soul/user/getCommonConfig"

    val CALLBACK_PAYPAL_NOTIFY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_minimum_list/list" else "/callback/paypal/notify"

    val MG_ROOM_ADMIN_ROOMTAG: String
        get() = if (useObfuscatedUrl) "/api_prod/res/network_maximum_array/set" else "/mg-room/admin/roomTag"

    val SHOWROOM_MUTEUSER: String
        get() = if (useObfuscatedUrl) "/api_prod/res/goods_min_key/add" else "/showRoom/muteUser"

    val GIFT_V2_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/report_average_hash/download" else "/gift/v2/list"

    val BROADCASTER_USEREVALUATE_SUBMITPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/gift_first_result/update" else "/broadcaster/userEvaluate/submitPostV2"

    val VIDEOCALLV2_HANGUP: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_min_list/add" else "/videoCallV2/hangUp"

    val REVIEW_IOS_BERRYLIVE_VIDEO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/msg_top_json/search" else "/review/ios/berrylive/video"

    val REVIEW_CHERRY_INFO_FLOWS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/harddisk_minimum_group/upload" else "/review/cherry/info-flows"

    val SECURITY_EXCEPTIONCODEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_last_hash/list" else "/security/exceptionCodePostV2"

    val USER_FUNCTIONINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_json/del" else "/user/functionInfo"

    val USER_GETUSERINFOBYUSERNOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/goods_max_rank/upload" else "/user/getUserInfoByUserNoPostV2"

    val USER_INSTRUCT_EVALUATION: String
        get() = if (useObfuscatedUrl) "/api_prod/order_minimum_table/update" else "/user/instruct/evaluation"

    val USER_SETSPECIALFOLLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_second_string/get" else "/user/setSpecialFollow"

    val USER_DELFRIENDINREVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/disk_last_list/set" else "/user/delFriendInReview"

    val SHOWROOM_LIVETASKINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_max_array/remove" else "/showRoom/liveTaskInfo"

    val GAME_BROADCASTER_ACTIVITY_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_top_group/set" else "/game/broadcaster/activity/list"

    val API_CHANNEL_RECHARGEORDERSTATUSCHANGE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_top_rank/list" else "/api/channel/rechargeOrderStatusChange"

    val LIVE_GIFT_FAVORITE_SWITCHTOP: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/document_average_array/del" else "/live/gift/favorite/switchTop"

    val BROADCASTER_DASHBOARDPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/followlist_max_table/del" else "/broadcaster/dashboardPostV2"

    val RANKACTIVITY_PUBLICITY: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_second_result/list" else "/rankActivity/publicity"

    val BROADCASTER_BROADCASTERGRADE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/gift_min_key/add" else "/broadcaster/broadcasterGrade"

    val BROADCASTER_GETBROADCASTERMEDIAS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/diagram_second_json/set" else "/broadcaster/getBroadcasterMedias"

    val USER_BACKPACK_GIFTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/game_key/set" else "/user/backpack/gifts"

    val BROADCASTER_GROUP_GETMESSAGENUMPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_top_value/get" else "/broadcaster/group/getMessageNumPostV2"

    val USER_GETBROADCASTEREXTRAINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_average_rank/delete" else "/user/getBroadcasterExtraInfo"

    val INFO_FLOW_TOPICLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/doc_last_map/upload" else "/info-flow/topicList"

    val COIN_CALLCARD_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_second_list/remove" else "/coin/callCard/info"

    val VIDEO_CALL_MATCH_V2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_max_hash/get" else "/video-call/match/v2"

    val GAME_PAYPAL_ORDER_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_top_result/del" else "/game/paypal-order/create"

    val BROADCASTER_INVITATION_INFO_V2POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/doc_first_value/search" else "/broadcaster/invitation/info/v2PostV2"

    val USER_NEW_SUBSCRIPTION_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_last_result/patch" else "/user/new-subscription/info"

    val REVIEW_CHERRY_VIDEO_FLOWS: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_maximum_rank/update" else "/review/cherry/video-flows"

    val BROADCASTER_SYNCALLINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/mem_last_price/list" else "/broadcaster/synCallInfo"

    val MG_ROOM_EXIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_minimum_list/post" else "/mg-room/exit"

    val BROADCASTER_TOPGIFTERS_SEARCHPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/device_first_value/upload" else "/broadcaster/topGifters/searchPostV2"

    val BROADCASTER_MEDIASCORE_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/activity_second_map/list" else "/broadcaster/mediaScore/get"

    val SHOWROOMUSER_OPEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/device_average_string/remove" else "/showRoomUser/open"

    val CONFIG_SENDDELACCOUNT: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_average_hash/update" else "/config/sendDelAccount"

    val USER_SAVEMONEY_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/goods_top_rank/set" else "/user/saveMoney/list"

    val ACTIVITY_VALENTINE_CUPIDWHEELINVITATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_minimum_rank/upload" else "/activity/valentine/cupidWheelInvitation"

    val GAME_SLOTMACHINE_LOTTERY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_top_array/del" else "/game/slotMachine/Lottery"

    val USER_LOCATIONPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_maximum_json/set" else "/user/locationPostV2"

    val USER_ISSENDGIFT2: String
        get() = if (useObfuscatedUrl) "/api_prod/harddisk_second_value/download" else "/user/isSendGift2"

    val USER_REJECTFRIENDAPPLYINREVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_max_rank/get" else "/user/rejectFriendApplyInReview"

    val BROADCASTER_MEDIASCORE_DESCPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_average_key/list" else "/broadcaster/mediaScore/descPostV2"

    val USER_GETBROADCASTEREXTRAINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/goods_last_value/list" else "/user/getBroadcasterExtraInfoPostV2"

    val COIN_PAYPAL_CANCEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/meminfo_min_price/del" else "/coin/paypal/cancel"

    val GIFT_V2_USER_CODE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/goods_min_price/update" else "/gift/v2/user-code"

    val GAME_LINGXIAN_GETUSERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/coin_first_json/add" else "/game/lingxian/getUserInfo"

    val VIDEO_CALL_MEETCALLCLICK: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_first_value/search" else "/video-call/meetCallClick"

    val USER_ISRECHARGEUSERS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_last_json/upload" else "/user/isRechargeUsers"

    val USER_GETUSERINFOBYUSERNO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/msg_minimum_list/remove" else "/user/getUserInfoByUserNo"

    val GAME_GETGAMETOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/money_min_key/get" else "/game/getGameToken"

    val USER_UPDATEMEDIA: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_min_price/add" else "/user/updateMedia"

    val LIVEINTERACTIVEGIFT_DEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_second_rank/post" else "/LiveInteractiveGift/del"

    val USER_RELATIONSCOUNTER: String
        get() = if (useObfuscatedUrl) "/api_prod/money_top_table/add" else "/user/relationsCounter"

    val INFO_FLOW_LIKE_UPDATESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/msg_first_array/update" else "/info-flow/like/updateStatus"

    val USER_ADDFRIENDINREVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_top_array/remove" else "/user/addFriendInReview"

    val BROADCASTER_NETWORK_GETQUALITY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/mem_min_rank/delete" else "/broadcaster/network/getQuality"

    val VIDEO_CALL_MATCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_second_list/post" else "/video-call/match"

    val USER_FREEADDFRIENDCOUNTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_max_hash/search" else "/user/freeAddFriendCountPostV2"

    val REVIEW_CHERRY_TOP_PICSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_second_json/list" else "/review/cherry/top-picsPostV2"

    val ACTIVITY_LASTACTIVITYANDIDS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_top_list/list" else "/activity/lastActivityAndIds"

    val BROADCASTER_MEDIASCORE_RECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_max_group/set" else "/broadcaster/mediaScore/receive"

    val USER_BACKPACK_LATESTEXPIRERECHARGECARDPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/harddisk_max_value/delete" else "/user/backpack/latestExpireRechargeCardPostV2"

    val MG_ROOM_UPDATECOINTASKANNOUNCE: String
        get() = if (useObfuscatedUrl) "/api_prod/doc_second_value/post" else "/mg-room/updateCoinTaskAnnounce"

    val INFO_FLOW_COMMENT_REPORT: String
        get() = if (useObfuscatedUrl) "/api_prod/msg_last_array/post" else "/info-flow/comment/report"

    val USER_BROADCASTERMASKFINDANDMARK: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_min_group/download" else "/user/broadcasterMaskFindAndMark"

    val LIVEROOM_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_first_key/list" else "/liveRoom/data"

    val HIT_RECHARGEH5CLIENTHIT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_first_item/upload" else "/hit/rechargeH5ClientHit"

    val ACTIVITY_GETROULETTEREWARDRECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/product_first_string/update" else "/activity/getRouletteRewardRecord"

    val BROADCASTER_WALL_BROADCASTERPROVINCE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_max_string/del" else "/broadcaster/wall/broadcasterProvince"

    val POPUP_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/doc_last_item/patch" else "/popUp/list"

    val BROADCASTER_WALL_HIGHQUALITY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/order_group/list" else "/broadcaster/wall/highQuality"

    val USER_ADDFRIENDSOUL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_first_rank/get" else "/user/addFriendSoul"

    val COIN_H5_RECHARGE_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/user_average_group/delete" else "/coin/h5-recharge/search"

    val MG_ROOM_USERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/link_average_price/update" else "/mg-room/userInfo"

    val USER_CREATEIMSESSIONRELATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_second_json/set" else "/user/createImSessionRelate"

    val USER_SVIP_EQUITY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/order_result/get" else "/user/svip/equity"

    val SHOWROOM_GETPRIVILEGEBYUSERIDPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_max_table/list" else "/showRoom/getPrivilegeByUserIdPostV2"

    val LIVEINTERACTIVEGIFT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/harddisk_list/update" else "/LiveInteractiveGift/list"

    val ACTIVITY_CANRECHAGETAGSHOWPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_map/upload" else "/activity/canRechageTagShowPostV2"

    val CONFIG_GETAPPCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/network_minimum_list/set" else "/config/getAppConfigPostV2"

    val COIN_GOODS_GETLASTSPECIALOFFERV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/gift_first_group/update" else "/coin/goods/getLastSpecialOfferV2"

    val CONFIG_GETGUIDEINCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_max_array/remove" else "/config/getGuideInConfig"

    val USER_RELATIONSCOUNTERTOUSER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_average_key/search" else "/user/relationsCounterToUser"

    val BROADCASTER_NEWUSERTASK_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_top_array/get" else "/broadcaster/newUserTask/list"

    val GIFT_SPECIALLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_min_value/add" else "/gift/specialList"

    val USER_GETISFOLLOWEDPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/network_min_price/update" else "/user/getIsFollowedPostV2"

    val VIDEO_CALL_MATCH_UNITY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_second_list/remove" else "/video-call/match/unity"

    val REVIEW_MEDIAWALL: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/game_last_json/del" else "/review/mediaWall"

    val UNITY_USER_GETUNITYMATCHCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/promotion_minimum_item/download" else "/unity/user/getUnityMatchConfig"

    val SHOWROOM_UPDATECOINTASKANNOUNCE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userorder_second_json/upload" else "/showRoom/updateCoinTaskAnnounce"

    val USER_CLUB_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/msg_last_map/patch" else "/user/club/config"

    val COIN_UPDATEINSIGHTPAYORDERSTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/promotion_maximum_map/download" else "/coin/updateInsightPayOrderStatus"

    val USER_UNFRIEND: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/stock_item/delete" else "/user/unfriend"

    val GIFT_BROADCASTERMASK_UNLOCK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_minimum_key/update" else "/gift/broadcasterMask/unlock"

    val VIDEO_CALL_CHANNEL_UPLOADABNORMALDATA: String
        get() = if (useObfuscatedUrl) "/api_prod/res/coin_second_group/upload" else "/video-call/channel/uploadAbnormalData"

    val SHORTLINK_MEDIA_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_max_json/post" else "/shortLink/media/search"

    val BROADCASTER_TASK_RECEIVEBONUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/doc_average_array/post" else "/broadcaster/task/receiveBonus"

    val USERLIVELEVEL_SHINING: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_top_array/download" else "/userLiveLevel/shining"

    val SECURITY_GETOTP: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_list/delete" else "/security/getOTP"

    val HIT_ASKFORGIFTPIC_HIT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/game_first_item/set" else "/hit/askForGiftPic/hit"

    val INFO_FLOW_COMMENT_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userorder_min_string/search" else "/info-flow/comment/publish"

    val SHOWROOMUSER_SMALLWINDOW: String
        get() = if (useObfuscatedUrl) "/api_prod/report_top_string/update" else "/showRoomUser/smallWindow"

    val MYLEVEL_CURRENT: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_minimum_value/remove" else "/myLevel/current"

    val BROADCASTER_CLUB_PRICELIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/network_average_price/add" else "/broadcaster/club/priceList"

    val USER_GETCHARGEMEDIAFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_json/set" else "/user/getChargeMediaForH5PostV2"

    val VIDEO_CALL_MATCH_SOUL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_minimum_hash/post" else "/video-call/match/soul"

    val REVIEW_MULTICHATROOM: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/activity_array/download" else "/review/multiChatRoom"

    val BROADCASTER_AUTOCALL_AUTOCALLSTRATEGYCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_minimum_item/patch" else "/broadcaster/autoCall/autoCallStrategyConfig"

    val USER_INSTRUCT_EVALUATIONPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/product_min_group/search" else "/user/instruct/evaluationPostV2"

    val USER_RANK_SEARCHCOUPLEV2: String
        get() = if (useObfuscatedUrl) "/api_prod/meminfo_second_key/get" else "/user/rank/searchCoupleV2"

    val USER_GETLEVELINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/product_max_item/upload" else "/user/getLevelInfo"

    val COIN_RECHARGE_CHECKBROADCASTERINVITATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/device_second_map/download" else "/coin/recharge/checkBroadcasterInvitation"

    val COIN_PRESENTED_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/money_min_array/upload" else "/coin/presented/get"

    val TEST_REDIS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/doc_value/upload" else "/test/redis"

    val MG_ROOM_BOSS_STATUS_SWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/promotion_minimum_key/update" else "/mg-room/boss/status-switch"

    val BROADCASTER_BROADCASTERINFO_UPLOADHORIZONTALAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_minimum_map/patch" else "/broadcaster/broadcasterInfo/uploadHorizontalAvatar"

    val BROADCASTER_SETTLEMENTCYCLEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/report_maximum_json/list" else "/broadcaster/settlementCyclePostV2"

    val COIN_RECHARGE_PAYMENT_IPA: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/gift_last_item/add" else "/coin/recharge/payment/ipa"

    val COIN_GOODS_GETPROMOTION: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userinfo_min_map/delete" else "/coin/goods/getPromotion"

    val LIVE_QUESTIONNAIRE_FILL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_first_rank/get" else "/live/questionnaire/fill"

    val HIT_STARTAPPHIT: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_max_result/add" else "/hit/startAppHit"

    val MG_ROOM_BOSS_ENTER_EXIT_CONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_result/update" else "/mg-room/boss/enter-exit/confirm"

    val CALLBACK_AGORA_CLOUDPLAYER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_last_table/delete" else "/callback/agora/cloudPlayer"

    val COMMON_APPLOG_TRIGGER: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_top_string/del" else "/common/appLog/trigger"

    val USER_SEARCHCOINFLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/config_top_item/del" else "/user/searchCoinFlow"

    val UNITY_USER_GETUNITYUSEREVALUATION: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_last_table/get" else "/unity/user/getUnityUserEvaluation"

    val SHOWROOM_AUDIENCESPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_list/update" else "/showRoom/audiencesPostV2"

    val USER_SAVEMONEY: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/link_top_item/download" else "/user/saveMoney"

    val BROADCASTER_INVITATION_INFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_top_group/delete" else "/broadcaster/invitation/infoPostV2"
    
    val CONFIG_ACTIVITY_SLIDESHOW: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/product_first_json/post" else "/config/activity/slideshow"
    
    val USER_GETBROADCASTEREXTRAINFOFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userorder_list/post" else "/user/getBroadcasterExtraInfoForH5PostV2"
    
    val ROBOTSCRIPT_ME_PAIDSCRIPT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_maximum_hash/download" else "/robotScript/me/paidScript"
    
    val USER_FAQ_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_min_group/remove" else "/user/FAQ/get"
    
    val CONFIG_GETBEAUTYDATA: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userinfo_second_array/patch" else "/config/getBeautyData"
    
    val USER_SAVEUSERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/diagram_min_result/list" else "/user/saveUserInfo"
    
    val USER_SENDMISSCALLMESSAGEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_key/get" else "/user/sendMissCallMessagePostV2"
    
    val BROADCASTER_INSTRUCT_CONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_top_rank/del" else "/broadcaster/instruct/confirm"
    
    val COIN_DAILY_LOGIN_RECEIVETASKBONUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_max_map/del" else "/coin/daily-login/receiveTaskBonus"
    
    val ACTIVITY_USER_ACTIVE_SCORERECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/config_average_json/update" else "/activity/user-active/scoreReceive"
    
    val BROADCASTER_SWIPE_MILES_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_maximum_result/patch" else "/broadcaster/swipe/miles/config"
    
    val ROBOTSCRIPT_PAIDSCRIPT: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_price/del" else "/robotScript/paidScript"
    
    val LIVEINTERACTIVEGIFT_ADDITEM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/goods_maximum_table/del" else "/LiveInteractiveGift/addItem"

    val COIN_CALLCARD_INFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/link_key/patch" else "/coin/callCard/infoPostV2"

    val USER_STATUS_SETORREMOVEONCALLSTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_top_array/remove" else "/user/status/setOrRemoveOnCallStatus"

    val MG_ROOM_AUDIENCESPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/diagram_last_hash/update" else "/mg-room/audiencesPostV2"

    val ACTIVITY_CANRECHAGETAGSHOW: String
        get() = if (useObfuscatedUrl) "/api_prod/userorder_last_price/post" else "/activity/canRechageTagShow"

    val SECURITY_EMAILREGISTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/config_second_item/remove" else "/security/emailRegister"

    val CONFIG_GETSTRATEGYFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/money_minimum_array/delete" else "/config/getStrategyForH5"

    val BROADCASTER_SWIPE_DISLIKE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/money_max_price/delete" else "/broadcaster/swipe/dislike"

    val BROADCASTER_RANK_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/product_max_value/delete" else "/broadcaster/rank/search"

    val MG_ROOM_MGROOMVIDEOCALL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_top_map/del" else "/mg-room/mgRoomVideoCall"

    val USER_SEARCHIMSESSIONRELATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_price/update" else "/user/searchImSessionRelate"

    val USER_SAVEMONEY_STATISTICPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/message_last_json/del" else "/user/saveMoney/statisticPostV2"

    val SECURITY_ISVALIDTOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_table/del" else "/security/isValidToken"
    
    val SHOWROOM_BROADCASTERS: String
        get() = if (useObfuscatedUrl) "/api_prod/msg_last_key/patch" else "/showRoom/broadcasters"
    
    val CONFIG_SAVEUSERSCREENSHOTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/agent_minimum_value/download" else "/config/saveUserScreenshots"
    
    val INFO_FLOW_DETAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/stock_max_table/upload" else "/info-flow/detail"

    val TEST_REJECT_CALL_FREEZE: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_array/download" else "/test/reject-call/freeze"

    val CONFIG_GETUSERAUTH: String
        get() = if (useObfuscatedUrl) "/api_prod/report_top_value/upload" else "/config/getUserAuth"

    val BROADCASTER_INVITATION_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_array/download" else "/broadcaster/invitation/info"

    val SHORTLINK_GETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/config_first_key/upload" else "/shortLink/getPostV2"

    val USER_GETUSERLISTONLINESTATUSJOINEXTRA: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_last_array/patch" else "/user/getUserListOnlineStatusJoinExtra"

    val USER_GETUSERINFOFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_min_result/del" else "/user/getUserInfoForH5PostV2"

    val BROADCASTER_VISITUSERDETAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_last_rank/upload" else "/broadcaster/visitUserDetail"

    val SHOWROOM_ROOMVIEWERSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/mem_average_list/update" else "/showRoom/roomViewersPostV2"

    val BROADCASTER_USEREVALUATE_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/agent_average_table/post" else "/broadcaster/userEvaluate/submit"

    val MG_ROOM_MIC_SPEAK_APPLY: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/order_first_hash/del" else "/mg-room/mic/speak/apply"

    val BROADCASTERVIDEO_TEACH_LANGUAGELIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/money_top_price/post" else "/broadcasterVideo/teach/languageList"

    val SHOWROOM_ROOM_RANK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_table/set" else "/showRoom/room/rank"

    val USER_BACKPACK_LATESTEXPIRERECHARGECARD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/disk_first_table/post" else "/user/backpack/latestExpireRechargeCard"

    val PIPLINE_CONTENTLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/order_last_item/update" else "/pipline/contentListPostV2"

    val HIT_ICONENTRY_HIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_top_map/del" else "/hit/iconEntry/hit"

    val INFO_FLOW_GETOTHERUSERTOPIC: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/agent_first_group/post" else "/info-flow/getOtherUserTopic"

    val SECURITY_SELECTCALLACTIONLOG: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_min_string/update" else "/security/selectCallActionLog"

    val CONFIG_GETLAUNCHSCREEN: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/product_max_list/remove" else "/config/getLaunchScreen"

    val TEST_T_CHECKREVIEWSTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_json/search" else "/test/t/checkReviewStatus"

    val CONFIG_SUBMITINITDATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/money_average_hash/update" else "/config/submitInitData"

    val LIVEKIT_USER_RECHARGE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userorder_minimum_list/post" else "/livekit/user/recharge"

    val USER_ALIAS_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/order_top_key/add" else "/user/alias/list"

    val USER_CREATEIMSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/res/diagram_last_string/upload" else "/user/createImSession"

    val VIDEOCALLV2_CHANNEL_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_max_group/search" else "/videoCallV2/channel/create"

    val GIFT_LIVE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/config_average_list/get" else "/gift/live/list"

    val HIT_CLIENTSTARTLOG: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_minimum_group/add" else "/hit/clientStartLog"

    val GIFT_GIFTMAP_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/device_minimum_list/patch" else "/gift/giftMap/list"

    val COIN_SUBSCRIPTION_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_minimum_string/download" else "/coin/subscription/search"

    val RETRIEVE_CUSTOM_EMAILSUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_max_price/download" else "/retrieve/custom/emailSubmit"

    val ROBOTSCRIPT_AUTOSAYHI: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_max_value/upload" else "/robotScript/autoSayHi"

    val BROADCASTER_WALL_LOWPRICE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_second_item/update" else "/broadcaster/wall/lowPrice"

    val USER_GETHOBBIESPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_maximum_price/del" else "/user/getHobbiesPostV2"

    val BROADCASTER_MULTIPLE_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_maximum_key/download" else "/broadcaster/multiple/search"

    val USER_FAVORITE_PAGEFAVORITES: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_maximum_string/del" else "/user/favorite/pageFavorites"

    val MSG_NOTIFICATIONLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_minimum_price/add" else "/msg/notificationList"

    val BROADCASTER_BROADCASTERINFO_UPLOADBATCHAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_average_rank/update" else "/broadcaster/broadcasterInfo/uploadBatchAvatar"

    val USER_ISRECHARGEUSERSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_average_map/update" else "/user/isRechargeUsersPostV2"

    val USER_LOCATION: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_first_rank/patch" else "/user/location"

    val SHOWVIDEO_GETINDIACONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_last_list/add" else "/showVideo/getIndiaConfig"

    val BROADCASTER_REGISTER_RECOMMEND: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_minimum_item/download" else "/broadcaster/register/recommend"

    val TEST_AUTOCALLADDPOOL: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_max_table/get" else "/test/autoCallAddPool"

    val USER_SAVEBASICINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_second_list/update" else "/user/saveBasicInfo"

    val GIFT_ASKGIFTPIC_GIFTLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_max_price/update" else "/gift/askGiftPic/giftListPostV2"

    val ACTIVITY_ASSEMBLEAWARD: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_second_list/update" else "/activity/assembleAward"

    val API_CHANNEL_WITHDRAWORDERCREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/report_first_hash/search" else "/api/channel/withdrawOrderCreate"

    val LIVE_PRELOAD_RESOURCES_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_array/list" else "/live/preLoad/resources/list"

    val COIN_RECHARGE_UPGRADEGOODS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/coin_last_table/upload" else "/coin/recharge/upgradeGoods"

    val BROADCASTER_DASHBOARD: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/config_first_value/upload" else "/broadcaster/dashboard"

    val USER_GETUSERLISTONLINESTATUSPOSTV3: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/activity_first_result/set" else "/user/getUserListOnlineStatusPostV3"

    val REVIEW_ZODIAC_BROADCASTERLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/user_min_table/post" else "/review/zodiac/broadcasterList"

    val USER_GETUSERLISTONLINESTATUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/agent_minimum_key/search" else "/user/getUserListOnlineStatusPostV2"

    val SHOWROOM_USERLIVELEVEL_SHINING: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/diagram_average_rank/download" else "/showRoom/userLiveLevel/shining"

    val ACTIVITY_CHECKIN_ACTIVE_CHECK: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_last_table/remove" else "/activity/checkin-active/check"

    val GAME_PAYPAL_ORDER_CAPTURE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_minimum_json/list" else "/game/paypal-order/capture"

    val BROADCASTER_WALL_CLASSIFIEDSEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/res/coin_minimum_rank/search" else "/broadcaster/wall/classifiedSearch"

    val BROADCASTERVIDEO_TEACH_COMMENTANDLIKE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/product_maximum_map/remove" else "/broadcasterVideo/teach/commentAndLike"

    val TEST_TEST_BLOCK_ACCOUNTTEST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/user_max_json/upload" else "/test/test/block_AccountTest"

    val BROADCASTER_EXCHANGE_RATEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_second_result/del" else "/broadcaster/exchange/ratePostV2"

    val USER_CAMERA_CLOSE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/message_max_result/remove" else "/user/camera/close"

    val BROADCASTER_TASK_SETTLEMENTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/promotion_average_hash/delete" else "/broadcaster/task/settlementPostV2"

    val PIPLINE_VSHOT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/doc_min_key/delete" else "/pipline/vShot"

    val VIDEO_CALL_CLEARRULEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_minimum_rank/delete" else "/video-call/clearRulePostV2"

    val ACTIVITY_GIFTUPGRADERANKDATAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_first_list/download" else "/activity/giftUpgradeRankDataPostV2"

    val ACTIVITY_GETROULETTEOVERVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_last_table/list" else "/activity/getRouletteOverview"

    val LIVEROOM_ENDPAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_top_map/del" else "/liveRoom/endPage"

    val USER_GETUSERMASKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_min_string/del" else "/user/getUserMaskList"

    val USER_CLUB_NOTICE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/msg_first_result/upload" else "/user/club/notice"

    val USER_AVATAR_SEARCHPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_last_item/search" else "/user/avatar/searchPostV2"

    val USER_BACKPACK_USEAVATARFRAME: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_second_result/remove" else "/user/backpack/useAvatarFrame"

    val BROADCASTER_INVITATION_SEARCH_V2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_first_string/patch" else "/broadcaster/invitation/search/v2"

    val BROADCASTER_ONECLICKSAYHI: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/link_second_item/upload" else "/broadcaster/oneClickSayHi"

    val SECURITY_EXISTSBYEMAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/device_max_result/upload" else "/security/existsByEmail"

    val BROADCASTER_EXCLUSIVE_RECEIVETASKBONUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/game_average_array/post" else "/broadcaster/exclusive/receiveTaskBonus"

    val CONFIG_SWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_last_value/list" else "/config/switch"

    val INFO_FLOW_GETRANDOMUSERLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_first_price/patch" else "/info-flow/getRandomUserList"

    val GIFT_LIMITVIPGIFT_NOTPURCHASED: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/report_minimum_hash/post" else "/gift/limitVIPGift/notPurchased"

    val VIDEO_CALL_USER_CALLRESULT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/link_minimum_key/remove" else "/video-call/user/callResult"

    val MG_ROOM_MIC_UP_DOWN_CONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/memory_last_group/list" else "/mg-room/mic/up-down/confirm"

    val TEST_TESTAUTOCALL_APP: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_last_item/upload" else "/test/testAutoCall/app"

    val TEST_INCOMECOEFFICIENT: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_minimum_item/add" else "/test/incomeCoefficient"

    val SCRIPTKILL_JOINGROUP: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_maximum_array/del" else "/scriptKill/joinGroup"

    val MG_ROOM_ADMIN_OPEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_minimum_key/delete" else "/mg-room/admin/open"

    val ACTIVITY_USER_ACTIVE_BONUSRECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_first_item/add" else "/activity/user-active/bonusReceive"

    val USER_BINDINGEMAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/link_second_array/delete" else "/user/bindingEmail"
    
    val SECURITY_LOGOUT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/coin_last_result/patch" else "/security/logout"

    val SHOWROOMUSER_USEGIFTWALL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/memory_array/update" else "/showRoomUser/useGiftWall"

    val SHOWROOMUSER_UPLOADCOVER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/link_first_hash/set" else "/showRoomUser/uploadCover"

    val RETRIEVE_CUSTOM_TASK_COMPLETE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/msg_minimum_value/list" else "/retrieve/custom/task/complete"

    val USER_RELATIONSCOUNTERPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_second_rank/post" else "/user/relationsCounterPostV2"

    val INFO_FLOW_INFORM_FINDNUMBER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_maximum_list/get" else "/info-flow/inform/findNumber"

    val USER_BROADCASTERRELATIONS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/device_top_group/download" else "/user/broadcasterRelations"

    val COIN_GIVEINDIACALLCARDNUM: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/message_average_key/remove" else "/coin/giveIndiaCallCardNum"

    val REVIEW_IOS_STREAM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_min_table/download" else "/review/ios/stream"

    val POPUP_BYRECHARGE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/document_max_value/add" else "/popUp/byRecharge"

    val ACTIVITY_CHARGE_ACTIVE_RECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/message_min_group/patch" else "/activity/charge-active/receive"

    val USER_SENDMISSCALLMESSAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/userinfo_average_hash/update" else "/user/sendMissCallMessage"

    val COIN_PAYCHANNEL_GETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_first_key/del" else "/coin/payChannel/getPostV2"

    val SHOWROOM_DATAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/meminfo_maximum_json/get" else "/showRoom/dataPostV2"

    val LIVE_ACTIVITY_CONFESSIONWALL_INVITATIONS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/report_min_json/list" else "/live/activity/confessionWall/invitations"

    val ACTIVITY_USER_ACTIVE_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/msg_string/add" else "/activity/user-active/data"

    val SOULVIDEOCALL_V2_POPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/network_first_item/patch" else "/soulVideoCall/v2/popUp"

    val SCRIPTKILL_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_last_table/search" else "/scriptKill/listPostV2"

    val BROADCASTER_GROUPMESSAGECONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/coin_max_array/delete" else "/broadcaster/groupMessageConfigPostV2"

    val ROBOTSCRIPT_ME_ROBOTSCRIPT_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/device_min_result/patch" else "/robotScript/me/robotScript/config"

    val BROADCASTER_RANK_SEARCHRANKFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_last_rank/remove" else "/broadcaster/rank/searchRankForH5"

    val SHOWROOM_CONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/message_first_list/patch" else "/showRoom/configPostV2"

    val USER_GETISFOLLOWEDINREVIEW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_last_table/download" else "/user/getIsFollowedInReview"

    val GIFT_MASKCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_average_array/remove" else "/gift/maskConfigPostV2"

    val COIN_DAILY_LOGIN_TASKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/device_maximum_list/add" else "/coin/daily-login/taskList"

    val BROADCASTER_USEREVALUATE_EVALUATELIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_maximum_hash/patch" else "/broadcaster/userEvaluate/evaluateList"

    val SOUL_BROADCASTER_STRATEGY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_first_map/update" else "/soul/broadcaster/strategy"

    val USER_GETAVATARLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_max_result/set" else "/user/getAvatarList"

    val PIPLINE_SWITCHPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_minimum_map/delete" else "/pipline/switchPostV2"

    val SHOWROOMUSER_USERLIVELEVESWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_top_string/list" else "/showRoomUser/userLiveLeveSwitch"

    val WEEK_CARD_DETAILFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/meminfo_minimum_group/add" else "/week-card/detailForH5"

    val REVIEW_ALBUMCOVERLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/stock_second_value/delete" else "/review/albumCoverList"

    val COIN_VOICETOTEXTCONSUME: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/network_top_string/post" else "/coin/voiceToTextConsume"

    val VIDEO_CALL_MATCH_SOUL_GUIDE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_average_json/patch" else "/video-call/match/soul/guide"

    val USER_SAVEMONEY_STATISTIC: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userinfo_second_table/set" else "/user/saveMoney/statistic"

    val BROADCASTER_GETBROADCASTERFIRSTMEDIA: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/followlist_top_table/del" else "/broadcaster/getBroadcasterFirstMedia"

    val COIN_PROMOTIONPAYCHANNEL_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/gift_maximum_group/download" else "/coin/promotionPayChannel/get"

    val BROADCASTERVIDEO_TAKE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_average_key/list" else "/broadcasterVideo/take"

    val BROADCASTER_COMMONCALLPRICE: String
        get() = if (useObfuscatedUrl) "/api_prod/message_top_price/get" else "/broadcaster/commonCallPrice"

    val BROADCASTER_SWIPE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_second_table/del" else "/broadcaster/swipe/list"

    val INFO_FLOW_DELINFOFLOWALBUM: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/activity_maximum_list/del" else "/info-flow/delInfoFlowAlbum"

    val UNITY_USER_SWITCHMATCHGENDER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_maximum_key/search" else "/unity/user/switchMatchGender"

    val BROADCASTER_RECOMMEND_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_result/set" else "/broadcaster/recommend/search"

    val CALLBACK_ZEGO_RTC: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_maximum_rank/download" else "/callback/zego/rtc"

    val REPORT_COMPLAIN_BLOCKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/disk_min_hash/remove" else "/report/complain/blockList"

    val INFO_FLOW_CREATEINFOFLOWALBUM: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/diagram_average_json/search" else "/info-flow/createInfoFlowAlbum"

    val USER_STRONGGUIDE_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/gift_second_rank/upload" else "/user/strongGuide/config"

    val COIN_ORDER_WRITEBACKPAYNO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/game_minimum_key/set" else "/coin/order/writeBackPayNo"

    val VIDEO_CALL_MEETDISLIKE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/memory_top_price/delete" else "/video-call/meetDislike"

    val WEEK_CARD_BANNERDETAIL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/harddisk_min_json/search" else "/week-card/bannerDetail"

    val GIFT_GIFTMAP_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_first_key/get" else "/gift/giftMap/listPostV2"

    val INFO_FLOW_USER_INFOFLOWCOUNT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/product_average_hash/del" else "/info-flow/user/infoFlowCount"

    val USER_GETUSERCOINS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_first_hash/del" else "/user/getUserCoins"

    val GAME_SLOTMACHINE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/product_minimum_array/list" else "/game/slotMachine/list"

    val CALLBACK_PAYPAL_WEBHOOK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_maximum_list/update" else "/callback/paypal/webhook"

    val USER_GETUSERCARDINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/activity_maximum_list/post" else "/user/getUserCardInfo"

    val CALLBACK_AGORA_MESSAGE_INFORM: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/network_top_price/download" else "/callback/agora/message/inform"

    val BROADCASTER_CLUB_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/agent_max_price/upload" else "/broadcaster/club/config"

    val COIN_USDTPAYSCREENSHOTUPLOAD: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/msg_hash/upload" else "/coin/usdtPayScreenshotUpload"

    val SHOWROOM_GETUSERMUTESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_minimum_group/add" else "/showRoom/getUserMuteStatus"

    val COMMON_USER_IDLEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userinfo_maximum_array/remove" else "/common/user/idlePostV2"

    val COMMON_METHODMAPPING: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/doc_min_item/remove" else "/common/methodMapping"

    val HIT_ASCRIBERECORDREQS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_first_key/delete" else "/hit/ascribeRecordReqs"

    val MG_ROOM_COINTASKINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_first_value/patch" else "/mg-room/coinTaskInfoPostV2"

    val WIGO_INFO_FLOW_REPORT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/coin_min_rank/upload" else "/wigo/info-flow/report"

    val USER_GETFRIENDSLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_second_string/download" else "/user/getFriendsList"

    val USER_ISSENDIMTOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_max_array/patch" else "/user/isSendImToPostV2"

    val USER_GUIDEINVITECODE_BIND: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/document_min_string/remove" else "/user/guideInviteCode/bind"

    val USER_DELETEACCOUNT: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_top_item/delete" else "/user/deleteAccount"

    val BROADCASTER_SETTLEMENTCYCLE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/config_last_rank/set" else "/broadcaster/settlementCycle"

    val BROADCASTER_UPDATECUSTOMIZEPRICE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/user_minimum_group/patch" else "/broadcaster/updateCustomizePrice"

    val SHOWROOM_LIVETASKINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userorder_max_map/delete" else "/showRoom/liveTaskInfoPostV2"

    val REGION_GETLOGINPICBYIP: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_item/search" else "/region/getLoginPicByIp"

    val MG_ROOM_SEAT_DETAILS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_second_result/remove" else "/mg-room/seat/details"

    val ACTIVITY_GETTOPUSERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/promotion_first_price/post" else "/activity/getTopUserInfo"

    val VIDEO_CALL_CHANNEL_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/agent_maximum_group/update" else "/video-call/channel/create"

    val RETRIEVE_CUSTOM_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userinfo_average_map/search" else "/retrieve/custom/config"

    val PUSH_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/device_maximum_string/get" else "/push/config"

    val USER_BROADCASTERVISITORS: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_first_price/upload" else "/user/broadcasterVisitors"

    val VIDEOCALLV2_CHANNEL_JOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/promotion_last_price/del" else "/videoCallV2/channel/join"

    val SECURITY_CLEANOTP: String
        get() = if (useObfuscatedUrl) "/api_prod/res/activity_min_price/search" else "/security/cleanOTP"

    val GIFT_V2_USER_CODE_BATCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_top_json/download" else "/gift/v2/user-code/batch"

    val USER_GIVEUSERGIFTS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/network_group/remove" else "/user/giveUserGifts"

    val ACTIVITY_VALENTINE_CUPIDWHEELINVITATIONLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userinfo_second_array/add" else "/activity/valentine/cupidWheelInvitationList"

    val SECURITY_GETOTPPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/gift_second_key/upload" else "/security/getOTPPostV2"

    val MG_ROOM_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/device_last_item/get" else "/mg-room/data"

    val COIN_H5_RECHARGE_GOODS: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/gift_maximum_result/get" else "/coin/h5-recharge/goods"

    val PIPLINE_PAYUNLOCK: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userinfo_average_item/add" else "/pipline/payUnlock"

    val PERMISSION_SETTING_REPORT: String
        get() = if (useObfuscatedUrl) "/api_prod/harddisk_last_result/add" else "/permission/setting/report"

    val WIGO_INFO_FLOW_COMMENT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/msg_maximum_group/delete" else "/wigo/info-flow/comment-list"

    val BROADCASTER_INSTRUCT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/harddisk_average_group/patch" else "/broadcaster/instruct/list"

    val VIDEO_CALL_CHANNEL_JOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_maximum_price/patch" else "/video-call/channel/join"

    val USER_GETBROADCASTERCOINS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_second_group/add" else "/user/getBroadcasterCoins"

    val GIFT_BLINDBOX_AWARD_RECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_average_hash/post" else "/gift/blindbox/award/record"

    val GIFT_V2_USER_CODEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_key/post" else "/gift/v2/user-codePostV2"

    val ACTIVITY_ACTIVITYHONORWALL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_minimum_group/remove" else "/activity/activityHonorWall"

    val INFO_FLOW_INFOFLOWALBUMPAGE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_min_array/get" else "/info-flow/infoFlowAlbumPage"

    val USER_GUIDE_SEARCHOLDTOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/product_minimum_key/upload" else "/user/guide/searchOldToken"

    val USER_SAVEUSERINFOINCLUDEHOBBY: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_minimum_key/patch" else "/user/saveUserInfoIncludeHobby"

    val GIFT_ACTIVITY_ONLY_ASKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/activity_average_json/delete" else "/gift/activity-only/askList"

    val HIT_WEBUSERAGENTHITFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/memory_group/post" else "/hit/webUserAgentHitForH5"

    val TEST_VISITBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/report_hash/search" else "/test/visitBroadcaster"

    val TEST_IMAGES: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_maximum_array/patch" else "/test/images"

    val CALLBACK_ADJUST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/report_max_hash/list" else "/callback/adjust"

    val SKIRESORT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/agent_max_table/get" else "/skiResort/list"

    val TEST_TESTSETACCESS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/mem_map/search" else "/test/testSetAccess"

    val BROADCASTER_FREECALLPOPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/res/message_second_json/download" else "/broadcaster/freeCallPopup"

    val USERLIVELEVEL_EQUITY: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_maximum_item/search" else "/userLiveLevel/equity"

    val USER_INVITECODE_BINDV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/network_minimum_array/download" else "/user/inviteCode/bindV2"

    val USER_S3_UPLOADURLPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/goods_last_json/post" else "/user/s3/uploadUrlPostV2"

    val ACTIVITY_CANROULETTESHOWPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_second_item/get" else "/activity/canRouletteShowPostV2"

    val SHOWROOM_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/agent_minimum_price/update" else "/showRoom/config"

    val LIVE_ACTIVITY_CONFESSIONWALL_DELUSERIDS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/userorder_last_result/list" else "/live/activity/confessionWall/delUserIds"

    val WIGO_INFO_FLOW_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/agent_second_json/download" else "/wigo/info-flow/publish"

    val GIFT_ACTIVITY_ONLY_SHOWLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/config_max_array/del" else "/gift/activity-only/showListPostV2"

    val LIVE_ACTIVITY_CONFESSIONWALL_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/goods_minimum_string/add" else "/live/activity/confessionWall/delete"

    val MG_ROOM_MIC_UP: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/msg_max_result/upload" else "/mg-room/mic/up"

    val TEST_FIELD_ENCRYPT_LOCALCACHE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_maximum_json/del" else "/test/field/encrypt/localCache"

    val USER_RECOMMENDBROADCASTER_SEND: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_last_string/list" else "/user/recommendBroadcaster/send"

    val SHOWROOMGOLDCONTEST_REFRESHRANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/gift_maximum_result/remove" else "/showRoomGoldContest/refreshRank"

    val USER_GETFRIENDSLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/money_json/list" else "/user/getFriendsListPostV2"

    val TEST_TESTLISTACCESS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/meminfo_list/upload" else "/test/testListAccess"

    val TEST_MATCH_WEIGHT_SCORE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_max_string/patch" else "/test/match-weight-score"

    val USER_GETGUARDIANCOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_minimum_map/add" else "/user/getGuardianCoin"

    val EVENT_ACTIVE_JOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_maximum_list/patch" else "/event-active/join"

    val COIN_REGISTER_FREEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_max_item/update" else "/coin/register/freePostV2"

    val DRESSUP_CENTER_BUY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/game_last_json/remove" else "/dressup/center/buy"

    val BROADCASTER_IMINCENTIVE_RECEIVECOINSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_maximum_map/patch" else "/broadcaster/IMIncentive/receiveCoinsPostV2"

    val USER_USER_UPDATECREATETIME: String
        get() = if (useObfuscatedUrl) "/api_prod/userinfo_min_string/post" else "/user/user/updateCreateTime"

    val MG_ROOM_DATAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_maximum_price/download" else "/mg-room/dataPostV2"

    val USER_USER_RECHARGE_INFO_POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_min_item/delete" else "/user/user-recharge-info/postV2"

    val USER_INSTRUCT_PAY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/promotion_top_table/upload" else "/user/instruct/pay"

    val WIGO_INFO_FLOW_NEWEST_PUBLISH: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_minimum_result/del" else "/wigo/info-flow/newest-publish"

    val USER_STRONGGUIDE_CONFIGV2: String
        get() = if (useObfuscatedUrl) "/api_prod/link_min_group/list" else "/user/strongGuide/configV2"

    val SHOWROOM_CLOSE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/disk_first_group/patch" else "/showRoom/close"

    val ACTIVITY_GETTOPN: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_maximum_hash/add" else "/activity/getTopN"

    val ACTIVITY_GETROULETTEENTRANCE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/memory_top_string/add" else "/activity/getRouletteEntrance"

    val BROADCASTER_WALL_SEARCHFORNEARBY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/activity_maximum_table/remove" else "/broadcaster/wall/searchForNearby"

    val USER_NEW_SUBSCRIPTION_INFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/doc_second_key/remove" else "/user/new-subscription/infoPostV2"

    val SOULVIDEOCALL_V2_HANGUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userinfo_last_table/download" else "/soulVideoCall/v2/hangUp"

    val USER_FAVORITE_ISFAVORITE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/msg_last_hash/remove" else "/user/favorite/isFavorite"

    val INFO_FLOW_INFORM_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_group/remove" else "/info-flow/inform/list"

    val ADS_RECORDVIP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_maximum_value/list" else "/ads/recordVIP"

    val MG_ROOM_ENTER: String
        get() = if (useObfuscatedUrl) "/api_prod/res/product_second_value/search" else "/mg-room/enter"

    val CONFIG_SYS_NOTICE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_maximum_price/download" else "/config/sys/notice"

    val VIDEO_CALL_CLEARRULE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_top_item/patch" else "/video-call/clearRule"

    val ACTIVITY_CHARGE_ACTIVE_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/followlist_minimum_group/upload" else "/activity/charge-active/data"

    val USER_ALIAS_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_second_table/delete" else "/user/alias/listPostV2"

    val GIFT_BLINDBOX_RANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/agent_top_rank/del" else "/gift/blindbox/rank"

    val ACTIVITY_CANROULETTESHOW: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_second_group/upload" else "/activity/canRouletteShow"

    val BROADCASTER_RECOMMEND_SAYHI: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_last_price/add" else "/broadcaster/recommend/sayHi"

    val REVIEW_ZODIAC_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_average_item/get" else "/review/zodiac/list"

    val ACTIVITY_BANNER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_string/post" else "/activity/banner"

    val WIGO_INFO_FLOW_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/goods_maximum_array/upload" else "/wigo/info-flow/delete"

    val USER_INSTRUCT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_max_item/set" else "/user/instruct/list"

    val BROADCASTER_EXCLUSIVE_WEEKLYTASKLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_max_array/remove" else "/broadcaster/exclusive/weeklyTaskListPostV2"

    val USER_OSS_POLICYPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/promotion_min_list/update" else "/user/oss/policyPostV2"

    val SHOWROOMUSER_ROOM_RANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/message_top_value/update" else "/showRoomUser/room/rank"

    val SCRIPTKILL_LISTMEMBERS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/activity_minimum_price/list" else "/scriptKill/listMembers"

    val USER_GETINFOBYUID: String
        get() = if (useObfuscatedUrl) "/api_prod/link_last_key/set" else "/user/getInfoByUid"

    val USER_GETUSERINFOFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/followlist_first_rank/get" else "/user/getUserInfoForH5"

    val MG_ROOM_CALLPKGINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/followlist_minimum_string/patch" else "/mg-room/callPkgInfoPostV2"

    val BROADCASTER_EXCLUSIVE_TASKLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_second_string/set" else "/broadcaster/exclusive/taskListPostV2"

    val USER_ADDFRIEND: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_second_result/patch" else "/user/addFriend"

    val USER_BASEINFO_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_minimum_table/upload" else "/user/baseinfo/search"

    val TEST_PUSH_WIGO_ROBOT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userorder_array/remove" else "/test/push/wigo/robot"

    val COIN_GOODS_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_price/update" else "/coin/goods/listPostV2"

    val USER_LEVELPOWER_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/mem_last_list/search" else "/user/levelPower/list"

    val TEST_REVENUE_JOB_API: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/activity_minimum_key/get" else "/test/revenue/job/api"

    val COIN_H5_RECHARGE_PAYMENTTYPE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/coin_array/list" else "/coin/h5-recharge/paymentType"

    val USER_V2_USERRELATIONS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_second_array/update" else "/user/v2/userRelations"

    val VIDEO_CALL_BROADCASTER_ONLINEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/game_top_key/set" else "/video-call/broadcaster/onlinePostV2"

    val VIDEO_CALL_COMMAND_CONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_top_price/update" else "/video-call/command/confirm"

    val LIVEROOM_DATAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userinfo_max_key/get" else "/liveRoom/dataPostV2"

    val SHORTLINK_FINALTARGET: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_average_rank/download" else "/shortLink/finalTarget"

    val COIN_RECHARGE_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/coin_top_array/add" else "/coin/recharge/search"

    val EVENT_ACTIVE_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/diagram_first_array/get" else "/event-active/list"

    val VIDEO_CALL_RECORD_WARN: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/document_list/get" else "/video-call/record/warn"

    val USER_GETUSERINFOFORWIGO: String
        get() = if (useObfuscatedUrl) "/api_prod/agent_last_result/search" else "/user/getUserInfoForWigo"

    val TEST_ROBOT_CALL_USER: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_maximum_value/del" else "/test/robot/call/user"

    val SKIRESORT_COMMENT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_last_array/delete" else "/skiResort/comment/list"

    val VIDEO_CALL_MATCH_CLEAN_HISTORY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_minimum_item/add" else "/video-call/match/clean/history"

    val PUSH_CONFIG_UPDATE: String
        get() = if (useObfuscatedUrl) "/api_prod/mem_average_rank/download" else "/push/config/update"

    val SECURITY_EXISTSBYEMAILPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/doc_price/delete" else "/security/existsByEmailPostV2"

    val USER_UPDATEBIGOUID: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_minimum_table/get" else "/user/updateBigoUid"

    val USER_SAVEMONEY_DEL: String
        get() = if (useObfuscatedUrl) "/api_prod/disk_first_result/post" else "/user/saveMoney/del"

    val GAME_BROADCASTER_ACTIVITY_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/activity_list/post" else "/game/broadcaster/activity/listPostV2"

    val INFO_FLOW_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/promotion_second_hash/update" else "/info-flow/delete"

    val SHORTLINK_MEDIA_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_last_json/get" else "/shortLink/media/get"

    val INFO_FLOW_USER_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_average_table/remove" else "/info-flow/user/list"

    val MG_ROOM_BOSS_EXIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/product_minimum_price/remove" else "/mg-room/boss/exit"

    val COIN_GOODS_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/activity_max_value/patch" else "/coin/goods/search"

    val USER_USER_RECHARGE_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/memory_minimum_key/patch" else "/user/user-recharge-info"

    val USER_COINS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_second_value/post" else "/user/coins"

    val SHOWROOMUSER_UPSCALERANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/config_max_list/set" else "/showRoomUser/upscaleRank"

    val USER_CONSUMED_INCREASE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_last_item/set" else "/user/consumed/increase"

    val SHOWROOMGOLDCONTEST_ACTIVITYINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/harddisk_first_map/upload" else "/showRoomGoldContest/activityInfoPostV2"

    val USER_SVIP_GRADEPOINT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_average_key/add" else "/user/svip/gradePoint"

    val TEST_FIELD_DECODE_LOCALCACHE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/promotion_max_table/update" else "/test/field/decode/localCache"

    val BROADCASTER_FAQ_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/goods_maximum_list/delete" else "/broadcaster/FAQ/get"

    val BROADCASTER_AUTOCALL_AUTOCALLSTRATEGYCONFIGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_max_string/upload" else "/broadcaster/autoCall/autoCallStrategyConfigPostV2"

    val SECURITY_EXISTSBYMOBILEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_price/download" else "/security/existsByMobilePostV2"

    val COIN_GOODS_GETLASTSPECIALOFFER: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/doc_minimum_array/add" else "/coin/goods/getLastSpecialOffer"

    val BROADCASTER_TASK_RECEIVEBONUSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_minimum_array/search" else "/broadcaster/task/receiveBonusPostV2"

    val COIN_GOODS_BROADCASTERINVITATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/link_maximum_rank/upload" else "/coin/goods/broadcasterInvitation"

    val GIFT_JACKPOT_AWARD_RECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/promotion_last_json/upload" else "/gift/jackpot/award/record"

    val BROADCASTER_ACITVITYRANK_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/goods_max_rank/set" else "/broadcaster/acitvityRank/search"

    val ACTIVITY_GETACTIVITYRANKV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_json/download" else "/activity/getActivityRankV2"

    val INFO_FLOW_UPDATEINFOFLOWALBUM: String
        get() = if (useObfuscatedUrl) "/api_prod/config_table/post" else "/info-flow/updateInfoFlowAlbum"

    val SKIRESORT_GETINFOFLOWANDFRIENDNUM: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_average_item/update" else "/skiResort/getInfoFlowAndFriendNum"

    val ACTIVITY_CHECKIN_ACTIVE_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/message_result/list" else "/activity/checkin-active/data"

    val GIFT_JACKPOT_AWARD_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/doc_minimum_group/download" else "/gift/jackpot/award/info"

    val CONFIG_SENDDELACCOUNTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_minimum_result/post" else "/config/sendDelAccountPostV2"

    val INFO_FLOW_GIFTS_GIVEUSER: String
        get() = if (useObfuscatedUrl) "/api_prod/userorder_value/patch" else "/info-flow/Gifts/giveUser"

    val VIDEO_CALL_SOUL_CHANNEL_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_average_map/del" else "/video-call/soul/channel/create"

    val SCRIPTKILL_LISTMEMBERSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/config_average_price/add" else "/scriptKill/listMembersPostV2"

    val COIN_PAYPAL_CAPTURE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/link_first_table/patch" else "/coin/paypal/capture"

    val SHOWROOMUSER_GETUSERSENSITIVEWORDS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/order_average_string/set" else "/showRoomUser/getUserSensitiveWords"

    val USER_INVITATION_GETCODEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/product_average_table/del" else "/user/invitation/getCodePostV2"

    val BROADCASTER_WALL_SEARCH_V2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/money_first_key/remove" else "/broadcaster/wall/search/v2"

    val USER_INVITATION_GETINVITER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/order_item/del" else "/user/invitation/getInviter"

    val SYSTEM_TIMESTAMP: String
        get() = if (useObfuscatedUrl) "/api_prod/game_max_result/download" else "/system/timestamp"

    val USER_NEW_SUBSCRIPTION_POPUPPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/doc_average_map/get" else "/user/new-subscription/popupPostV2"

    val RANKACTIVITY_RANKTOPONE: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_last_rank/upload" else "/rankActivity/rankTopOne"

    val GIFT_ASKGIFTPIC_GIFTLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_array/add" else "/gift/askGiftPic/giftList"

    val SHORTLINK_FINALTARGETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/followlist_map/patch" else "/shortLink/finalTargetPostV2"

    val SHORTLINK_MEDIA_SEARCHPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/promotion_top_value/set" else "/shortLink/media/searchPostV2"

    val TEST_NEW_USER_TASK_TEST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_minimum_table/add" else "/test/new-user-task-test"

    val BROADCASTER_EXCLUSIVE_TASKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/doc_top_group/del" else "/broadcaster/exclusive/taskList"

    val WIGO_INFO_FLOW_LIKE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_first_rank/remove" else "/wigo/info-flow/like"

    val USER_ACTIVEING: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/disk_key/patch" else "/user/activeing"

    val USER_CONSUMED_GET: String
        get() = if (useObfuscatedUrl) "/api_prod/gift_first_map/download" else "/user/consumed/get"

    val LIVECHAT_CONTENT_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_second_list/delete" else "/liveChat/content/submit"

    val USER_LEVELPOWER_NOTICEUNLOCKLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/stock_min_price/remove" else "/user/levelPower/noticeUnlockList"

    val USER_GETUSERCOINSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_average_map/upload" else "/user/getUserCoinsPostV2"

    val REPORT_APPLY: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/link_min_string/patch" else "/report/apply"

    val USER_BACKPACK_LISTAVAILABLEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/user_second_rank/get" else "/user/backpack/listAvailablePostV2"

    val PIPLINE_LIVSTAR: String
        get() = if (useObfuscatedUrl) "/api_prod/res/stock_min_string/update" else "/pipline/livStar"

    val SHOWROOMUSER_ROOM_VIEWERS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/doc_map/patch" else "/showRoomUser/room/viewers"

    val SHOWROOMUSER_JOINSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/doc_first_item/upload" else "/showRoomUser/joinSession"

    val REGION_GETREGIONCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/message_maximum_key/patch" else "/region/getRegionConfig"

    val USER_GETCHARGEMEDIAFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/coin_max_json/list" else "/user/getChargeMediaForH5"

    val CONFIG_SWITCHPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_string/upload" else "/config/switchPostV2"

    val BROADCASTER_FAKEBROADCASTERPOPUP: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_minimum_map/list" else "/broadcaster/fakeBroadcasterPopup"

    val IMBACK_ROBOTSCRIPTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/promotion_top_group/update" else "/IMBack/robotScriptV2"

    val VIDEO_CALL_AUTO_CALL_RECORD_UPDATESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_top_result/remove" else "/video-call/auto-call-record/updateStatus"

    val SHOWROOMUSER_CLOSE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/promotion_list/set" else "/showRoomUser/close"

    val MSG_GETACTIVITYMSGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/goods_group/patch" else "/msg/getActivityMsgPostV2"

    val GAME_SLOTMACHINE_CLEANFLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/res/msg_minimum_map/get" else "/game/slotMachine/cleanFlow"

    val LIVE_GIFT_FAVORITE_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/product_last_list/del" else "/live/gift/favorite/delete"

    val COIN_RECHARGE_PAYMENT_GP: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/harddisk_average_hash/list" else "/coin/recharge/payment/gp"

    val BROADCASTER_GROUP_MESSAGE_UPLOAD: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/stock_min_result/update" else "/broadcaster/group/message/upload"

    val USER_FOLLOWEACHOTHER: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/promotion_top_rank/del" else "/user/followEachOther"

    val TEST_DECRYPT: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/user_minimum_result/list" else "/test/decrypt"

    val INFO_FLOW_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/activity_second_list/search" else "/info-flow/list"

    val BROADCASTER_USEREVALUATE_CLUBUSER: String
        get() = if (useObfuscatedUrl) "/api_prod/stock_first_item/upload" else "/broadcaster/userEvaluate/ClubUser"

    val COIN_GOODS_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/userorder_maximum_string/add" else "/coin/goods/list"

    val USER_S3_UPLOADURL: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userorder_minimum_rank/get" else "/user/s3/uploadUrl"

    val SHOWROOM_ROOM_VIEWERS: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userinfo_first_json/add" else "/showRoom/room/viewers"

    val USER_REARCAMERA_CONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/stock_top_map/upload" else "/user/rearCamera/config"

    val SHOWROOM_AUDIENCES: String
        get() = if (useObfuscatedUrl) "/api_prod/config_max_result/list" else "/showRoom/audiences"

    val MG_ROOM_BOSS_KICK: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_last_hash/add" else "/mg-room/boss/kick"

    val RISK_SHUMEI_CALLBACK: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/config_first_string/patch" else "/risk/shuMei/callback"

    val SHOWROOM_ROOMRANKPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/coin_minimum_rank/set" else "/showRoom/roomRankPostV2"

    val CONFIG_CONTENT_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_top_map/update" else "/config/content/search"

    val BROADCASTER_INVITE_LINKPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_result/download" else "/broadcaster/invite/linkPostV2"

    val BROADCASTER_ONCALLSTRATEGYCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_maximum_result/delete" else "/broadcaster/onCallStrategyConfig"

    val COMMON_TRANSLATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/stock_first_item/update" else "/common/translate"

    val GAME_LINGXIAN_UPDATECOIN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/product_last_list/download" else "/game/lingxian/updateCoin"

    val REPORT_COMPLAIN_REMOVEBLOCK: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_second_rank/post" else "/report/complain/removeBlock"

    val USER_RANK_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/diagram_first_item/search" else "/user/rank/search"

    val USER_GETFEEDBACKCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/product_top_hash/upload" else "/user/getFeedbackConfig"

    val CALLBACK_ZEGO_CLOUDRECORD: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/agent_maximum_key/remove" else "/callback/zego/cloudRecord"

    val USER_GUARDIANBANNERPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/diagram_min_rank/update" else "/user/guardianBannerPostV2"

    val SOULVIDEOCALL_V2_CHANNEL_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/network_average_price/upload" else "/soulVideoCall/v2/channel/create"

    val BROADCASTER_GUARDIANRANK: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/network_max_result/del" else "/broadcaster/guardianRank"

    val GAME_GAMEENTRYPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_last_string/remove" else "/game/gameEntryPostV2"

    val BROADCASTER_GETBROADCASTERPHOTOSANDVIDEOS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_second_table/get" else "/broadcaster/getBroadcasterPhotosAndVideos"

    val VIDEO_CALL_AGORA_RTMTOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/harddisk_maximum_table/update" else "/video-call/agora/rtmToken"

    val ACTIVITY_GETACTIVITYRANK: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_last_key/search" else "/activity/getActivityRank"

    val COIN_RECHARGE_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_first_table/get" else "/coin/recharge/create"

    val RETRIEVE_CUSTOM_GOODSGET: String
        get() = if (useObfuscatedUrl) "/api_prod/res/config_first_json/add" else "/retrieve/custom/goodsGet"

    val TEST_GET_TOKEN: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/mem_last_json/post" else "/test/get-token"

    val COIN_CALLCARD_RECEIVEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_first_result/patch" else "/coin/callCard/receivePostV2"

    val MYLEVEL_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/document_map/delete" else "/myLevel/list"

    val BROADCASTER_INSTRUCT_UPDATEPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/config_min_value/search" else "/broadcaster/instruct/updatePostV2"

    val USER_GETUSERINFOFORINDIAPKG: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/device_top_map/patch" else "/user/getUserInfoForIndiaPkg"

    val WELL_KNOWN_ACME_CHALLENGE: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_maximum_table/set" else "/.well-known/acme-challenge"

    val LIVEKIT_USER_GETUSERINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_first_value/download" else "/livekit/user/getUserInfo"

    val BROADCASTER_WARNCONTENTS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/promotion_first_map/search" else "/broadcaster/warnContents"

    val USER_UPDATEBATCHAVATAR: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/followlist_max_array/set" else "/user/updateBatchAvatar"

    val SCRIPTKILL_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/res/document_average_table/update" else "/scriptKill/list"

    val SHOWROOM_DATA: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/config_last_map/search" else "/showRoom/data"

    val CONFIG_SUBMITINSTALLREFERER: String
        get() = if (useObfuscatedUrl) "/api_prod/network_value/update" else "/config/submitInstallReferer"

    val SHOWROOMUSER_WIGODIAMOND: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/game_minimum_table/upload" else "/showRoomUser/wigoDiamond"

    val TEST_SENDCONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/res/network_first_map/add" else "/test/sendConfirm"

    val USER_INCENTIVE_COLLECT: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/activity_hash/download" else "/user/incentive/collect"

    val MG_ROOM_MIC_INVITATION: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/device_top_result/download" else "/mg-room/mic/invitation"

    val ACTIVITY_GETROULETTEINFO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/harddisk_minimum_hash/del" else "/activity/getRouletteInfo"

    val ACTIVITY_VALENTINE_CUPIDWHEEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_first_list/patch" else "/activity/valentine/cupidWheel"

    val BROADCASTER_GUARDIAN_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/product_item/patch" else "/broadcaster/guardian/search"

    val RISK_SHUMEI_VIDEO_CALLBACK: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/doc_maximum_array/set" else "/risk/shuMei/video/callback"

    val USER_GETUSERLISTONLINESTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_max_price/remove" else "/user/getUserListOnlineStatus"

    val COIN_H5_RECHARGE_CREATE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/agent_minimum_result/set" else "/coin/h5-recharge/create"

    val ACTIVITY_ICONENTRY_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_maximum_value/set" else "/activity/iconEntry/list"

    val BROADCASTER_WARNCONTENTSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_first_array/update" else "/broadcaster/warnContentsPostV2"

    val USER_FREEADDFRIENDCOUNT: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_second_key/remove" else "/user/freeAddFriendCount"

    val TEST_GOOGLE_TRANSLATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/memory_min_value/add" else "/test/google/translate"

    val REVIEW_IOS_VIDEO: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_array/post" else "/review/ios/video"

    val SHOWROOMTESTENV_UPLOADVIDEO: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_min_item/list" else "/showRoomTestEnv/uploadVideo"

    val REVIEW_IOS_STREAMPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/res/userorder_average_item/search" else "/review/ios/streamPostV2"

    val ACTIVITY_GETACTIVITYIDLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/game_hash/download" else "/activity/getActivityIdList"

    val CALLBACK_RONGCLOUD_CHATROOMSTATUS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/followlist_maximum_key/patch" else "/callback/rongcloud/chatRoomStatus"

    val GIFT_ACTIVITY_ONLY_ASKLISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/link_last_map/get" else "/gift/activity-only/askListPostV2"

    val SECURITY_PHONELOGIN: String
        get() = if (useObfuscatedUrl) "/api_prod/money_max_result/search" else "/security/phoneLogin"

    val VIDEO_CALL_PICKUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/message_average_group/download" else "/video-call/pickUp"

    val REVIEW_ZODIAC_INFO_FLOW: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/promotion_maximum_item/remove" else "/review/zodiac/info-flow"

    val LIVEROOM_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/report_top_item/delete" else "/liveRoom/list"

    val MSG_GETACTIVITYMSG: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/link_last_result/delete" else "/msg/getActivityMsg"

    val CONFIG_GETLANGVALUE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/goods_minimum_hash/del" else "/config/getLangValue"

    val VIDEO_CALL_ZEGO_RTCTOKENPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/doc_top_value/search" else "/video-call/zego/rtcTokenPostV2"

    val LIVE_ACTIVITY_CONFESSIONWALL_CONFESS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_first_json/upload" else "/live/activity/confessionWall/confess"

    val MG_ROOM_AUDIENCES: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/coin_top_group/patch" else "/mg-room/audiences"

    val ADS_GETUNLOCKCONFIG: String
        get() = if (useObfuscatedUrl) "/api_prod/msg_maximum_rank/set" else "/ads/getUnlockConfig"

    val SECURITY_EXISTSBYMOBILE: String
        get() = if (useObfuscatedUrl) "/api_prod/res/memory_min_list/del" else "/security/existsByMobile"

    val USER_GETUSERONLINESTATUSFORH5: String
        get() = if (useObfuscatedUrl) "/api_prod/document_max_string/delete" else "/user/getUserOnlineStatusForH5"

    val USER_ISGUARDBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/res/activity_min_group/search" else "/user/isGuardBroadcaster"

    val BROADCASTER_EXCHANGE_RATE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userinfo_average_value/patch" else "/broadcaster/exchange/rate"

    val RETRIEVE_CUSTOM_GOODSTRIGGER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_second_map/del" else "/retrieve/custom/goodsTrigger"

    val SHORTLINK_TARGETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_second_price/upload" else "/shortLink/targetPostV2"

    val BROADCASTER_VIDEOSTREAM_SEARCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/followlist_price/delete" else "/broadcaster/videoStream/search"

    val USER_SAVEMONEY_LISTPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/meminfo_minimum_array/upload" else "/user/saveMoney/listPostV2"

    val USER_V2_BROADCASTERRELATIONS: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/report_first_group/post" else "/user/v2/broadcasterRelations"

    val CONFIG_ACTIVITY_SLIDESHOWPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_minimum_string/upload" else "/config/activity/slideshowPostV2"

    val CALLBACK_GOOGLE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/followlist_average_json/delete" else "/callback/google"

    val GAME_FLOW_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/res/game_top_table/get" else "/game/flow/submit"

    val SECURITY_ACCOUNTPASSWORD: String
        get() = if (useObfuscatedUrl) "/api_prod/report_top_rank/set" else "/security/accountPassword"

    val SHOWROOMUSER_QUITSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/gift_last_json/post" else "/showRoomUser/quitSession"

    val ACTIVITY_ROULETTELOTTERY: String
        get() = if (useObfuscatedUrl) "/api_prod/game_first_price/set" else "/activity/rouletteLottery"

    val USER_ADDROBOTVISITOR: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/activity_maximum_price/update" else "/user/addRobotVisitor"

    val LIVE_GIFT_FAVORITE_UPSERT: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/order_maximum_key/delete" else "/live/gift/favorite/upsert"

    val USER_LIVEGIVEUSERGIFTS: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_minimum_json/download" else "/user/liveGiveUserGifts"

    val SHOWROOM_GETPRIVILEGEBYUSERID: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/harddisk_min_key/set" else "/showRoom/getPrivilegeByUserId"

    val USER_INCENTIVE_HAS: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/promotion_last_string/post" else "/user/incentive/has"

    val USER_GETMEDIAURLPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/money_last_key/remove" else "/user/getMediaUrlPostV2"

    val SECURITY_CAPTCHAAPP: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/stock_min_string/add" else "/security/captchaApp"

    val INFO_FLOW_COMMENT_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/userinfo_maximum_group/get" else "/info-flow/comment/list"

    val USER_GAME_SIGNPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/message_second_result/patch" else "/user/game/signPostV2"

    val SHOWROOMUSER_GETUSERSPECIALDAY: String
        get() = if (useObfuscatedUrl) "/api_prod/res/money_average_price/set" else "/showRoomUser/getUserSpecialDay"

    val VIDEO_CALL_MATCH_CANCEL: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/gift_first_list/search" else "/video-call/match/cancel"

    val GIFT_LIVE_LIST_GETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userinfo_last_map/set" else "/gift/live/list/getPostV2"

    val VIDEO_CALL_MEETLIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/doc_top_item/list" else "/video-call/meetList"

    val IMBACK_SENDCONFIRM: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/user_max_list/get" else "/IMBack/sendConfirm"

    val USER_GETLEVELINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/promotion_average_value/add" else "/user/getLevelInfoPostV2"

    val CALLBACK_OSS_CALLBACK: String
        get() = if (useObfuscatedUrl) "/api_prod/msg_second_result/update" else "/callback/oss/callback"

    val CONFIG_GETSTRATEGYFORH5POSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/meminfo_average_key/get" else "/config/getStrategyForH5PostV2"

    val BROADCASTER_EVALUATE_SUBMIT: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/agent_maximum_hash/download" else "/broadcaster/evaluate/submit"

    val USER_GETUSERINFOFORINDIAPKGPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/stock_average_result/del" else "/user/getUserInfoForIndiaPkgPostV2"

    val GAME_PAYPAL_ORDER_QUERY: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/message_first_table/download" else "/game/paypal-order/query"

    val USERLIVELEVEL_GRADEPOINT: String
        get() = if (useObfuscatedUrl) "/api_prod/promotion_maximum_list/set" else "/userLiveLevel/gradePoint"

    val USER_MODE_SWITCH: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/goods_maximum_rank/add" else "/user/mode/switch"

    val SHOWROOMTESTENV_UPLOADPRESIGNEDURL: String
        get() = if (useObfuscatedUrl) "/api_prod/res/mem_minimum_map/del" else "/showRoomTestEnv/uploadPreSignedUrl"

    val INFO_FLOW_COMMENT_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userinfo_top_list/del" else "/info-flow/comment/delete"

    val LIVE_ACTIVITY_H5_CONFIG_LIST: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/goods_average_list/delete" else "/live/activity/h5/config/list"

    val COIN_CALLCARD_RECEIVE: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_max_array/search" else "/coin/callCard/receive"

    val VIDEO_CALL_AUTOCALLV2_HANGUP: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/userinfo_map/search" else "/video-call/autoCallV2/hangUp"

    val USER_ACTION_REFUSECALLBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/gift_last_price/set" else "/user/action/refuseCallBroadcaster"

    val SHORTLINK_TARGET: String
        get() = if (useObfuscatedUrl) "/api_prod/network_second_json/add" else "/shortLink/target"

    val USER_GETCHARGEMEDIAPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/message_maximum_string/set" else "/user/getChargeMediaPostV2"

    val MG_ROOM_MIC_UP_EXPEDITE_INFO: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/userorder_average_hash/upload" else "/mg-room/mic/up/expedite/info"

    val USER_RECOMMENDGODDESS: String
        get() = if (useObfuscatedUrl) "/api_prod/v2/money_hash/add" else "/user/recommendGoddess"

    val REVIEW_CHERRY_VIDEO_FLOWSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/meminfo_top_string/del" else "/review/cherry/video-flowsPostV2"

    val VIDEO_CALL_CHANNEL_STREAM_START: String
        get() = if (useObfuscatedUrl) "/api_prod/userinfo_top_group/add" else "/video-call/channel/stream/start"

    val SHOWROOM_JOINSESSION: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/mem_minimum_key/delete" else "/showRoom/joinSession"

    val USER_UNLOCKMEDIA: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/userorder_maximum_key/list" else "/user/unlockMedia"

    val WIGO_INFO_FLOW_COMMENT_DELETE: String
        get() = if (useObfuscatedUrl) "/api_prod/goods_maximum_price/upload" else "/wigo/info-flow/comment-delete"

    val RANKACTIVITY_HERRANKNUMBER_FORBANNER: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/coin_first_key/get" else "/rankActivity/herRankNumber/forBanner"

    val SHOWROOM_BROADCASTERSPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v0/device_first_list/upload" else "/showRoom/broadcastersPostV2"

    val MG_ROOM_USERINFOPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/network_maximum_map/get" else "/mg-room/userInfoPostV2"

    val COIN_PRESENTED_GETPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/v3/gift_first_rank/del" else "/coin/presented/getPostV2"

    val USER_GETRANDOMBROADCASTER: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userorder_max_json/set" else "/user/getRandomBroadcaster"

    val LOG_LIVE_CHAT: String
        get() = if (useObfuscatedUrl) "/api_prod/v1/userorder_first_value/search" else "/log/live-chat"

    val LOG_LIVECHATPOSTV2: String
        get() = if (useObfuscatedUrl) "/api_prod/resource/order_average_table/upload" else "/log/liveChatPostV2"

} 