package com.stargate.pxo.data.network.client

import okhttp3.Interceptor
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * API客户端
 * 用于创建和配置Retrofit实例
 */
class ApiClient internal constructor(
    baseUrl: String,
    timeout: Long = DEFAULT_TIMEOUT,
    debug: Boolean = false,
    interceptors: List<Interceptor> = emptyList()
) {
    /**
     * Retrofit实例
     */
    private val retrofit: Retrofit
    
    /**
     * OkHttp客户端
     */
    private val okHttpClient: OkHttpClient
    
    init {
        // 构建OkHttp客户端
        val builder = OkHttpClient.Builder()
            .connectTimeout(timeout, TimeUnit.SECONDS)
            .readTimeout(timeout, TimeUnit.SECONDS)
            .writeTimeout(timeout, TimeUnit.SECONDS)

        // 添加自定义拦截器
        interceptors.forEach { interceptor ->
            builder.addInterceptor(interceptor)
        }
        
        okHttpClient = builder.build()
        
        // 构建Retrofit实例
        retrofit = Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    /**
     * 创建API服务接口实例
     */
    fun <T> create(serviceClass: Class<T>): T {
        return retrofit.create(serviceClass)
    }
    
    /**
     * 创建API服务接口实例（使用泛型扩展函数）
     */
    internal inline fun <reified T> create(): T {
        return retrofit.create(T::class.java)
    }
    
    companion object {
        /**
         * 默认超时时间（秒）
         */
        const val DEFAULT_TIMEOUT = 30L
        
        /**
         * 创建构建器
         */
        fun builder(): ApiClientBuilder {
            return ApiClientBuilder()
        }
    }
}