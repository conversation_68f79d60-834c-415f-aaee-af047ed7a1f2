package com.stargate.pxo.data.network.config

import com.stargate.pxo.common.constant.ApiConstants
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 网络配置类
 * 用于管理网络相关的配置参数
 */
@Singleton
class NetworkConfig @Inject constructor() {


    /**
     * API基础URL
     */
    val baseUrl: String = ApiConstants.BASE_URL
    
    /**
     * 是否开启调试模式
     */
    val isDebug: Boolean = true
    
    /**
     * 连接超时时间（秒）
     */
    val connectTimeout: Long = 30
    
    /**
     * 读取超时时间（秒）
     */
    val readTimeout: Long = 30
    
    /**
     * 写入超时时间（秒）
     */
    val writeTimeout: Long = 30
    
    /**
     * 最大缓存大小（MB）
     */
    val maxCacheSize: Long = 10 * 1024 * 1024 // 10MB
    
    /**
     * 缓存过期时间（秒）
     */
    val cacheExpiration: Long = 60 * 60 // 1小时
    
    /**
     * 重试次数
     */
    val retryCount: Int = 3
}