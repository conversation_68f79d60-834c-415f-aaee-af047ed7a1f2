# Puxxi 增强版网络和存储框架

基于统一的 Resource 封装和 DataRepository 的增强版网络和存储框架，提供更简洁、更灵活的 API 调用方式。

## 核心组件

### Resource

统一的资源状态封装，包含三种状态：

- `Success`: 成功状态，包含数据
- `Error`: 错误状态，包含错误信息
- `Loading`: 加载中状态

### DataRepository

统一的数据存储接口，支持内存缓存和数据库存储：

- 支持泛型方法，无需显式指定类型
- 支持多种获取策略
- 支持自动缓存和过期控制

### EnhancedNetworkClient

增强的网络客户端，使用 Resource 封装结果：

- 统一处理 HTTP 响应和异常
- 与 DataRepository 集成，提供缓存支持
- 支持同步和异步请求方式
- 优化的重试机制，支持指数退避策略
- 类型安全的响应处理，避免类型转换异常
- 统一的错误处理流程

### DomainMapper

领域模型映射器接口，用于自动转换 DTO 到领域模型：

- 标准化 DTO 和领域模型之间的转换
- 支持列表批量转换
- 避免重复的转换逻辑

## 使用示例

### 1. 在仓库中使用

```kotlin
@Singleton
class ProductRepository @Inject constructor(
    private val productApiService: ProductApiService,
    private val networkClient: EnhancedNetworkClient,
    private val productMapper: ProductMapper
) {
    fun getProductDetail(productId: String): Flow<Resource<Product>> {
        val cacheKey = "product_$productId"
        
        return networkClient.executeWithCache(
            cacheKey = cacheKey,
            fetchStrategy = FetchStrategy.CACHE_FIRST,
            expireTime = 30,
            timeUnit = TimeUnit.MINUTES
        ) {
            productApiService.getProductDetail(productId)
        }.map { result ->
            result.map { productMapper.mapToDomain(it) }
        }
    }
}
```

### 2. 在ViewModel中使用

```kotlin
@HiltViewModel
class ProductViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    // 使用扩展函数简化API调用
    fun getProductDetail(productId: String) {
        apiRequest(
            request = { productRepository.getProductDetail(productId) },
            onStart = { /* 显示加载状态 */ },
            onSuccess = { product -> /* 处理成功结果 */ },
            onError = { message, code -> /* 处理错误 */ }
        )
    }
}
```

### 3. 在UI中使用

```kotlin
@Composable
fun ProductDetailScreen(viewModel: ProductViewModel) {
    val productState by viewModel.productState.collectAsState()
    
    when (val state = productState) {
        is Resource.Loading -> LoadingIndicator()
        is Resource.Success -> ProductDetail(product = state.data)
        is Resource.Error -> ErrorMessage(message = state.message)
    }
}
```

## 获取策略

- `REMOTE_ONLY`: 只从远程获取，忽略缓存
- `CACHE_ONLY`: 只从缓存获取，不请求网络
- `CACHE_FIRST`: 优先从缓存获取，缓存不可用时请求网络
- `REMOTE_FIRST`: 优先从远程获取，远程失败时使用缓存
- `CACHE_AND_REMOTE`: 先返回缓存，然后请求远程更新

## 最佳实践

### 1. 选择合适的存储策略

- 对于频繁变化的数据，使用 `REMOTE_FIRST` 或 `REMOTE_ONLY`
- 对于相对稳定的数据，使用 `CACHE_FIRST`
- 对于大型列表数据，考虑缓存时间和存储空间

### 2. 使用状态流

```kotlin
// 在ViewModel中定义状态流
private val _productState = MutableStateFlow<Resource<Product>>(Resource.loading())
val productState: StateFlow<Resource<Product>> = _productState.asStateFlow()

// 更新状态流
fun getProductDetail(productId: String) {
    viewModelScope.launch {
        productRepository.getProductDetail(productId).collect { result ->
            _productState.value = result
        }
    }
}
```

### 3. 使用映射器

```kotlin
// 定义映射器
@Singleton
class ProductMapper @Inject constructor() : DomainMapper<ProductDto, Product> {
    override fun mapToDomain(entity: ProductDto): Product {
        return Product(
            id = entity.id,
            name = entity.name,
            // 其他属性映射
        )
    }
}

// 在仓库中使用
fun getProductList(): Flow<Resource<List<Product>>> {
    return networkClient.execute {
        productApiService.getProductList()
    }.map { result ->
        result.map { list -> productMapper.mapToDomainList(list) }
    }
}
```

## 重要优化

### 1. 类型安全的响应处理

EnhancedNetworkClient 使用类型安全的方式处理响应，避免类型转换异常：

```kotlin
// 优化后的handleResponse方法
private fun <T : Any> handleResponse(response: BaseResponse<T>): Resource<T> {
    if (!response.isSuccess) {
        val errorMessage = response.msg.ifEmpty { "Unknown error" }
        return Resource.error(
            message = errorMessage,
            code = response.code,
            exception = NetworkException.HttpError(response.code, errorMessage)
        )
    }

    val body = response.data
    
    return if (body != null) {
        // 直接使用响应数据，避免不安全的类型转换
        Resource.success(body)
    } else {
        Resource.error(
            message = "Empty response body",
            code = -1,
            exception = NetworkException.ParseError("Empty response body")
        )
    }
}
```

### 2. 优化的重试机制

使用辅助方法封装重试逻辑，支持指数退避策略：

```kotlin
// 重试辅助方法
private suspend fun <T> executeWithRetry(
    retryCount: Int,
    retryDelay: Long,
    block: suspend () -> T
): T {
    var currentDelay = retryDelay
    var lastException: Exception? = null
    
    for (attempt in 0..retryCount) {
        try {
            // 如果不是第一次尝试，则延迟
            if (attempt > 0) {
                delay(currentDelay)
                currentDelay = (currentDelay * 1.5).toLong() // 指数退避
            }
            
            // 执行代码块
            return block()
        } catch (e: Exception) {
            lastException = e
            // 如果是最后一次尝试，则抛出异常
            if (attempt == retryCount) {
                throw e
            }
            // 否则记录异常并继续重试
            LogUtil.w(TAG, "Attempt ${attempt+1} failed: ${e.message}. Retrying...")
        }
    }
    
    // 理论上不会执行到这里
    throw lastException ?: Exception("Unknown error")
}
```

## 迁移指南

从旧版框架迁移到增强版框架：

1. 使用 `EnhancedNetworkClient` 替代 `NetworkClient`
2. 使用 `DataRepository` 替代 `StorageManager` 和 `CacheManager`
3. 使用 `Resource` 替代 `ApiResult`
4. 创建并使用领域模型映射器
5. 在 ViewModel 中使用 API 请求扩展函数

## 配置示例

```kotlin
// 在NetworkModule中注册组件
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideDataRepository(
        memoryStorage: MemoryDataStorage,
        roomStorage: RoomDataStorage
    ): DataRepository {
        return DefaultDataRepository(memoryStorage, roomStorage)
    }
    
    @Provides
    @Singleton
    fun provideEnhancedNetworkClient(dataRepository: DataRepository): EnhancedNetworkClient {
        return EnhancedNetworkClient(dataRepository)
    }
} 