package com.stargate.pxo.data.network.storage.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query

/**
 * 缓存数据访问对象
 * 定义与数据库交互的操作
 */
@Dao
interface CacheDao {
    /**
     * 插入缓存数据，如果键已存在则替换
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(cacheEntity: CacheEntity)
    
    /**
     * 根据键获取缓存数据
     */
    @Query("SELECT * FROM cache_data WHERE `key` = :key")
    suspend fun getByKey(key: String): CacheEntity?
    
    /**
     * 根据键删除缓存数据
     */
    @Query("DELETE FROM cache_data WHERE `key` = :key")
    suspend fun deleteByKey(key: String)
    
    /**
     * 删除所有缓存数据
     */
    @Query("DELETE FROM cache_data")
    suspend fun deleteAll()
    
    /**
     * 删除过期的缓存数据
     * 注意：只能删除有过期时间的数据，永不过期的数据（expireTime=0）不会被删除
     */
    @Query("DELETE FROM cache_data WHERE expireTime > 0 AND (createTime + expireTime) < :currentTime")
    suspend fun deleteExpired(currentTime: Long)
    
    /**
     * 获取所有过期的缓存键
     */
    @Query("SELECT `key` FROM cache_data WHERE expireTime > 0 AND (createTime + expireTime) < :currentTime")
    suspend fun getExpiredKeys(currentTime: Long): List<String>
    
    /**
     * 获取所有缓存键
     */
    @Query("SELECT `key` FROM cache_data")
    suspend fun getAllKeys(): List<String>
    
    /**
     * 检查键是否存在
     */
    @Query("SELECT COUNT(*) FROM cache_data WHERE `key` = :key")
    suspend fun exists(key: String): Int
} 