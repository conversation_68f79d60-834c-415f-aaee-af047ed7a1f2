package com.stargate.pxo.data.network.storage

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 多级数据存储实现
 * 结合内存存储和数据库存储的优点，提供更高效的数据访问
 */
@Singleton
class MultiLevelDataStorage @Inject constructor(
    private val memoryStorage: MemoryDataStorage,
    private val roomStorage: RoomDataStorage,
    private val cacheMonitor: CacheMonitor
) : DataStorage {
    
    override suspend fun <T> saveData(key: String, data: T, expireTime: Long) = withContext(Dispatchers.IO) 
{
        // 同时写入内存和数据库
        @Suppress("UNCHECKED_CAST")
        memoryStorage.saveData(key, data, expireTime)
        roomStorage.saveData(key, data, expireTime)
        
        // 只需要通知一次监控器
        cacheMonitor.onCacheSaved(key, expireTime)
    }
    
    override suspend fun <T : Any> getData(key: String, type: Class<T>): T? = withContext(Dispatchers.IO) {
        // 先尝试从内存获取
        val memoryData = memoryStorage.getData(key, type)
        
        if (memoryData != null) {
            // 从内存获取成功，无需额外通知监控器（已在memoryStorage中通知）
            return@withContext memoryData
        }
        
        // 如果内存中没有，尝试从数据库获取
        val dbData = roomStorage.getData(key, type)
        
        // 如果数据库中有数据，将其加载到内存中以加速后续访问
        if (dbData != null) {
            // 从数据库中获取过期时间信息比较复杂，这里简化处理，使用默认过期时间
            // 实际应用中可以在CacheEntity中添加额外字段来存储这些信息
            memoryStorage.saveData(key, dbData, 0)
        }
        
        // 无需额外通知监控器（已在roomStorage中通知）
        dbData
    }
    
    override suspend fun hasValidData(key: String): Boolean = withContext(Dispatchers.IO) {
        // 检查内存或数据库中是否有有效数据
        val memoryHasData = memoryStorage.hasValidData(key)
        
        if (memoryHasData) {
            return@withContext true
        }
        
        val roomHasData = roomStorage.hasValidData(key)
        
        // 通知监控器（综合结果）
        if (!memoryHasData && !roomHasData) {
            cacheMonitor.onCacheAccessed(key, false)
        }
        
        roomHasData
    }
    
    override suspend fun removeData(key: String): Unit = withContext(Dispatchers.IO) {
        // 同时从内存和数据库中删除
        memoryStorage.removeData(key)
        roomStorage.removeData(key)
        
        // 只需要通知一次监控器
        cacheMonitor.onCacheRemoved(key)
    }
    
    override suspend fun clearAll() = withContext(Dispatchers.IO) {
        // 同时清除内存和数据库中的所有数据
        memoryStorage.clearAll()
        roomStorage.clearAll()
        
        // 监控器已在各自实现中通知
    }
    
    override suspend fun clearExpired() = withContext(Dispatchers.IO) {
        // 同时清除内存和数据库中的过期数据
        memoryStorage.clearExpired()
        roomStorage.clearExpired()
        
        // 监控器已在各自实现中通知
    }
} 