package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * API响应基类
 * 
 * @param code 状态码
 * @param msg 消息
 * @param data 数据
 * @param isSuccess 是否成功
 */
data class BaseResponse<T>(
    @SerializedName("code")
    val code: Int = 0,

    @SerializedName("msg")
    val msg: String = "",

    @SerializedName("data")
    val data: T? = null,

    @SerializedName("success")
    val isSuccess: Boolean = false,

    @SerializedName("key")
    val key: String = ""
) {
    /**
     * 判断响应是否成功
     * 必须同时满足：code == 0 AND success == true AND data != null
     */
    fun isSuccessful(): Boolean {
        return code == 0 && isSuccess && data != null
    }
    
    /**
     * 获取数据，如果数据为空则抛出异常
     */
    fun requireData(): T {
        return data ?: throw IllegalStateException("Response data is null")
    }
    
    /**
     * 获取数据，如果数据为空则返回默认值
     */
    fun getDataOrDefault(defaultValue: T): T {
        return data ?: defaultValue
    }
    
    /**
     * 判断响应是否为业务错误
     */
    fun isBusinessError(): Boolean {
        return !isSuccessful()
    }
} 