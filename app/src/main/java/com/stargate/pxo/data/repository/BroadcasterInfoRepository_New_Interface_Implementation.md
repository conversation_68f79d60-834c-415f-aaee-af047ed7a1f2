# BroadcasterInfoRepository 新接口实现

## 🎯 新增接口

在 BroadcasterInfo 接口中新增了 `getUserOnlineStatusPostV2` 方法，现已在 BroadcasterInfoRepository 中完成实现。

## 📋 接口对比

### 1. **getUserListOnlineStatusPostV3** (已存在)
```kotlin
// 接口定义
suspend fun getUserListOnlineStatusPostV3(
    @Url url: String = PuxxiUrl.USER_GETUSERLISTONLINESTATUSPOSTV3,
    @Body data: Map<String, Any> = mapOf()
): BaseResponse<List<Map<String, Any>>>

// Repository 实现
fun getUserListOnlineStatusPostV3(data: Map<String, Any> = mapOf()): Flow<Resource<List<Map<String, Any>>>>
```

**用途：** 批量获取多个主播的在线状态

### 2. **getUserOnlineStatusPostV2** (新增) ✅
```kotlin
// 接口定义
suspend fun getUserOnlineStatusPostV2(
    @Url url: String = PuxxiUrl.USER_GETUSERONLINESTATUSPOSTV2,
    @Body data: Map<String, Any> = mapOf()
): BaseResponse<String>

// Repository 实现
fun getUserOnlineStatusPostV2(data: Map<String, Any> = mapOf()): Flow<Resource<String>>
```

**用途：** 获取单个用户的在线状态

## 🔧 Repository 实现详情

### 新增的 getUserOnlineStatusPostV2 实现

```kotlin
/**
 * 获取单个用户在线状态
 * 仅从网络获取，不使用缓存
 */
fun getUserOnlineStatusPostV2(data: Map<String, Any> = mapOf()): Flow<Resource<String>> {
    return networkClient.execute(
        apiCall = {
            broadcasterInfo.getUserOnlineStatusPostV2(data = data)
        },
        retryCount = 2,
        retryDelay = 1000
    ).onEach { resource ->
        when (resource) {
            is Resource.Success -> {
                LogUtil.d("BroadcasterInfoRepository", "获取用户在线状态成功: ${resource.data}")
            }
            is Resource.Error -> {
                LogUtil.e("BroadcasterInfoRepository", "获取用户在线状态失败: ${resource.message}")
            }
            is Resource.Loading -> {
                LogUtil.d("BroadcasterInfoRepository", "正在获取用户在线状态...")
            }
        }
    }
}
```

### 实现特点

#### 1. **网络配置**
- ✅ **重试机制** - 失败时重试2次
- ✅ **重试延迟** - 每次重试间隔1秒
- ✅ **无缓存** - 直接从网络获取最新状态

#### 2. **日志记录**
- ✅ **成功日志** - 记录获取到的状态值
- ✅ **错误日志** - 记录失败原因
- ✅ **加载日志** - 记录请求进度

#### 3. **返回类型**
- ✅ **Flow<Resource<String>>** - 响应式数据流
- ✅ **String 类型** - 直接返回状态字符串

## 📊 两个接口的使用场景

### getUserListOnlineStatusPostV3 (批量查询)

#### 适用场景：
- 🏠 **主播墙页面** - 批量查询当前页面所有主播状态
- 💬 **消息列表** - 批量查询所有聊天对象状态
- 👥 **关注列表** - 批量查询关注的主播状态

#### 使用示例：
```kotlin
// 在 BroadcasterStatusManager 中使用
fun requestStatusBatch(userIds: List<String>) {
    val requestData = mapOf("userIds" to userIds)
    
    broadcasterInfoRepository.getUserListOnlineStatusPostV3(requestData)
        .collect { resource ->
            when (resource) {
                is Resource.Success -> {
                    handleBatchStatusResponse(resource.data)
                }
                // ... 其他处理
            }
        }
}
```

### getUserOnlineStatusPostV2 (单个查询)

#### 适用场景：
- 👤 **个人资料页** - 查询特定用户的状态
- 📞 **通话前检查** - 确认对方是否在线
- 🔍 **搜索结果** - 查询搜索到的用户状态
- 💬 **私聊窗口** - 实时显示对方状态

#### 使用示例：
```kotlin
// 在 ProfileViewModel 中使用
fun checkUserStatus(userId: String) {
    val requestData = mapOf("userId" to userId)
    
    viewModelScope.launch {
        broadcasterInfoRepository.getUserOnlineStatusPostV2(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        val status = resource.data
                        updateUserStatus(userId, status)
                    }
                    is Resource.Error -> {
                        showError("获取用户状态失败")
                    }
                    is Resource.Loading -> {
                        showLoading()
                    }
                }
            }
    }
}
```

## 🎯 在 BroadcasterStatusManager 中的集成

### 可以根据需要选择合适的接口

```kotlin
class BroadcasterStatusManager {
    
    /**
     * 批量请求状态（推荐用于轮询）
     */
    private suspend fun requestStatusBatchInternal(userIds: List<String>) {
        val requestData = mapOf("userIds" to userIds)
        
        broadcasterInfoRepository.getUserListOnlineStatusPostV3(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        handleStatusResponse(resource.data)
                    }
                    // ... 其他处理
                }
            }
    }
    
    /**
     * 单个请求状态（用于特殊场景）
     */
    suspend fun requestSingleUserStatus(userId: String) {
        val requestData = mapOf("userId" to userId)
        
        broadcasterInfoRepository.getUserOnlineStatusPostV2(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        val status = resource.data
                        updateSingleUserStatus(userId, status)
                    }
                    // ... 其他处理
                }
            }
    }
}
```

## 📈 性能对比

### 批量查询 vs 单个查询

#### getUserListOnlineStatusPostV3 (批量)
- ✅ **网络效率高** - 一次请求获取多个状态
- ✅ **适合轮询** - 减少网络请求次数
- ✅ **服务器友好** - 减少服务器负载

#### getUserOnlineStatusPostV2 (单个)
- ✅ **响应速度快** - 单个查询响应更快
- ✅ **精确控制** - 只查询需要的用户
- ✅ **实时性好** - 适合实时状态检查

### 使用建议

```kotlin
// ✅ 推荐：批量查询（轮询场景）
val userIds = listOf("user1", "user2", "user3", "user4", "user5")
broadcasterInfoRepository.getUserListOnlineStatusPostV3(mapOf("userIds" to userIds))

// ✅ 推荐：单个查询（实时检查场景）
broadcasterInfoRepository.getUserOnlineStatusPostV2(mapOf("userId" to "user123"))

// ❌ 不推荐：多次单个查询
userIds.forEach { userId ->
    broadcasterInfoRepository.getUserOnlineStatusPostV2(mapOf("userId" to userId))
}
```

## 🔍 调试和监控

### 日志标签

搜索 `BroadcasterInfoRepository` 可以看到：

```
D/BroadcasterInfoRepository: 正在批量获取主播状态...
D/BroadcasterInfoRepository: 批量获取主播状态成功，数量: 10
D/BroadcasterInfoRepository: 正在获取用户在线状态...
D/BroadcasterInfoRepository: 获取用户在线状态成功: online
```

### 错误处理

```
E/BroadcasterInfoRepository: 批量获取主播状态失败: Network timeout
E/BroadcasterInfoRepository: 获取用户在线状态失败: User not found
```

## ✅ 总结

**BroadcasterInfoRepository 新接口实现完成！**

### 新增功能：
1. ✅ **getUserOnlineStatusPostV2** - 单个用户状态查询
2. ✅ **完整的错误处理** - 包含重试机制和日志记录
3. ✅ **响应式设计** - 返回 Flow<Resource<String>>

### 使用场景：
- 🔄 **批量查询** - 使用 getUserListOnlineStatusPostV3
- 🎯 **单个查询** - 使用 getUserOnlineStatusPostV2
- 📱 **灵活选择** - 根据具体需求选择合适的接口

### 技术特点：
- ⚡ **高性能** - 支持重试和错误恢复
- 📝 **完整日志** - 便于调试和监控
- 🔄 **响应式** - 基于 Flow 的异步处理

现在你可以在任何需要查询用户状态的地方使用这两个接口了！
