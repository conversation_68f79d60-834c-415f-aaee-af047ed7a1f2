package com.stargate.pxo.data.network.storage

/**
 * 缓存键生成和管理工具
 */
object CacheKeys {
    // 缓存键前缀
    private const val PREFIX = "puxxi_cache_"
    
    // 常量键，按模块分类
    object User {
        const val PROFILE = "${PREFIX}user_profile"
        const val SETTINGS = "${PREFIX}user_settings"
        const val PREFERENCES = "${PREFIX}user_preferences"
        
        /**
         * 生成特定用户的缓存键
         */
        fun profile(userId: String) = "${PROFILE}_$userId"
    }
    
    object Content {
        const val FEED = "${PREFIX}content_feed"
        const val RECOMMENDED = "${PREFIX}content_recommended"
        
        /**
         * 生成特定内容的缓存键
         */
        fun item(contentId: String) = "${PREFIX}content_item_$contentId"
        
        /**
         * 生成特定类型内容列表的缓存键
         */
        fun list(type: String, page: Int = 0) = "${PREFIX}content_list_${type}_$page"
    }
    
    object Config {
        const val APP_CONFIG = "${PREFIX}app_config"
        const val THEME = "${PREFIX}app_theme"
        const val FEATURE_FLAGS = "${PREFIX}feature_flags"
        const val SERVER_CONFIG = "${PREFIX}server_config"
        
        /**
         * 生成应用配置缓存键
         */
        fun appConfig() = APP_CONFIG
        
        /**
         * 生成主题配置缓存键
         */
        fun theme() = THEME
        
        /**
         * 生成功能开关配置缓存键
         */
        fun featureFlags() = FEATURE_FLAGS
        
        /**
         * 生成服务器配置缓存键
         */
        fun serverConfig() = SERVER_CONFIG
    }
    
    /**
     * 生成带参数的缓存键
     * @param base 基础键名
     * @param params 参数键值对
     * @return 格式化的缓存键
     */
    fun with(base: String, vararg params: Pair<String, Any>): String {
        if (params.isEmpty()) return base
        
        val paramString = params.joinToString("_") { (key, value) ->
            "${key}_$value"
        }
        
        return "${base}_$paramString"
    }
    
    /**
     * 生成特定API请求的缓存键
     * @param endpoint API端点
     * @param params 请求参数
     * @return 格式化的缓存键
     */
    fun forApi(endpoint: String, vararg params: Pair<String, Any>): String {
        val sanitizedEndpoint = endpoint
            .replace("/", "_")
            .replace("-", "_")
            .lowercase()
        
        return with("${PREFIX}api_$sanitizedEndpoint", *params)
    }
    
    /**
     * 生成用于分页数据的缓存键
     * @param base 基础键名
     * @param page 页码
     * @param size 页大小
     * @return 格式化的缓存键
     */
    fun forPagination(base: String, page: Int, size: Int): String {
        return "${base}_page_${page}_size_$size"
    }
} 