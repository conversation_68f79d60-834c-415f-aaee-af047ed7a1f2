package com.stargate.pxo.data.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.stargate.pxo.data.db.dao.BroadcasterDao
import com.stargate.pxo.data.db.dao.UserDao
import com.stargate.pxo.data.db.entity.BroadcasterEntity
import com.stargate.pxo.data.db.entity.UserEntity

/**
 * 应用数据库
 */
@Database(
    entities = [
        UserEntity::class,
        BroadcasterEntity::class
    ],
    version = 2,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {

    /**
     * 获取用户Dao
     */
    abstract fun userDao(): UserDao

    /**
     * 获取主播Dao
     */
    abstract fun broadcasterDao(): BroadcasterDao
    
    companion object {
        private const val DATABASE_NAME = "puxxi_db"
        
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        /**
         * 获取数据库实例
         */
        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .fallbackToDestructiveMigration() // 如果数据库版本变更，重新创建数据库
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
} 