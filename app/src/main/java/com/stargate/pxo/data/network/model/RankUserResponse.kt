package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

/**
 * 排行榜数据
 */
data class RankUserResponse(
    @SerializedName("monthName")
    val monthName: String,
    @SerializedName("sortNo")
    val sortNo: String,
    @SerializedName("rankData")
    val rankData: List<RankUserItem>
)

/**
 * 排行榜用户项
 */
data class RankUserItem(
    @SerializedName("sort")
    val sort: Int,
    @SerializedName("userId")
    val userId: String,
    @SerializedName("liveLevel")
    val liveLevel: Int,
    @SerializedName("hidingLiveLevel")
    val hidingLiveLevel: Boolean? = null,
    @SerializedName("nickname")
    val nickname: String,
    @SerializedName("avatar")
    val avatar: String,
    @SerializedName("avatarMapPath")
    val avatarMapPath: String,
    @SerializedName("sets")
    val sets: UserDecorationSets
)

/**
 * 用户装饰套装
 */
data class UserDecorationSets(
    @SerializedName("medalList")
    val medalList: List<MedalItem>? = null,
    @SerializedName("avatarFrame")
    val avatarFrame: DecorationResource? = null,
    @SerializedName("medal")
    val medal: DecorationResource? = null,
    @SerializedName("entranceCar")
    val entranceCar: DecorationResource? = null,
    @SerializedName("cardSkin")
    val cardSkin: DecorationResource? = null
)

/**
 * 勋章项
 */
data class MedalItem(
    @SerializedName("mp4")
    val mp4: List<String>? = null,
    @SerializedName("png")
    val png: List<String>? = null,
    @SerializedName("svga")
    val svga: List<String>? = null
)

/**
 * 装饰资源
 */
data class DecorationResource(
    @SerializedName("mp4")
    val mp4: List<String>? = null,
    @SerializedName("png")
    val png: List<String>? = null,
    @SerializedName("svga")
    val svga: List<String>? = null
)