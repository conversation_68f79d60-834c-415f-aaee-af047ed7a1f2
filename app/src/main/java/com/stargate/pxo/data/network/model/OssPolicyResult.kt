package com.stargate.pxo.data.network.model

import com.google.gson.annotations.SerializedName

data class OssPolicyResult (
    @SerializedName("accessKeyId")
    val accessKeyId: String,
    
    @SerializedName("policy")
    val policy: String,
    
    @SerializedName("signature")
    val signature: String,
    
    @SerializedName("expire")
    val expire: String,
    
    @SerializedName("dir")
    val dir: String,
    
    @SerializedName("host")
    val host: String,
    
    @SerializedName("callback")
    val callback: String,
  )


