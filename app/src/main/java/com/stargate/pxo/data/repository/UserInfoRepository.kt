package com.stargate.pxo.data.repository

import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.api.UserInfoNetInterface
import com.stargate.pxo.data.network.client.EnhancedNetworkClient
import com.stargate.pxo.data.network.model.OssPolicyResult
import com.stargate.pxo.data.network.model.UpdateAvatarResult
import com.stargate.pxo.data.network.model.UserInfo
import com.stargate.pxo.data.network.storage.DataRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户信息Repository
 * 负责处理用户信息相关的网络请求和数据管理
 */
@Singleton
class UserInfoRepository @Inject constructor(
    private val userInfoNetInterface: UserInfoNetInterface,
    private val networkClient: EnhancedNetworkClient,
) {
    
    companion object {
        private const val TAG = "UserInfoRepository"
    }
    
    /**
     * 保存用户信息
     */
    fun saveUserInfo(data: Map<String, Any>): Flow<Resource<Any>> {
        return networkClient.execute(
            apiCall = {
                userInfoNetInterface.saveUserInfo(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
    
    /**
     * 获取OSS上传权限
     */
    fun getUserOssPolicyPostV2(data: Map<String, Any> = mapOf()): Flow<Resource<OssPolicyResult>> {
        return networkClient.execute(
            apiCall = {
                userInfoNetInterface.getUserOssPolicyPostV2(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }

    /**
     * 更新用户头像
     * 仅进行网络请求，不使用缓存
     */
    fun userUpdateAvatar(data: Map<String, Any>): Flow<Resource<UpdateAvatarResult>> {
        return networkClient.execute(
            apiCall = {
                userInfoNetInterface.userUpdateAvatar(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
    
    /**
     * 获取用户信息
     * 获取用户的详细信息
     */
    fun getUserInfo(data: Map<String, Any> = mapOf()): Flow<Resource<UserInfo>> {
        return networkClient.execute(
            apiCall = {
                userInfoNetInterface.getUserInfo(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        ).onEach { resource ->
            // 获取用户信息成功后的处理
            if (resource is Resource.Success) {
                processGetUserInfoResponse(resource.data)
            }
        }
    }

    /**
     * 刷新用户信息
     */
    fun refreshUserInfo(): Flow<Resource<UserInfo>> {
        return networkClient.execute(
            apiCall = {
                userInfoNetInterface.getUserInfo()
            },
            retryCount = 2,
            retryDelay = 1000
        ).onEach { resource ->
            if (resource is Resource.Success) {
                processGetUserInfoResponse(resource.data)
            }
        }
    }
}


/**
 * 处理获取用户信息响应
 * 在这里可以处理获取用户信息成功后的逻辑
 */
private fun processGetUserInfoResponse(userInfo: UserInfo) {
    LogUtil.d("UserInfoRepository", "获取用户信息成功: ${userInfo.nickname}, ID: ${userInfo.userId}")
    // 这里可以添加获取用户信息成功后的额外处理逻辑
    // 比如更新本地缓存、更新UserManager等
    UserManager.updateUserInfo { _ -> userInfo }
}