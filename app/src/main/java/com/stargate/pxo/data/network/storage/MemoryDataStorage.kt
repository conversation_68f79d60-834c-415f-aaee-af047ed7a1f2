package com.stargate.pxo.data.network.storage

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内存数据存储实现
 * 使用内存缓存数据，应用重启后数据会丢失
 */
@Singleton
class MemoryDataStorage @Inject constructor(
    private val cacheMonitor: CacheMonitor
) : DataStorage {
    
    /**
     * 缓存项
     */
    private data class CacheItem<T>(
        val data: T,
        val createTime: Long,
        val expireTime: Long
    ) {
        /**
         * 检查数据是否已过期
         */
        fun isExpired(): Boolean {
            if (expireTime <= 0) {
                return false // 永不过期
            }
            val currentTime = System.currentTimeMillis()
            return currentTime > (createTime + expireTime)
        }
    }
    
    // 使用ConcurrentHashMap保证线程安全
    private val cache = ConcurrentHashMap<String, CacheItem<Any>>()
    
    override suspend fun <T> saveData(key: String, data: T, expireTime: Long) = withContext(Dispatchers.IO) {
        @Suppress("UNCHECKED_CAST")
        cache[key] = CacheItem(
            data = data as Any,
            createTime = System.currentTimeMillis(),
            expireTime = expireTime
        )
        
        // 监控缓存保存
        cacheMonitor.onCacheSaved(key, expireTime)
    }
    
    @Suppress("UNCHECKED_CAST")
    override suspend fun <T : Any> getData(key: String, type: Class<T>): T? = withContext(Dispatchers.IO) {
        val item = cache[key]
        
        if (item == null || item.isExpired()) {
            if (item?.isExpired() == true) {
                // 如果数据已过期，删除它
                cache.remove(key)
                cacheMonitor.onCacheExpired(key)
            }
            // 监控缓存访问（未命中）
            cacheMonitor.onCacheAccessed(key, false)
            null
        } else {
            try {
                // 监控缓存访问（命中）
                cacheMonitor.onCacheAccessed(key, true)
                
                // 使用明确的类型转换，确保类型安全
                if (type.isInstance(item.data)) {
                    type.cast(item.data)
                } else {
                    // 类型不匹配
                    cacheMonitor.onCacheAccessed(key, false)
                    null
                }
            } catch (e: Exception) {
                // 监控缓存访问（类型转换失败）
                cacheMonitor.onCacheAccessed(key, false)
                null
            }
        }
    }
    
    override suspend fun hasValidData(key: String): Boolean = withContext(Dispatchers.IO) {
        val item = cache[key]
        val isValid = item != null && !item.isExpired()
        cacheMonitor.onCacheAccessed(key, isValid)
        isValid
    }
    
    override suspend fun removeData(key: String): Unit = withContext(Dispatchers.IO) {
        cache.remove(key)
        cacheMonitor.onCacheRemoved(key)
    }
    
    override suspend fun clearAll() = withContext(Dispatchers.IO) {
        val keys = cache.keys().toList()
        cache.clear()
        keys.forEach { key ->
            cacheMonitor.onCacheRemoved(key)
        }
    }
    
    override suspend fun clearExpired() = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        val expiredKeys = cache.entries
            .filter { (_, item) -> item.isExpired() }
            .map { it.key }
        
        expiredKeys.forEach { key ->
            cache.remove(key)
            cacheMonitor.onCacheExpired(key)
        }
    }
} 