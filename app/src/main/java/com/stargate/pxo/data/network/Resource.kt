package com.stargate.pxo.data.network

/**
 * 统一资源状态封装
 * 用于表示数据加载的不同状态（加载中、成功、失败）
 */
sealed class Resource<out T> {
    /**
     * 加载中状态
     */
    object Loading : Resource<Nothing>()
    
    /**
     * 成功状态，包含数据
     */
    data class Success<T>(val data: T) : Resource<T>()
    
    /**
     * 错误状态，包含错误信息
     */
    data class Error(
        val message: String, 
        val code: Int = -1, 
        val exception: Throwable? = null
    ) : Resource<Nothing>()
    
    /**
     * 判断是否成功
     */
    val isSuccess: Boolean get() = this is Success
    
    /**
     * 判断是否失败
     */
    val isError: Boolean get() = this is Error
    
    /**
     * 判断是否加载中
     */
    val isLoading: Boolean get() = this is Loading
    
    /**
     * 获取成功数据，如果不是成功状态则返回null
     */
    fun getOrNull(): T? = when (this) {
        is Success -> data
        else -> null
    }
    
    /**
     * 获取成功数据，如果不是成功状态则返回默认值
     */
    fun getOrDefault(defaultValue: @UnsafeVariance T): T = when (this) {
        is Success -> data
        else -> defaultValue
    }
    
    /**
     * 获取错误信息，如果不是错误状态则返回null
     */
    fun errorOrNull(): Error? = when (this) {
        is Error -> this
        else -> null
    }
    
    /**
     * 转换数据类型
     */
    fun <R> map(transform: (T) -> R): Resource<R> = when (this) {
        is Success -> Success(transform(data))
        is Error -> this
        is Loading -> Loading
    }
    
    /**
     * 处理各种状态
     */
    inline fun <R> fold(
        onSuccess: (T) -> R,
        onError: (Error) -> R,
        onLoading: () -> R
    ): R = when (this) {
        is Success -> onSuccess(data)
        is Error -> onError(this)
        is Loading -> onLoading()
    }
    
    /**
     * 处理成功状态
     */
    inline fun onSuccess(action: (T) -> Unit): Resource<T> {
        if (this is Success) action(data)
        return this
    }
    
    /**
     * 处理错误状态
     */
    inline fun onError(action: (Error) -> Unit): Resource<T> {
        if (this is Error) action(this)
        return this
    }
    
    /**
     * 处理加载状态
     */
    inline fun onLoading(action: () -> Unit): Resource<T> {
        if (this is Loading) action()
        return this
    }
    
    companion object {
        /**
         * 创建成功结果
         */
        fun <T> success(data: T): Resource<T> = Success(data)
        
        /**
         * 创建错误结果
         */
        fun error(
            message: String, 
            code: Int = -1, 
            exception: Throwable? = null
        ): Resource<Nothing> = Error(message, code, exception)
        
        /**
         * 创建加载中结果
         */
        fun loading(): Resource<Nothing> = Loading
        
        /**
         * 从结果或异常创建资源
         */
        fun <T> from(block: () -> T): Resource<T> = try {
            success(block())
        } catch (e: Exception) {
            error(e.message ?: "Unknown error", exception = e)
        }
    }
} 