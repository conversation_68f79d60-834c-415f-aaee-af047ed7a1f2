package com.stargate.pxo.data.network.api

import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.model.BaseResponse
import com.stargate.pxo.data.network.model.LoginResponse
import com.stargate.pxo.data.network.model.OssPolicyResult
import com.stargate.pxo.data.network.model.UpdateAvatarResult
import com.stargate.pxo.data.network.model.UserInfo
import retrofit2.http.Body
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Url

interface UserInfoNetInterface {

    /**
     * 保存用户基本信息
     */
    @POST
    @Headers("Content-Type: application/json")
    @JvmSuppressWildcards
    suspend fun saveUserInfo(
        @Url url: String = PuxxiUrl.USER_SAVEUSERINFO,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<Boolean>

    /**
     * 获取oss上传权限
     */
    @POST
    @JvmSuppressWildcards
    suspend fun getUserOssPolicyPostV2(
        @Url url: String = PuxxiUrl.USER_SAVEBASICINFO,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<OssPolicyResult>



    /**
     * 更新用户头像
     */
    @POST
    @JvmSuppressWildcards
    suspend fun userUpdateAvatar(
        @Url url: String = PuxxiUrl.USER_UPDATEAVATAR,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<UpdateAvatarResult>



    /**
     * 获取用户信息
     */
    @POST
    @JvmSuppressWildcards
    suspend fun getUserInfo(
        @Url url: String = PuxxiUrl.USER_GETUSERINFOPOSTV2,
        @Body data: Map<String, Any> = mapOf()
    ): BaseResponse<UserInfo>





}