package com.stargate.pxo.data.network.client

import okhttp3.Interceptor

/**
 * ApiClient构建器
 * 用于创建和配置ApiClient实例
 */
class ApiClientBuilder {
    private var baseUrl: String = ""
    private var timeout: Long = ApiClient.DEFAULT_TIMEOUT
    private var debug: Boolean = false
    private val interceptors: MutableList<Interceptor> = mutableListOf()
    
    /**
     * 设置基础URL
     */
    fun baseUrl(url: String): ApiClientBuilder {
        this.baseUrl = url
        return this
    }
    
    /**
     * 设置超时时间
     */
    fun timeout(seconds: Long): ApiClientBuilder {
        this.timeout = seconds
        return this
    }
    
    /**
     * 设置是否开启调试模式
     */
    fun debug(enable: Boolean): ApiClientBuilder {
        this.debug = enable
        return this
    }
    
    /**
     * 添加拦截器
     */
    fun addInterceptor(interceptor: Interceptor): ApiClientBuilder {
        this.interceptors.add(interceptor)
        return this
    }
    
    /**
     * 构建ApiClient实例
     */
    fun build(): ApiClient {
        require(baseUrl.isNotEmpty()) { "Base URL cannot be empty" }
        return ApiClient(baseUrl, timeout, debug, interceptors)
    }
}