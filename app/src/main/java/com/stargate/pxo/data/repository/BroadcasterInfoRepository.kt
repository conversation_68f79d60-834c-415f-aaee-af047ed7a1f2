package com.stargate.pxo.data.repository

import com.google.gson.reflect.TypeToken
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.db.dao.BroadcasterDao
import com.stargate.pxo.data.mapper.BroadcasterMapper
import com.stargate.pxo.data.network.PuxxiUrl
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.api.BroadcasterInfo
import com.stargate.pxo.data.network.client.EnhancedNetworkClient
import com.stargate.pxo.data.network.model.BannerInfo
import com.stargate.pxo.data.network.model.BroadcasterItem
import com.stargate.pxo.data.network.storage.CacheKeys
import com.stargate.pxo.data.network.storage.DataRepository
import com.stargate.pxo.data.network.storage.FetchStrategy
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主播信息Repository
 * 负责处理主播相关的网络请求
 */
@Singleton
class BroadcasterInfoRepository @Inject constructor(
    private val broadcasterInfo: BroadcasterInfo,
    private val networkClient: EnhancedNetworkClient,
    private val dataRepository: DataRepository,
    private val broadcasterDao: BroadcasterDao
) {
    
    /**
     * 获取Banner信息
     * 使用接口作为key进行缓存，executeWithCacheTyped自动处理缓存逻辑
     */
    fun getBannerInfo(data: Map<String, Any> = mapOf()): Flow<Resource<List<BannerInfo>>> {
        return networkClient.execute(
//            cacheKey = CacheKeys.forApi(PuxxiUrl.GAME_BANNER_INFO), // 使用接口URL作为缓存key
//            fetchStrategy = FetchStrategy.CACHE_FIRST, // 缓存优先
//            expireTime = BANNER_CACHE_EXPIRE_TIME,
//            timeUnit = TimeUnit.HOURS,
            apiCall = {
                broadcasterInfo.getBannerInfo(data = data)
            }
        ).onEach { resource ->
            // executeWithCacheTyped已经自动处理了缓存逻辑
            // Repository只需要记录日志或处理其他业务逻辑
            when (resource) {
                is Resource.Success -> {
                    LogUtil.d("BroadcasterInfoRepository", "Banner信息获取成功，数量: ${resource.data.size}")
                }
                is Resource.Error -> {
                    LogUtil.e("BroadcasterInfoRepository", "Banner信息获取失败: ${resource.message}")
                }
                is Resource.Loading -> {
                    LogUtil.d("BroadcasterInfoRepository", "正在获取Banner信息...")
                }
            }
        }
    }
    
    /**
     * 获取主播状态列表
     * 仅从网络获取，不使用缓存
     */
    fun getUserListOnlineStatusPostV3(data: Map<String, Any> = mapOf()): Flow<Resource<Map<String, Any>>> {
        return networkClient.execute(
            apiCall = {
                broadcasterInfo.getUserListOnlineStatusPostV3(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }

    /**
     * 获取单个用户在线状态
     * 仅从网络获取，不使用缓存
     */
    fun getUserOnlineStatusPostV2(data: Map<String, Any> = mapOf()): Flow<Resource<String>> {
        return networkClient.execute(
            apiCall = {
                broadcasterInfo.getUserOnlineStatusPostV2(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        )
    }
    
    /**
     * 获取主播墙列表
     * 将每个主播信息存储到数据库，使用userId作为主键
     */
    fun getBroadcasterWall(data: Map<String, Any> = mapOf()): Flow<Resource<List<BroadcasterItem>>> {
        return networkClient.execute(
            apiCall = {
                broadcasterInfo.getBroadcasterWall(data = data)
            },
            retryCount = 2,
            retryDelay = 1000
        ).onEach { resource ->
            // 处理数据库存储逻辑
            if (resource is Resource.Success) {
                try {
                    val broadcasters = resource.data
                    LogUtil.d("BroadcasterInfoRepository", "开始存储主播数据到数据库，数量: ${broadcasters.size}")

                    // 将网络模型转换为数据库实体
                    val entities = BroadcasterMapper.fromNetworkModels(broadcasters)

                    // 存储到数据库，使用userId作为主键（自动处理重复数据）
                    broadcasterDao.insertBroadcasters(entities)

                    LogUtil.d("BroadcasterInfoRepository", "主播数据存储到数据库成功")
                } catch (e: Exception) {
                    LogUtil.e("BroadcasterInfoRepository", "存储主播数据到数据库失败", e)
                    // 数据库存储失败不影响网络数据的返回
                }
            }
        }
    }
    
    /**
     * 从数据库获取主播列表
     * 可以作为离线数据或缓存数据使用
     */
    suspend fun getBroadcastersFromDatabase(): List<BroadcasterItem> {
        return try {
            val entities = broadcasterDao.getAllBroadcasters()
            val broadcasters = BroadcasterMapper.toNetworkModels(entities)
            LogUtil.d("BroadcasterInfoRepository", "从数据库获取主播数据成功，数量: ${broadcasters.size}")
            broadcasters
        } catch (e: Exception) {
            LogUtil.e("BroadcasterInfoRepository", "从数据库获取主播数据失败", e)
            emptyList()
        }
    }

    /**
     * 清除所有主播数据
     */
    suspend fun clearAllBroadcasters() {
        try {
            broadcasterDao.deleteAllBroadcasters()
            LogUtil.d("BroadcasterInfoRepository", "清除所有主播数据成功")
        } catch (e: Exception) {
            LogUtil.e("BroadcasterInfoRepository", "清除主播数据失败", e)
        }
    }

    /**
     * 清除Banner缓存
     */
    suspend fun clearBannerCache() {
        dataRepository.clear(CacheKeys.forApi(PuxxiUrl.GAME_BANNER_INFO))
    }
}
