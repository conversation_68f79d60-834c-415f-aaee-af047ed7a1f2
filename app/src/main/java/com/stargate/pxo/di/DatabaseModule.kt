package com.stargate.pxo.di

import android.content.Context
import com.stargate.pxo.data.db.AppDatabase
import com.stargate.pxo.data.db.dao.BroadcasterDao
import com.stargate.pxo.data.db.dao.UserDao
import com.stargate.pxo.data.manager.UserManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库模块
 * 提供数据库和DAO的依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * 提供数据库实例
     */
    @Singleton
    @Provides
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getInstance(context)
    }
    
    /**
     * 提供UserDao
     */
    @Singleton
    @Provides
    fun provideUserDao(database: AppDatabase): UserDao {
        return database.userDao()
    }

    /**
     * 提供BroadcasterDao
     */
    @Singleton
    @Provides
    fun provideBroadcasterDao(database: AppDatabase): BroadcasterDao {
        return database.broadcasterDao()
    }

    /**
     * 初始化UserManager
     */
    @Singleton
    @Provides
    fun provideUserManager(userDao: UserDao): UserManager {
        // 初始化UserManager
        UserManager.init(userDao)
        return UserManager
    }
} 