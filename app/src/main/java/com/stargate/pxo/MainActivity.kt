package com.stargate.pxo

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.core.view.WindowCompat
import androidx.navigation.compose.rememberNavController
import com.stargate.pxo.presentation.ui.navigation.PuxxiNavigation
import com.stargate.pxo.presentation.ui.navigation.NavigationManager
import com.stargate.pxo.presentation.ui.theme.PuxxiTheme
import com.stargate.pxo.presentation.ui.view.GlobalLoadingView
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 沉浸式设置已移到 PuxxiTheme 中统一处理

        setContent {
            PuxxiTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()

                    // 初始化全局导航管理器
                    LaunchedEffect(navController) {
                        NavigationManager.initialize(navController)
                    }

                    PuxxiNavigation(navController = navController)
                    GlobalLoadingView()
                }
            }
        }
    }
}