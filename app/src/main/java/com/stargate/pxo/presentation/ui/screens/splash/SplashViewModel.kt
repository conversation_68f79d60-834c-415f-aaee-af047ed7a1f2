package com.stargate.pxo.presentation.ui.screens.splash

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.repository.AppNetInterfaceRepository
import com.stargate.pxo.presentation.ui.view.GlobalLoadingManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Splash screen state
 */
data class SplashState(
    val isLoading: Boolean = true,
    val navigateToMain: Boolean = false,
    val navigateToLogin: Boolean = false,
    val error: String? = null
) : UiState

/**
 * Splash screen ViewModel
 */
@HiltViewModel
class SplashViewModel @Inject constructor(
    private val appNetInterfaceRepository : AppNetInterfaceRepository
) : BaseViewModel<SplashState>() {
    

    override fun createInitialState(): SplashState = SplashState()
    
    fun startSplashTimer() {
        viewModelScope.launch {
            try {
                initializeAppConfig()
            } catch (e: Exception) {
                updateState { copy(isLoading = false, error = e.message) }
            }
        }
    }
    

    fun resetNavigation() {
        updateState { copy(navigateToMain = false, navigateToLogin = false) }
    }

    /**
     * 初始化应用配置
     * 使用PuxxiCoroutine.io进行IO操作
     */
    private fun initializeAppConfig() {
        PuxxiCoroutine.io(
            tag = "AppConfig_Init",
            onError = { error ->
                // 配置初始化失败，使用默认配置
                LogUtil.e("SplashViewModel", "配置初始化失败: ${error.message}")
                updateState { copy(isLoading = false, navigateToLogin = true) }
                GlobalLoadingManager.hide()
            }
        ) {
            // 先尝试从缓存加载配置
            appNetInterfaceRepository.getAppConfig()
                .collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            // 缓存配置加载成功，在后台刷新
                            refreshConfigInBackground()
                            PuxxiCoroutine.delay(2000L)
                            updateState { copy(isLoading = false, navigateToLogin = true) }
                            GlobalLoadingManager.hide()
                        }
                        is Resource.Error -> {
                            // 缓存不存在，从服务器获取
                            fetchConfigFromServer()
                        }
                        is Resource.Loading -> {
                            PuxxiCoroutine.withMain { GlobalLoadingManager.show() }
                        }
                    }
                }
        }
    }

    /**
     * 从服务器获取配置
     * 使用PuxxiCoroutine.io进行网络请求
     */
    private fun fetchConfigFromServer() {
        PuxxiCoroutine.io(
            tag = "AppConfig_Fetch",
            onError = { error ->
                LogUtil.e("SplashViewModel", "从服务器获取配置失败: ${error.message}")
                updateState { copy(isLoading = false, navigateToLogin = true) }
                GlobalLoadingManager.hide()
            }
        ) {
            appNetInterfaceRepository.getAppConfig()
                .collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            PuxxiCoroutine.delay(2000L)
                            updateState { copy(isLoading = false, navigateToLogin = true) }
                            GlobalLoadingManager.hide()
                        }
                        is Resource.Error -> {
                            LogUtil.e("SplashViewModel", "从服务器获取应用配置失败: ${resource.message}")
                            updateState { copy(isLoading = false, navigateToLogin = true) }
                            GlobalLoadingManager.hide()
                        }
                        is Resource.Loading -> {
                            // 保持loading状态
                        }
                    }
                }
        }
    }

    /**
     * 在后台刷新配置
     * 使用PuxxiCoroutine.background进行后台任务
     */
    private fun refreshConfigInBackground() {
        PuxxiCoroutine.background(
            tag = "AppConfig_Refresh",
            onError = { error ->
            }
        ) {
            appNetInterfaceRepository.refreshAppConfig()
                .collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                        }
                        is Resource.Error -> {
                        }
                        is Resource.Loading -> {
                        }
                    }
                }
        }
    }
}