package com.stargate.pxo.presentation.ui.view

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import coil.ImageLoader
import coil.decode.GifDecoder
import coil.decode.ImageDecoderDecoder
import coil.decode.SvgDecoder
import coil.disk.DiskCache
import coil.memory.MemoryCache
import coil.request.CachePolicy
import coil.request.ImageRequest
import coil.util.DebugLogger
import okhttp3.OkHttpClient
import java.util.concurrent.TimeUnit

object ImageLoaderConfig {
    
    private var imageLoader: ImageLoader? = null
    
    fun initialize(context: Context, enableDebug: Boolean = false) {
        if (imageLoader == null) {
            imageLoader = createImageLoader(context, enableDebug)
        }
    }
    
    fun getImageLoader(context: Context): ImageLoader {
        return imageLoader ?: createImageLoader(context, false).also { imageLoader = it }
    }
    
    private fun createImageLoader(context: Context, enableDebug: Boolean): ImageLoader {
        val okHttpClient = OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .addInterceptor(ImageRetryInterceptor(maxRetries = 3, retryDelayMs = 1000L)) // 添加重试拦截器
            .apply {
                if (enableDebug) {
                    // 添加日志拦截器用于调试
                    addInterceptor { chain ->
                        val request = chain.request()
                        android.util.Log.d("ImageLoader", "Loading image: ${request.url}")
                        val response = chain.proceed(request)
                        android.util.Log.d("ImageLoader", "Response code: ${response.code} for ${request.url}")
                        response
                    }
                }
            }
            .build()
        
        return ImageLoader.Builder(context)
            .okHttpClient(okHttpClient)
            .memoryCache {
                MemoryCache.Builder(context)
                    .maxSizePercent(0.40)
                    .build()
            }
            .diskCache {
                DiskCache.Builder()
                    .directory(context.cacheDir.resolve("image_cache"))
                    .maxSizePercent(0.10)
                    .build()
            }
            .components {
                if (android.os.Build.VERSION.SDK_INT >= 28) {
                    add(ImageDecoderDecoder.Factory())
                } else {
                    add(GifDecoder.Factory())
                }
                add(SvgDecoder.Factory())
            }
            .respectCacheHeaders(false)
            .apply {
                if (enableDebug) {
                    logger(DebugLogger())
                }
            }
            .build()
    }
    
    @Composable
    fun createImageRequest(
        data: Any?,
        cachePolicy: CachePolicy = CachePolicy.ENABLED,
        crossfade: Boolean = true,
        placeholder: Int? = null,
        error: Int? = null
    ): ImageRequest {
        val context = LocalContext.current

        return ImageRequest.Builder(context)
            .data(data)
            .memoryCachePolicy(cachePolicy)
            .diskCachePolicy(cachePolicy)
            .networkCachePolicy(cachePolicy)
            .crossfade(crossfade)
            .apply {
                placeholder?.let { placeholder(it) }
                error?.let { error(it) }
            }
            .build()
    }
    
    fun clearMemoryCache() {
        imageLoader?.memoryCache?.clear()
    }
    
    fun clearDiskCache() {
        imageLoader?.diskCache?.clear()
    }
    
    fun clearAllCaches() {
        clearMemoryCache()
        clearDiskCache()
    }
}

enum class ImageCacheStrategy {
    ENABLED,
    DISABLED,
    MEMORY_ONLY,
    DISK_ONLY
}

fun ImageCacheStrategy.toCachePolicy(): CachePolicy {
    return when (this) {
        ImageCacheStrategy.ENABLED -> CachePolicy.ENABLED
        ImageCacheStrategy.DISABLED -> CachePolicy.DISABLED
        ImageCacheStrategy.MEMORY_ONLY -> CachePolicy.ENABLED
        ImageCacheStrategy.DISK_ONLY -> CachePolicy.ENABLED
    }
}