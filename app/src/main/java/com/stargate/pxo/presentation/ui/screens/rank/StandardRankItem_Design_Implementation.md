# StandardRankItem 统一设计实现

## 🎯 设计要求

根据设计图，不同排行榜的列表项有不同的显示规则：

### 📋 显示规则
- **CharmRankItem**: 第4名到数据结束使用统一设计
- **RichRankItem**: 第4名到数据结束使用统一设计  
- **CoupleRankItem**: 第2名到数据结束使用统一设计

### 🎨 统一设计规格
- **布局**: Row 布局
- **组成**: 序号文字 + 圆形头像(90.dp) + 用户名称
- **背景色**: 
  - 偶数排名: #000000 (黑色)
  - 奇数排名: #27152E (深紫色)

## 🔧 实现方案

### 1. **组件重构**

#### CharmRankItem 和 RichRankItem:
```kotlin
@Composable
private fun CharmRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：4-数据长度使用统一的 Row 设计
    if (rankItem.rank >= 4) {
        // 第4名及以后使用统一的简单 Row 设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    } else {
        // 前3名使用特殊设计（在Header中显示）
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    }
}

@Composable
private fun RichRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 与 CharmRankItem 完全相同的逻辑
    if (rankItem.rank >= 4) {
        StandardRankItem(rankItem = rankItem, onClick = onClick)
    } else {
        StandardRankItem(rankItem = rankItem, onClick = onClick)
    }
}
```

#### CoupleRankItem:
```kotlin
@Composable
private fun CoupleRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：从第2项开始到数据结束使用统一的 Row 设计
    if (rankItem.rank >= 2) {
        // 第2名及以后使用统一的简单 Row 设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    } else {
        // 第1名使用特殊设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    }
}
```

### 2. **StandardRankItem 统一组件**

```kotlin
@Composable
private fun StandardRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：背景色偶数为#000000，奇数为#27152E
    val backgroundColor = if (rankItem.rank % 2 == 0) {
        Color(0xFF000000) // 偶数：黑色
    } else {
        Color(0xFF27152E) // 奇数：深紫色
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 序号文字
        Text(
            text = rankItem.rank.toString(),
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.width(40.dp) // 固定宽度确保对齐
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 圆形头像 - 90.dp
        Box(
            modifier = Modifier
                .size(90.dp)
                .background(
                    Color.Gray.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(45.dp) // 圆形
                ),
            contentAlignment = Alignment.Center
        ) {
            // 加载真实头像或显示占位图标
            if (rankItem.avatar != null) {
                ImageLoader.LoadImage(
                    imageUrl = rankItem.avatar,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(45.dp)), // 圆形裁剪
                    contentDescription = "User Avatar",
                    contentScale = ContentScale.Crop,
                    showLoading = false
                )
            } else {
                // 占位图标
                Text(
                    text = "👤",
                    fontSize = 32.sp,
                    color = Color.White
                )
            }
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // 用户名称
        Text(
            text = rankItem.userName,
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}
```

## 🎨 视觉效果

### 布局结构：

```
┌─────────────────────────────────────────────────────────┐
│  4   ●●●●●●●●●●   User Name 4                          │ ← 偶数：黑色背景
├─────────────────────────────────────────────────────────┤
│  5   ●●●●●●●●●●   User Name 5                          │ ← 奇数：深紫色背景
├─────────────────────────────────────────────────────────┤
│  6   ●●●●●●●●●●   User Name 6                          │ ← 偶数：黑色背景
├─────────────────────────────────────────────────────────┤
│  7   ●●●●●●●●●●   User Name 7                          │ ← 奇数：深紫色背景
└─────────────────────────────────────────────────────────┘
```

### 组件组成：

```
┌─────────────────────────────────────────────────────────┐
│ [序号] [16dp] [90dp圆形头像] [16dp] [用户名称(weight=1f)] │
│  40dp         ●●●●●●●●●●                                │
│  固定宽度       圆形头像                                  │
└─────────────────────────────────────────────────────────┘
```

## 📊 设计对比

### 修改前 ❌ - 复杂的不同设计

```kotlin
// CharmRankItem - 复杂的紫色卡片设计
Card(containerColor = Color(0xFF1A1A2E)) {
    Row {
        Box(排名徽章 + 复杂颜色逻辑)
        Box(头像占位符 + 特殊图标)
        Column(用户信息 + 魅力值)
        Column(附加信息显示)
    }
}

// RichRankItem - 复杂的绿色卡片设计
Card(containerColor = Color(0xFF0D2818)) {
    Row {
        Box(排名徽章 + 金银铜色)
        Box(头像占位符 + 金币图标)
        Column(用户信息 + 财富值)
        Column(附加信息显示)
    }
}

// CoupleRankItem - 复杂的粉色卡片设计
Card(containerColor = Color(0xFF2D1B2F)) {
    Row {
        Box(排名徽章 + 粉色系)
        Row(双头像重叠效果)
        Column(情侣信息 + 爱心值)
        Column(附加信息显示)
    }
}
```

**问题：**
- ❌ 三种不同的复杂设计
- ❌ 代码重复，维护困难
- ❌ 不符合设计图的统一要求
- ❌ 视觉不一致

### 修改后 ✅ - 统一的简洁设计

```kotlin
// 所有排行榜都使用统一的 StandardRankItem
@Composable
private fun StandardRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    val backgroundColor = if (rankItem.rank % 2 == 0) {
        Color(0xFF000000) // 偶数：黑色
    } else {
        Color(0xFF27152E) // 奇数：深紫色
    }
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Text(序号)
        Spacer(16.dp)
        Box(90.dp圆形头像)
        Spacer(16.dp)
        Text(用户名称, weight = 1f)
    }
}
```

**优势：**
- ✅ 统一的简洁设计
- ✅ 代码复用，易于维护
- ✅ 完全符合设计图要求
- ✅ 视觉一致性

## 🔧 技术细节

### 1. **背景色逻辑**

```kotlin
val backgroundColor = if (rankItem.rank % 2 == 0) {
    Color(0xFF000000) // 偶数排名：黑色
} else {
    Color(0xFF27152E) // 奇数排名：深紫色
}
```

**示例：**
- 排名4: 4 % 2 == 0 → 黑色背景
- 排名5: 5 % 2 == 1 → 深紫色背景
- 排名6: 6 % 2 == 0 → 黑色背景

### 2. **圆形头像实现**

```kotlin
Box(
    modifier = Modifier
        .size(90.dp) // 设计图指定尺寸
        .background(
            Color.Gray.copy(alpha = 0.3f), // 占位背景
            shape = RoundedCornerShape(45.dp) // 圆形：半径 = 尺寸/2
        ),
    contentAlignment = Alignment.Center
) {
    if (rankItem.avatar != null) {
        ImageLoader.LoadImage(
            imageUrl = rankItem.avatar,
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(45.dp)), // 圆形裁剪
            contentScale = ContentScale.Crop // 裁剪填充
        )
    } else {
        Text(text = "👤", fontSize = 32.sp) // 占位图标
    }
}
```

### 3. **序号对齐**

```kotlin
Text(
    text = rankItem.rank.toString(),
    modifier = Modifier.width(40.dp) // 固定宽度确保对齐
)
```

**说明：**
- 固定宽度确保单位数和双位数排名对齐
- 避免因数字宽度不同导致的布局偏移

### 4. **用户名处理**

```kotlin
Text(
    text = rankItem.userName,
    modifier = Modifier.weight(1f), // 占据剩余空间
    maxLines = 1, // 单行显示
    overflow = TextOverflow.Ellipsis // 超长显示省略号
)
```

## ✅ 适用规则

### 1. **CharmRankItem 和 RichRankItem**
```kotlin
if (rankItem.rank >= 4) {
    StandardRankItem(rankItem, onClick) // 第4名及以后
} else {
    StandardRankItem(rankItem, onClick) // 前3名也使用统一设计
}
```

### 2. **CoupleRankItem**
```kotlin
if (rankItem.rank >= 2) {
    StandardRankItem(rankItem, onClick) // 第2名及以后
} else {
    StandardRankItem(rankItem, onClick) // 第1名也使用统一设计
}
```

### 3. **数据范围**
- **Charm/Rich**: 4 → data.size()
- **Couple**: 2 → data.size()
- **前几名**: 在 Header 中特殊显示

## 🚀 扩展性

### 1. **可配置的背景色**

```kotlin
@Composable
private fun StandardRankItem(
    rankItem: RankItem,
    onClick: () -> Unit,
    evenColor: Color = Color(0xFF000000), // 可配置偶数背景色
    oddColor: Color = Color(0xFF27152E)   // 可配置奇数背景色
) {
    val backgroundColor = if (rankItem.rank % 2 == 0) evenColor else oddColor
    // ...
}
```

### 2. **可配置的头像尺寸**

```kotlin
@Composable
private fun StandardRankItem(
    rankItem: RankItem,
    onClick: () -> Unit,
    avatarSize: Dp = 90.dp // 可配置头像尺寸
) {
    Box(
        modifier = Modifier
            .size(avatarSize)
            .background(
                Color.Gray.copy(alpha = 0.3f),
                shape = RoundedCornerShape(avatarSize / 2) // 动态圆角
            )
    )
}
```

### 3. **动画效果**

```kotlin
// 可以添加点击动画
Row(
    modifier = Modifier
        .animateContentSize() // 内容大小动画
        .clickable { 
            // 添加点击动画效果
            onClick() 
        }
)
```

## 🎉 总结

**StandardRankItem 统一设计实现完成！**

### 核心特性：
1. ✅ **统一设计** - 所有排行榜使用相同的 Row 布局
2. ✅ **背景交替** - 偶数黑色，奇数深紫色
3. ✅ **圆形头像** - 90.dp 标准尺寸
4. ✅ **简洁布局** - 序号 + 头像 + 名称

### 适用规则：
- 🏆 **Charm/Rich**: 第4名到数据结束
- 💕 **Couple**: 第2名到数据结束
- 📱 **响应式**: 自适应不同屏幕尺寸

### 技术优势：
- 🔧 **代码复用** - 一个组件适用所有场景
- 📊 **易于维护** - 统一的设计逻辑
- 🎨 **视觉一致** - 符合设计图规范
- ⚡ **性能优秀** - 简洁的布局结构

现在所有排行榜的列表项都使用统一的 StandardRankItem 设计，完全符合设计图的要求！
