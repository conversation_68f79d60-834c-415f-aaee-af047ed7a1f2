# RankScreen 数据类型迁移

## 🎯 迁移目标

将 RankScreen 中的数据类型从通用的 `RankItem` 迁移到具体的业务数据类型：

- **CharmRankItems**: `List<RankBroadcasterItem>`
- **RickRankItems**: `List<RankUserItem>`  
- **CoupleRankItems**: `List<RankCoupleItem>`

## 🔧 修改内容

### 1. **数据获取逻辑修改**

#### 修改前 ❌:
```kotlin
val rankItems = when (tabType) {
    RankTabType.CHARM -> uiState.charmRankItems
    RankTabType.RICK -> uiState.rickRankItems
    RankTabType.COUPLE -> uiState.coupleRankItems
}
```

#### 修改后 ✅:
```kotlin
val rankItems = when (tabType) {
    RankTabType.CHARM -> uiState.charmRankItems.mapIndexed { index, item ->
        RankItem(
            id = item.userId,
            rank = item.sort,
            userName = item.nickname,
            score = item.rankScore ?: 0L,
            additionalInfo = item.giftValue?.toString() ?: "0",
            avatar = item.avatar,
            tabType = tabType
        )
    }
    RankTabType.RICK -> uiState.rickRankItems.mapIndexed { index, item ->
        RankItem(
            id = item.userId,
            rank = item.sort,
            userName = item.nickname,
            score = 0L, // RankUserItem 没有 score 字段
            additionalInfo = "Level ${item.liveLevel}",
            avatar = item.avatar,
            tabType = tabType
        )
    }
    RankTabType.COUPLE -> uiState.coupleRankItems.mapIndexed { index, item ->
        RankItem(
            id = "${item.userModel.userId}_${item.broadcastetModel.userId}",
            rank = item.sort,
            userName = "${item.userModel.nickname} & ${item.broadcastetModel.nickname}",
            score = 0L, // RankCoupleItem 没有 score 字段
            additionalInfo = "Couple",
            avatar = item.userModel.avatar, // 使用用户头像
            tabType = tabType
        )
    }
}
```

### 2. **头部组件数据传递**

#### CharmRankHeader 修改:
```kotlin
// 修改前 ❌
@Composable
private fun CharmRankHeader() {
    // 使用硬编码的占位数据
    userName = "User1"
}

// 修改后 ✅
@Composable
private fun CharmRankHeader(
    charmRankItems: List<RankBroadcasterItem>
) {
    // 使用真实数据
    userName = charmRankItems.getOrNull(0)?.nickname ?: "No Data"
    userAvatar = charmRankItems.getOrNull(0)?.avatar
}
```

#### RichRankHeader 修改:
```kotlin
// 修改前 ❌
@Composable
private fun RichRankHeader() {
    // 使用硬编码的占位数据
    userName = "RichUser1"
}

// 修改后 ✅
@Composable
private fun RichRankHeader(
    richRankItems: List<RankUserItem>
) {
    // 使用真实数据
    userName = richRankItems.getOrNull(0)?.nickname ?: "No Data"
    userAvatar = richRankItems.getOrNull(0)?.avatar
}
```

### 3. **头部组件调用修改**

```kotlin
// 修改前 ❌
item {
    when (tabType) {
        RankTabType.CHARM -> CharmRankHeader()
        RankTabType.RICK -> RichRankHeader()
        RankTabType.COUPLE -> CoupleRankHeader()
    }
}

// 修改后 ✅
item {
    when (tabType) {
        RankTabType.CHARM -> CharmRankHeader(uiState.charmRankItems)
        RankTabType.RICK -> RichRankHeader(uiState.rickRankItems)
        RankTabType.COUPLE -> CoupleRankHeader()
    }
}
```

## 📊 数据结构映射

### 1. **RankBroadcasterItem → RankItem**

```kotlin
RankBroadcasterItem(
    sort: Int,                    → rank: Int
    userId: String,               → id: String
    nickname: String,             → userName: String
    avatar: String,               → avatar: String
    rankScore: Long?,             → score: Long
    giftValue: Long?,             → additionalInfo: String
    // 其他字段...
)
```

**映射逻辑:**
- `sort` → `rank`: 排名
- `userId` → `id`: 唯一标识
- `nickname` → `userName`: 用户名
- `avatar` → `avatar`: 头像URL
- `rankScore` → `score`: 排行分数
- `giftValue` → `additionalInfo`: 礼物价值作为附加信息

### 2. **RankUserItem → RankItem**

```kotlin
RankUserItem(
    sort: Int,                    → rank: Int
    userId: String,               → id: String
    nickname: String,             → userName: String
    avatar: String,               → avatar: String
    liveLevel: Int,               → additionalInfo: String ("Level X")
    // 没有 score 字段            → score: 0L (默认值)
)
```

**映射逻辑:**
- `sort` → `rank`: 排名
- `userId` → `id`: 唯一标识
- `nickname` → `userName`: 用户名
- `avatar` → `avatar`: 头像URL
- `liveLevel` → `additionalInfo`: 直播等级作为附加信息
- 默认 `score = 0L`: 因为 RankUserItem 没有分数字段

### 3. **RankCoupleItem → RankItem**

```kotlin
RankCoupleItem(
    sort: Int,                    → rank: Int
    userModel: CoupleUserModel,   → userName: String (组合名称)
    broadcastetModel: CoupleBroadcasterModel → id: String (组合ID)
)
```

**映射逻辑:**
- `sort` → `rank`: 排名
- `userModel.userId + broadcastetModel.userId` → `id`: 组合唯一标识
- `userModel.nickname + " & " + broadcastetModel.nickname` → `userName`: 组合名称
- `userModel.avatar` → `avatar`: 使用用户头像
- `"Couple"` → `additionalInfo`: 固定标识
- 默认 `score = 0L`: 因为 RankCoupleItem 没有分数字段

## 🔧 技术细节

### 1. **空数据处理**

```kotlin
// 安全获取数据，避免数组越界
userName = charmRankItems.getOrNull(0)?.nickname ?: "No Data"
userAvatar = charmRankItems.getOrNull(0)?.avatar

// 头像URL检查
if (rankItem.avatar?.isNotEmpty() == true) {
    // 加载真实头像
} else {
    // 显示占位图标
}
```

### 2. **数据转换**

```kotlin
// 将业务数据转换为UI数据
uiState.charmRankItems.mapIndexed { index, item ->
    RankItem(
        id = item.userId,
        rank = item.sort,
        userName = item.nickname,
        score = item.rankScore ?: 0L, // 空值处理
        additionalInfo = item.giftValue?.toString() ?: "0", // 空值处理
        avatar = item.avatar,
        tabType = tabType
    )
}
```

### 3. **类型安全**

```kotlin
// 导入具体的数据类型
import com.stargate.pxo.data.network.model.RankBroadcasterItem
import com.stargate.pxo.data.network.model.RankCoupleItem
import com.stargate.pxo.data.network.model.RankUserItem

// 使用具体类型的参数
@Composable
private fun CharmRankHeader(
    charmRankItems: List<RankBroadcasterItem> // 具体类型
)
```

## ✅ 迁移优势

### 1. **类型安全**
- ✅ 使用具体的业务数据类型
- ✅ 编译时类型检查
- ✅ 避免类型转换错误

### 2. **数据完整性**
- ✅ 直接访问所有业务字段
- ✅ 保留原始数据结构
- ✅ 支持扩展字段

### 3. **维护性**
- ✅ 数据流向清晰
- ✅ 业务逻辑分离
- ✅ 易于调试和测试

### 4. **扩展性**
- ✅ 支持不同排行榜的特殊字段
- ✅ 便于添加新的数据类型
- ✅ 灵活的数据映射逻辑

## 🚀 后续优化

### 1. **数据映射优化**

```kotlin
// 可以创建扩展函数简化映射
fun RankBroadcasterItem.toRankItem(tabType: RankTabType): RankItem {
    return RankItem(
        id = userId,
        rank = sort,
        userName = nickname,
        score = rankScore ?: 0L,
        additionalInfo = giftValue?.toString() ?: "0",
        avatar = avatar,
        tabType = tabType
    )
}

// 使用扩展函数
val rankItems = uiState.charmRankItems.map { it.toRankItem(tabType) }
```

### 2. **缓存优化**

```kotlin
// 可以缓存转换后的数据，避免重复转换
val cachedRankItems by remember(uiState.charmRankItems) {
    derivedStateOf {
        uiState.charmRankItems.map { it.toRankItem(tabType) }
    }
}
```

### 3. **错误处理**

```kotlin
// 添加更完善的错误处理
try {
    val rankItems = uiState.charmRankItems.mapIndexed { index, item ->
        item.toRankItem(tabType)
    }
} catch (e: Exception) {
    LogUtil.e("RankScreen", "数据转换失败", e)
    emptyList<RankItem>()
}
```

## 🎉 总结

**RankScreen 数据类型迁移完成！**

### 核心改进:
1. ✅ **具体数据类型** - 使用 RankBroadcasterItem、RankUserItem、RankCoupleItem
2. ✅ **数据映射逻辑** - 将业务数据转换为UI数据
3. ✅ **头部数据传递** - 传递真实数据到头部组件
4. ✅ **类型安全** - 编译时类型检查，避免运行时错误

### 技术特点:
- 🔧 **类型安全** - 使用具体的业务数据类型
- 📊 **数据完整** - 保留所有业务字段信息
- 🛡️ **错误处理** - 完善的空值和异常处理
- ⚡ **性能优秀** - 高效的数据转换逻辑

现在 RankScreen 已经完全适配新的数据类型，能够正确处理和显示三种不同排行榜的真实数据！
