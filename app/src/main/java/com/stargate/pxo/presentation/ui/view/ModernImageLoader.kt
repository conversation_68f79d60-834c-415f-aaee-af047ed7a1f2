package com.stargate.pxo.presentation.ui.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.material3.CircularProgressIndicator
import coil.compose.SubcomposeAsyncImage
import coil.compose.SubcomposeAsyncImageContent

@Composable
fun ModernImageLoader(
    imageUrl: Any?,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    contentScale: ContentScale = ContentScale.Crop,
    placeholder: Int? = null,
    error: Int? = null,
    shape: Shape = RoundedCornerShape(0.dp),
    cacheStrategy: ImageCacheStrategy = ImageCacheStrategy.ENABLED,
    showLoading: Boolean = true,
    fadeDuration: Int = 300,
    onSuccess: (() -> Unit)? = null,
    onError: (() -> Unit)? = null
) {
    when {
        imageUrl == null -> {
            error?.let { errorRes ->
                androidx.compose.foundation.Image(
                    painter = androidx.compose.ui.res.painterResource(id = errorRes),
                    contentDescription = contentDescription,
                    modifier = modifier.clip(shape),
                    contentScale = contentScale
                )
            }
        }
        ImageLoader.isLocalResource(imageUrl) -> {
            ImageLoader.LoadImage(
                imageUrl = imageUrl,
                modifier = modifier,
                contentDescription = contentDescription,
                contentScale = contentScale,
                placeholder = placeholder,
                error = error,
                shape = shape,
                showLoading = showLoading
            )
        }
        else -> {
            val imageRequest = ImageLoaderConfig.createImageRequest(
                data = imageUrl,
                cachePolicy = cacheStrategy.toCachePolicy(),
                crossfade = true,
                placeholder = placeholder,
                error = error
            )
            
            SubcomposeAsyncImage(
                model = imageRequest,
                contentDescription = contentDescription,
                modifier = modifier.clip(shape),
                contentScale = contentScale,
                loading = {
                    if (showLoading) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp)
                            )
                        }
                    }
                },
                success = { success ->
                    onSuccess?.invoke()
                    SubcomposeAsyncImageContent()
                },
                error = { error ->
                    onError?.invoke()
                }
            )
        }
    }
}

@Composable
fun CircularImageLoader(
    imageUrl: Any?,
    size: Dp = 100.dp,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    placeholder: Int? = null,
    error: Int? = null,
    showLoading: Boolean = true,
    cacheStrategy: ImageCacheStrategy = ImageCacheStrategy.ENABLED
) {
    ModernImageLoader(
        imageUrl = imageUrl,
        modifier = modifier.size(size),
        contentDescription = contentDescription,
        placeholder = placeholder,
        error = error,
        shape = RoundedCornerShape(50),
        cacheStrategy = cacheStrategy,
        showLoading = showLoading,
        contentScale = ContentScale.Crop
    )
}

@Composable
fun RoundedImageLoader(
    imageUrl: Any?,
    modifier: Modifier = Modifier,
    radius: Dp = 12.dp,
    contentDescription: String? = null,
    placeholder: Int? = null,
    error: Int? = null,
    showLoading: Boolean = true,
    cacheStrategy: ImageCacheStrategy = ImageCacheStrategy.ENABLED
) {
    ModernImageLoader(
        imageUrl = imageUrl,
        modifier = modifier,
        contentDescription = contentDescription,
        placeholder = placeholder,
        error = error,
        shape = RoundedCornerShape(radius),
        cacheStrategy = cacheStrategy,
        showLoading = showLoading,
        contentScale = ContentScale.Crop
    )
}