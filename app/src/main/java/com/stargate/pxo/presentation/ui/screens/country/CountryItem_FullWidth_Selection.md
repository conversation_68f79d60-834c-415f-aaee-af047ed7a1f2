# CountryItem 选中状态铺满宽度优化

## 🎯 优化目标

修改 CountryItem 组件，让选中状态时背景宽度铺满整个屏幕，移除左右的 32dp padding，同时保持内容的合理间距。

## 🔧 具体修改

### 修改前 ❌ - 选中状态有边距

```kotlin
Row(
    modifier = Modifier
        .fillMaxWidth()
        .clickable { onClick() }
        .background(if (isSelected) Color.Black else Color.Transparent)
        .padding(32.dp), // 所有状态都有32dp的padding
    verticalAlignment = Alignment.CenterVertically
) {
    // 国旗
    Box(modifier = Modifier.size(44.dp, 58.dp)) { ... }
    
    // 国家名称
    Text(modifier = Modifier.padding(start = 15.dp).weight(1f)) { ... }
    
    // 选中指示器
    if (isSelected) {
        Icon(modifier = Modifier.size(29.dp, 21.dp)) { ... }
    }
}
```

### 修改后 ✅ - 选中状态铺满宽度

```kotlin
Row(
    modifier = Modifier
        .fillMaxWidth()
        .clickable { onClick() }
        .background(if (isSelected) Color.Black else Color.Transparent)
        .padding(
            horizontal = if (isSelected) 0.dp else 32.dp, // ✅ 选中时铺满，未选中时保持边距
            vertical = 32.dp
        ),
    verticalAlignment = Alignment.CenterVertically
) {
    // 国旗 - 选中时添加左边距
    Box(
        modifier = Modifier
            .size(44.dp, 58.dp)
            .padding(start = if (isSelected) 32.dp else 0.dp) // ✅ 选中时内容左边距
    ) { ... }
    
    // 国家名称 - 保持原有间距
    Text(modifier = Modifier.padding(start = 15.dp).weight(1f)) { ... }
    
    // 选中指示器 - 选中时添加右边距
    if (isSelected) {
        Icon(
            modifier = Modifier
                .size(29.dp, 21.dp)
                .padding(end = 32.dp) // ✅ 选中时内容右边距
        ) { ... }
    }
}
```

## 📋 视觉效果对比

### 修改前 ❌ - 选中状态有边距

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│    ┌─────────────────────────────────────────────┐     │
│    │ 🇺🇸  United States                      ✓ │     │ ← 黑色背景有边距
│    └─────────────────────────────────────────────┘     │
│                                                         │
│    🇨🇳  China                                          │
│                                                         │
│    🇯🇵  Japan                                          │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 修改后 ✅ - 选中状态铺满宽度

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│ 🇺🇸  United States                                  ✓ │ ← 黑色背景铺满宽度
│                                                         │
│    🇨🇳  China                                          │
│                                                         │
│    🇯🇵  Japan                                          │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 🎨 实现细节

### 1. **动态 Padding 控制**

```kotlin
.padding(
    horizontal = if (isSelected) 0.dp else 32.dp, // 水平方向动态padding
    vertical = 32.dp // 垂直方向保持固定
)
```

**逻辑：**
- ✅ **选中状态** - 水平padding为0，背景铺满宽度
- ✅ **未选中状态** - 水平padding为32dp，保持原有间距
- ✅ **垂直padding** - 始终保持32dp，确保高度一致

### 2. **内容间距补偿**

#### 国旗左边距：
```kotlin
Box(
    modifier = Modifier
        .size(44.dp, 58.dp)
        .padding(start = if (isSelected) 32.dp else 0.dp) // 选中时补偿左边距
)
```

#### 选中指示器右边距：
```kotlin
Icon(
    modifier = Modifier
        .size(29.dp, 21.dp)
        .padding(end = 32.dp) // 选中时补偿右边距
)
```

### 3. **状态对比表**

| 状态 | Row Padding | 国旗左边距 | 指示器右边距 | 背景效果 |
|------|-------------|------------|--------------|----------|
| **未选中** | 32dp | 0dp | - | 透明背景，有边距 |
| **选中** | 0dp | 32dp | 32dp | 黑色背景，铺满宽度 |

## ✅ 用户体验提升

### 1. **视觉层次更清晰**
- ✅ **选中状态突出** - 黑色背景铺满宽度，更加醒目
- ✅ **未选中状态简洁** - 保持原有的简洁布局
- ✅ **对比度增强** - 选中与未选中状态差异更明显

### 2. **符合设计规范**
- ✅ **Material Design** - 选中状态的背景应该有足够的视觉权重
- ✅ **移动端习惯** - 用户习惯选中项有明显的背景区分
- ✅ **一致性** - 与其他列表选择组件保持一致

### 3. **交互反馈优化**
- ✅ **点击区域明确** - 铺满宽度的背景让点击区域更明确
- ✅ **状态反馈及时** - 选中状态变化更加明显
- ✅ **视觉引导** - 用户能快速识别当前选中项

## 🔧 技术实现优势

### 1. **性能优化**
- ✅ **条件渲染** - 只在需要时添加padding和背景
- ✅ **布局稳定** - 垂直高度保持一致，避免布局跳动
- ✅ **重组最小化** - 只有选中状态变化时才重新计算布局

### 2. **代码简洁**
- ✅ **逻辑清晰** - 使用简单的条件判断控制样式
- ✅ **易于维护** - 样式逻辑集中，便于后续调整
- ✅ **可扩展性** - 可以轻松添加更多的选中状态样式

### 3. **响应式设计**
- ✅ **自适应宽度** - 在不同屏幕尺寸下都能正确显示
- ✅ **内容保护** - 确保内容不会贴边显示
- ✅ **间距一致** - 保持与其他UI元素的间距协调

## 🎯 使用场景

### 1. **国家选择列表**
```
🌍 All                                               ✓
🇺🇸 United States
🇨🇳 China
🇯🇵 Japan
```
- 选中的"All"项背景铺满宽度，其他项保持边距

### 2. **设置选项列表**
```
通知设置                                              ✓
隐私设置
    账户设置
    语言设置
```
- 选中项背景铺满，突出当前选择

### 3. **筛选条件列表**
```
全部分类                                              ✓
    美女主播
    才艺表演
    游戏直播
```
- 当前筛选条件背景铺满，视觉层次清晰

## 🚀 总结

**CountryItem 选中状态铺满宽度优化完成！**

### 优化成果：
1. ✅ **选中状态铺满宽度** - 黑色背景从边到边，视觉效果更突出
2. ✅ **未选中状态保持原样** - 维持32dp边距，保持简洁
3. ✅ **内容间距合理** - 通过动态padding确保内容不贴边
4. ✅ **交互体验提升** - 选中状态更加明显，用户体验更好

### 技术特点：
- 🎯 **动态样式** - 根据选中状态动态调整padding和背景
- 📱 **响应式设计** - 适配不同屏幕尺寸
- ⚡ **性能优化** - 最小化重组，保持布局稳定
- 🔧 **易于维护** - 逻辑清晰，代码简洁

### 视觉效果：
- **选中项** - 黑色背景铺满宽度，内容有合理间距
- **未选中项** - 透明背景，保持32dp边距
- **整体协调** - 选中与未选中状态对比明显，层次清晰

现在 CountryItem 的选中状态会铺满整个宽度，提供更好的视觉反馈和用户体验！
