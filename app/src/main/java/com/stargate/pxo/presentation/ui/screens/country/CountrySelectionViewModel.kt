package com.stargate.pxo.presentation.ui.screens.country

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.StrategyConfigManager
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.model.CountryRegion
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 国家选择页面的 UiState
 */
data class CountrySelectionUiState(
    val countryRegions: List<CountryRegion> = emptyList(),
    val selectedRegion: String = "",
    val isLoading: Boolean = false,
    val error: String? = null
) : UiState

/**
 * 导航事件
 */
sealed class CountryNavigationEvent {
    data class NavigateBackWithResult(val country: CountryRegion) : CountryNavigationEvent()
    object NavigateBack : CountryNavigationEvent()
}

/**
 * 国家选择页面 ViewModel
 */
@HiltViewModel
class CountrySelectionViewModel @Inject constructor(
    private val strategyConfigManager: StrategyConfigManager
) : BaseViewModel<CountrySelectionUiState>() {

    override fun createInitialState(): CountrySelectionUiState = CountrySelectionUiState()

    // 导航事件
    private val _navigationEvent = MutableSharedFlow<CountryNavigationEvent>()
    val navigationEvent = _navigationEvent.asSharedFlow()

    init {
        loadCountryRegions()
    }

    /**
     * 设置初始选中的国家
     */
    fun setInitialSelectedCountry(countryCode: String?) {
        updateState {
            copy(selectedRegion = countryCode ?: "") // 如果为null，设置为空字符串（All选项）
        }
    }

    /**
     * 加载国家地区列表
     */
    private fun loadCountryRegions() {
        viewModelScope.launch {
            try {
                updateState { copy(isLoading = true, error = null) }

                LogUtil.d("CountrySelectionViewModel", "开始加载国家地区列表")

                // 从策略配置中获取国家地区列表
                val strategyConfig = strategyConfigManager.getStrategyConfig()

                val baseCountryRegions = if (strategyConfig != null && strategyConfig.broadcasterWallRegions.isNotEmpty()) {
                    LogUtil.d("CountrySelectionViewModel", "使用缓存的策略配置数据加载国家地区")
                    strategyConfig.broadcasterWallRegions.map { region ->
                        CountryRegion(
                            code = region,
                            name = getCountryDisplayName(region),
                            flag = getCountryFlag(region)
                        )
                    }
                } else {
                    LogUtil.w("CountrySelectionViewModel", "策略配置中没有国家地区数据，使用默认数据")
                    getDefaultCountryRegions()
                }

                // 添加"All"选项作为第一项
                val countryRegions = listOf(
                    CountryRegion(
                        code = "", // 空字符串表示"All"
                        name = "All",
                        flag = "🌍" // 地球图标表示全部
                    )
                ) + baseCountryRegions

                LogUtil.d("CountrySelectionViewModel", "转换后的国家列表: $countryRegions")

                updateState {
                    copy(
                        countryRegions = countryRegions,
                        isLoading = false
                    )
                }
            } catch (e: Exception) {
                LogUtil.e("CountrySelectionViewModel", "加载国家地区列表失败", e)
                updateState {
                    copy(
                        isLoading = false,
                        error = "Failed to load countries: ${e.message}"
                    )
                }
            }
        }
    }

    /**
     * 选择国家地区
     */
    fun selectCountryRegion(country: CountryRegion) {
        updateState { copy(selectedRegion = country.code) }

        // 发送导航事件，带上选择的国家
        // 如果选中的是"All"（code为空），则传递null给HomeScreen
        val resultCountry = if (country.code.isEmpty()) {
            CountryRegion(code = "", name = "All", flag = "🌍") // 保持UI显示，但实际传递空code
        } else {
            country
        }

        viewModelScope.launch {
            _navigationEvent.emit(CountryNavigationEvent.NavigateBackWithResult(resultCountry))
        }
    }

    /**
     * 重新加载数据
     */
    fun retry() {
        loadCountryRegions()
    }

    /**
     * 获取默认国家地区数据
     */
    private fun getDefaultCountryRegions(): List<CountryRegion> {
        return listOf(
            CountryRegion("", "All Countries", "🌍"),
            CountryRegion("CN", "China", "🇨🇳"),
            CountryRegion("US", "USA", "🇺🇸"),
            CountryRegion("DE", "Germany", "🇩🇪"),
            CountryRegion("FR", "France", "🇫🇷"),
            CountryRegion("GB", "United Kingdom (UK)", "🇬🇧"),
            CountryRegion("BR", "Brazil", "🇧🇷"),
            CountryRegion("CA", "Canada", "🇨🇦"),
            CountryRegion("AU", "Australia", "🇦🇺"),
            CountryRegion("IN", "India", "🇮🇳"),
            CountryRegion("JP", "Japan", "🇯🇵"),
            CountryRegion("KR", "South Korea", "🇰🇷"),
            CountryRegion("IT", "Italy", "🇮🇹"),
            CountryRegion("ES", "Spain", "🇪🇸"),
            CountryRegion("RU", "Russia", "🇷🇺"),
            CountryRegion("TH", "Thailand", "🇹🇭"),
            CountryRegion("SG", "Singapore", "🇸🇬"),
            CountryRegion("MY", "Malaysia", "🇲🇾"),
            CountryRegion("ID", "Indonesia", "🇮🇩"),
            CountryRegion("PH", "Philippines", "🇵🇭"),
            CountryRegion("MX", "Mexico", "🇲🇽")
        )
    }

    /**
     * 获取国家显示名称
     */
    private fun getCountryDisplayName(countryCode: String): String {
        return when (countryCode.uppercase()) {
            "" -> "All Countries"
            "CN" -> "China"
            "US" -> "USA"
            "DE" -> "Germany"
            "FR" -> "France"
            "GB" -> "United Kingdom (UK)"
            "BR" -> "Brazil"
            "CA" -> "Canada"
            "AU" -> "Australia"
            "IN" -> "India"
            "JP" -> "Japan"
            "KR" -> "South Korea"
            "IT" -> "Italy"
            "ES" -> "Spain"
            "RU" -> "Russia"
            "TH" -> "Thailand"
            "SG" -> "Singapore"
            "MY" -> "Malaysia"
            "ID" -> "Indonesia"
            "PH" -> "Philippines"
            "MX" -> "Mexico"
            else -> countryCode.uppercase()
        }
    }

    /**
     * 获取国家旗帜emoji
     */
    private fun getCountryFlag(countryCode: String): String {
        return when (countryCode.uppercase()) {
            "" -> "🌍"      // All - 地球图标
            "CO" -> "🇨🇴"   // Colombia - 哥伦比亚
            "MA" -> "🇲🇦"   // Morocco - 摩洛哥
            "BR" -> "🇧🇷"   // Brazil - 巴西
            "VE" -> "🇻🇪"   // Venezuela - 委内瑞拉
            "VN" -> "🇻🇳"   // Vietnam - 越南
            "UA" -> "🇺🇦"   // Ukraine - 乌克兰
            "PH" -> "🇵🇭"   // Philippines - 菲律宾
            "IN" -> "🇮🇳"   // India - 印度
            "TH" -> "🇹🇭"   // Thailand - 泰国
            "PE" -> "🇵🇪"   // Peru - 秘鲁
            "EC" -> "🇪🇨"   // Ecuador - 厄瓜多尔
            "TR" -> "🇹🇷"   // Turkey - 土耳其
            "AR" -> "🇦🇷"   // Argentina - 阿根廷
            "AZ" -> "🇦🇿"   // Azerbaijan - 阿塞拜疆
            "US" -> "🇺🇸"   // United States - 美国
            "RU" -> "🇷🇺"   // Russia - 俄罗斯
            "ID" -> "🇮🇩"   // Indonesia - 印度尼西亚
            else -> "🏳️"    // 默认旗帜
        }
    }

    /**
     * 搜索国家（可选功能）
     */
    fun searchCountries(query: String) {
        if (query.isBlank()) {
            loadCountryRegions()
            return
        }

        viewModelScope.launch {
            try {
                val strategyConfig = strategyConfigManager.getStrategyConfig()

                val allCountries = if (strategyConfig != null && strategyConfig.broadcasterWallRegions.isNotEmpty()) {
                    strategyConfig.broadcasterWallRegions.map { region ->
                        CountryRegion(
                            code = region,
                            name = getCountryDisplayName(region),
                            flag = getCountryFlag(region)
                        )
                    }
                } else {
                    getDefaultCountryRegions()
                }

                val filteredCountries = allCountries.filter { country ->
                    country.name.contains(query, ignoreCase = true) ||
                    country.code.contains(query, ignoreCase = true)
                }

                updateState { copy(countryRegions = filteredCountries) }
            } catch (e: Exception) {
                LogUtil.e("CountrySelectionViewModel", "搜索国家失败", e)
            }
        }
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        updateState { copy(error = null) }
    }
}
