# RankScreen TopBar 和 TabRow 整合

## 🎯 设计目标

根据设计稿要求，将 RankScreen 的 TopBar 和 TabRow 整合到同一行，并且样式状态与 HomeScreen 的 TopFilterTabsWithIcons 保持一致。

## 🔧 实现方案

### 修改前 ❌ - 分离的布局

```kotlin
Column {
    // Top Bar with Back Button
    RankTopBar(onBackClick = { ... })
    
    // Tab Row (单独一行)
    RankTabRow(
        selectedTabIndex = pagerState.currentPage,
        onTabSelected = { ... }
    )
    
    // Daily/Weekly Toggle
    DailyWeeklyToggle(...)
}
```

**问题：**
- ❌ TopBar 和 TabRow 分离在两行
- ❌ 样式与 HomeScreen 不一致
- ❌ 空间利用不够高效

### 修改后 ✅ - 整合的布局

```kotlin
Column {
    // Top Bar with Back Button and Tab Row (合并在同一行)
    RankTopBarWithTabs(
        selectedTabIndex = pagerState.currentPage,
        onBackClick = { ... },
        onTabSelected = { ... }
    )
    
    // Daily/Weekly Toggle
    DailyWeeklyToggle(...)
}
```

**优势：**
- ✅ TopBar 和 TabRow 在同一行
- ✅ 样式与 HomeScreen 保持一致
- ✅ 更好的空间利用

## 📋 具体实现

### 1. **RankTopBarWithTabs 组件**

```kotlin
@Composable
private fun RankTopBarWithTabs(
    selectedTabIndex: Int,
    onBackClick: () -> Unit,
    onTabSelected: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .padding(top = 45.dp) // 与 HomeScreen 一致
            .fillMaxWidth()
            .height(85.dp) // 与 HomeScreen 一致
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮 (左侧)
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            // 标签栏 (中间) - 参考 HomeScreen 的 TopFilterTabsWithIcons
            LazyRow(
                modifier = Modifier.padding(top = 25.dp).weight(1f).fillMaxHeight(),
            ) {
                itemsIndexed(listOf("Charm", "Rich", "Couple")) { index, title ->
                    RankTabItem(
                        title = title,
                        isSelected = index == selectedTabIndex,
                        onClick = { onTabSelected(index) }
                    )
                }
            }
            
            // 右侧占位，保持布局平衡
            Spacer(modifier = Modifier.width(48.dp))
        }
    }
}
```

### 2. **RankTabItem 组件 - 参考 HomeScreen 的 TagTab**

```kotlin
@Composable
private fun RankTabItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    // 动画化的颜色和字体大小 - 与 HomeScreen TagTab 一致
    val animatedTextColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFFE5C98B) else Color.White.copy(alpha = 0.6f),
        animationSpec = tween(durationMillis = 300),
        label = "text_color"
    )
    
    val animatedFontSize by animateFloatAsState(
        targetValue = if (isSelected) 42f else 32f,
        animationSpec = tween(durationMillis = 300),
        label = "font_size"
    )

    Box(
        modifier = Modifier
            .noRippleClickable(onClick = onClick)
            .padding(horizontal = 19.dp) // 与 HomeScreen 一致
            .height(60.dp), // 固定高度确保对齐
        contentAlignment = if (isSelected) Alignment.BottomCenter else Alignment.Center
    ) {
        Column (
            verticalArrangement = if (isSelected) Arrangement.Bottom else Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                color = animatedTextColor, // 动画颜色
                fontSize = animatedFontSize.sp, // 动画字体大小
                fontWeight = FontWeight.SemiBold
            )

            // 选中指示器动画 - 与 HomeScreen 一致
            AnimatedVisibility(
                visible = isSelected,
                enter = fadeIn(tween(300)) + slideInHorizontally(
                    initialOffsetX = { it / 2 },
                    animationSpec = tween(300)
                ),
                exit = fadeOut(tween(200)) + slideOutHorizontally(
                    targetOffsetX = { it / 2 },
                    animationSpec = tween(200)
                )
            ) {
                // 使用简单的下划线代替图片
                Box(
                    modifier = Modifier
                        .width(60.dp)
                        .height(4.dp)
                        .background(
                            Color(0xFFE5C98B), // 与 HomeScreen 一致的金色
                            RoundedCornerShape(2.dp)
                        )
                        .offset(y = (-5).dp)
                )
            }
        }
    }
}
```

## ✅ 与 HomeScreen 的一致性

### 1. **布局结构一致**

#### HomeScreen TopFilterTabsWithIcons:
```kotlin
Column(
    modifier = Modifier
        .padding(top = 45.dp)
        .fillMaxWidth()
        .height(85.dp)
) {
    Row {
        // 标签栏 (LazyRow)
        LazyRow { ... }
        
        // 右侧图标
        RightIcons()
    }
}
```

#### RankScreen RankTopBarWithTabs:
```kotlin
Column(
    modifier = Modifier
        .padding(top = 45.dp) // ✅ 一致
        .fillMaxWidth()
        .height(85.dp) // ✅ 一致
) {
    Row {
        // 返回按钮
        BackButton()
        
        // 标签栏 (LazyRow) ✅ 一致
        LazyRow { ... }
        
        // 右侧占位
        Spacer()
    }
}
```

### 2. **标签样式一致**

#### HomeScreen TagTab:
- ✅ 动画颜色：选中 `Color(0xFFE5C98B)`，未选中 `Color.White.copy(alpha = 0.6f)`
- ✅ 动画字体：选中 `42f`，未选中 `32f`
- ✅ 动画时长：`300ms`
- ✅ 选中指示器：淡入淡出 + 滑动动画

#### RankScreen RankTabItem:
- ✅ 动画颜色：选中 `Color(0xFFE5C98B)`，未选中 `Color.White.copy(alpha = 0.6f)`
- ✅ 动画字体：选中 `42f`，未选中 `32f`
- ✅ 动画时长：`300ms`
- ✅ 选中指示器：淡入淡出 + 滑动动画

### 3. **背景色一致**

```kotlin
// HomeScreen
.background(Color(0xFF27152E))

// RankScreen (修改后)
.background(Color(0xFF27152E)) // ✅ 一致
```

## 🎨 视觉效果

### 布局对比：

#### 修改前 ❌:
```
┌─────────────────────────────────────┐
│ [←] Ranking                         │ ← TopBar (单独一行)
├─────────────────────────────────────┤
│    [Charm] [Rich] [Couple]          │ ← TabRow (单独一行)
├─────────────────────────────────────┤
│        [Daily] [Weekly]             │
└─────────────────────────────────────┘
```

#### 修改后 ✅:
```
┌─────────────────────────────────────┐
│ [←]  [Charm] [Rich] [Couple]    [ ] │ ← TopBar + TabRow (同一行)
├─────────────────────────────────────┤
│        [Daily] [Weekly]             │
└─────────────────────────────────────┘
```

### 动画效果：

- ✅ **颜色过渡** - 选中/未选中状态的颜色平滑变化
- ✅ **字体缩放** - 选中时字体放大，未选中时缩小
- ✅ **指示器动画** - 选中指示器的淡入淡出和滑动效果
- ✅ **交互反馈** - 点击时的即时视觉反馈

## 🔧 技术细节

### 1. **必要的导入**

```kotlin
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
```

### 2. **状态管理**

```kotlin
// Pager 状态与 Tab 状态同步
LaunchedEffect(pagerState.currentPage) {
    val tabType = when (pagerState.currentPage) {
        0 -> RankTabType.CHARM
        1 -> RankTabType.RICK
        2 -> RankTabType.COUPLE
        else -> RankTabType.CHARM
    }
    viewModel.selectTab(tabType)
}
```

### 3. **布局权重**

```kotlin
Row {
    IconButton(...) // 固定宽度 48.dp
    LazyRow(modifier = Modifier.weight(1f)) // 占据剩余空间
    Spacer(modifier = Modifier.width(48.dp)) // 平衡布局
}
```

## 🚀 总结

**RankScreen TopBar 和 TabRow 整合完成！**

### 核心改进：
1. ✅ **布局整合** - TopBar 和 TabRow 合并到同一行
2. ✅ **样式一致** - 与 HomeScreen 的 TopFilterTabsWithIcons 保持一致
3. ✅ **动画效果** - 相同的颜色、字体、指示器动画
4. ✅ **空间优化** - 更好的空间利用和视觉平衡

### 用户体验：
- 🎨 **视觉统一** - 与 HomeScreen 保持一致的设计语言
- 📱 **操作便捷** - 返回按钮和标签切换在同一行，操作更便捷
- ✨ **动画流畅** - 平滑的标签切换动画效果
- 🔄 **交互一致** - 与应用其他页面保持一致的交互模式

现在 RankScreen 的 TopBar 和 TabRow 已经整合到同一行，并且样式与 HomeScreen 完全一致！
