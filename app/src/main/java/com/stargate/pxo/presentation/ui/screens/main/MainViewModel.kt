package com.stargate.pxo.presentation.ui.screens.main

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.data.manager.UserManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主页面状态
 */
data class MainScreenState(
    val currentTab: String = "home",
    val isLoading: Boolean = false,
    val error: String? = null
) : UiState

/**
 * 主页面ViewModel
 */
@HiltViewModel
class MainViewModel @Inject constructor() : BaseViewModel<MainScreenState>() {
    
    override fun createInitialState(): MainScreenState = MainScreenState()
    
    init {
        // 监听用户信息变化
        viewModelScope.launch {
            UserManager.currentUser.collectLatest { userInfo ->
                // 用户信息变化时可以在这里处理
            }
        }
    }
    
    /**
     * 切换底部导航标签
     */
    fun switchTab(tab: String) {
        updateState { copy(currentTab = tab) }
    }
} 