package com.stargate.pxo.presentation.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.Density
import androidx.core.view.WindowCompat

// 创建自定义的 CompositionLocal 用于调试
val LocalPuxxiDensity = staticCompositionLocalOf<Density> {
    error("LocalPuxxiDensity not provided")
}


// 统一使用深色主题
private val AppColorScheme = darkColorScheme(
    primary = Primary,
    secondary = Secondary,
    tertiary = Tertiary,
    background = Background,
    surface = Surface,
    error = Error,
    onPrimary = OnPrimary,
    onSecondary = OnSecondary,
    onTertiary = OnTertiary,
    onBackground = OnBackground,
    onSurface = OnSurface,
    onError = OnError
)

@Composable
fun PuxxiTheme(
    content: @Composable () -> Unit
) {
    val colorScheme = AppColorScheme
    
    // 应用沉浸式状态栏和导航栏
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = Color.Transparent.toArgb()
            window.navigationBarColor = Color.Transparent.toArgb()
            
            // 设置状态栏和导航栏为沉浸式
            WindowCompat.getInsetsController(window, view).apply {
                isAppearanceLightStatusBars = false // 状态栏图标为白色
                isAppearanceLightNavigationBars = false // 导航栏图标为白色
            }
            
            // 确保内容延伸到系统栏后面
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    val fontScale = LocalDensity.current.fontScale
    val displayMetrics = LocalContext.current.resources.displayMetrics
    val widthPixels = displayMetrics.widthPixels

    // 计算自定义密度，基于设计稿宽度 750px
    val customDensity = Density(
        density = widthPixels / 750f,
        fontScale = fontScale
    )

    // CompositionLocalProvider 应该在 MaterialTheme 外部
    // 同时提供系统 LocalDensity 和自定义 LocalPuxxiDensity
    CompositionLocalProvider(
        LocalDensity provides customDensity,
        LocalPuxxiDensity provides customDensity
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content
        )
    }
}