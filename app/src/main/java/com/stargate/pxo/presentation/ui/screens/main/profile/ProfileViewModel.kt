package com.stargate.pxo.presentation.ui.screens.main.profile

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 个人资料页面UI状态
 */
data class ProfileUiState(
    val userName: String = "",
    val userAvatar: String = "",
    val userLevel: String = "LV1",
    val diamondCount: String = "39000",
    val isVipUser: Boolean = false,
    val vipPrice: String = "$9.99 / Month",
    val isLoading: Boolean = false,
    val error: String? = null
) : UiState

/**
 * 个人资料页面菜单项
 */
data class ProfileMenuItem(
    val id: String,
    val title: String,
    val icon: Int,
    val hasArrow: Boolean = true
)

/**
 * 个人资料页面ViewModel
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val userManager: UserManager
) : BaseViewModel<ProfileUiState>() {

    companion object {
        private const val TAG = "ProfileViewModel"
    }

    // 初始化默认状态
    override fun createInitialState(): ProfileUiState = ProfileUiState()

    // 菜单项列表
    val menuItems = listOf(
        ProfileMenuItem(
            id = "customer_service",
            title = "Customer Service",
            icon = com.stargate.pxo.R.drawable.ic_call
        ),
        ProfileMenuItem(
            id = "blocked_list",
            title = "Blocked List",
            icon = com.stargate.pxo.R.drawable.close_icon
        ),
        ProfileMenuItem(
            id = "about",
            title = "About",
            icon = com.stargate.pxo.R.drawable.ic_error
        ),
        ProfileMenuItem(
            id = "setting",
            title = "Setting",
            icon = com.stargate.pxo.R.drawable.profile_right_icon
        )
    )

    init {
        observeUserChanges()
    }

    /**
     * 监听用户信息变化
     */
    private fun observeUserChanges() {
        viewModelScope.launch {
            userManager.currentUser.collectLatest { userInfo ->
                try {
                    updateState {
                        copy(
                            userName = userInfo?.nickname ?: "Charlotte",
                            userAvatar = userInfo?.avatar ?: "",
                            userLevel = "LV1", // 可以从用户数据中获取
                            diamondCount = "39000", // 可以从用户数据中获取
                            isVipUser = userInfo?.isVip ?: false,
                            isLoading = false,
                            error = null
                        )
                    }

                    LogUtil.d(TAG, "用户资料更新成功: ${userInfo?.nickname}")
                } catch (e: Exception) {
                    LogUtil.e(TAG, "更新用户资料失败", e)
                    updateState {
                        copy(
                            isLoading = false,
                            error = "更新用户资料失败: ${e.message}"
                        )
                    }
                }
            }
        }
    }

    /**
     * 处理菜单项点击
     */
    fun onMenuItemClick(menuItem: ProfileMenuItem) {
        LogUtil.d(TAG, "菜单项点击: ${menuItem.title}")
        
        when (menuItem.id) {
            "customer_service" -> {
                // 处理客服点击
                LogUtil.d(TAG, "打开客服")
            }
            "blocked_list" -> {
                // 处理黑名单点击
                LogUtil.d(TAG, "打开黑名单")
            }
            "about" -> {
                // 处理关于点击
                LogUtil.d(TAG, "打开关于页面")
            }
            "setting" -> {
                // 处理设置点击
                LogUtil.d(TAG, "打开设置页面")
            }
        }
    }

    /**
     * 处理VIP点击
     */
    fun onVipClick() {
        LogUtil.d(TAG, "VIP点击")
        // 处理VIP购买逻辑
    }

    /**
     * 处理活动横幅点击
     */
    fun onPromotionBannerClick() {
        LogUtil.d(TAG, "活动横幅点击")
        // 处理活动横幅点击逻辑
    }

    /**
     * 刷新用户资料
     */
    fun refreshProfile() {
    }
} 