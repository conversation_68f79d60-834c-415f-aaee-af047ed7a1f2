package com.stargate.pxo.presentation.ui.screens.webview

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.log.LogUtil
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * WebView 页面状态
 */
data class WebViewState(
    val isLoading: Boolean = false,
    val isError: Boolean = false,
    val errorMessage: String? = null,
    val currentUrl: String? = null,
    val title: String? = null,
    val canGoBack: Boolean = false
) : UiState

/**
 * WebView 页面 ViewModel
 */
@HiltViewModel
class WebViewViewModel @Inject constructor() : BaseViewModel<WebViewState>() {
    
    override fun createInitialState(): WebViewState = WebViewState()
    
    /**
     * 更新页面加载状态
     */
    fun updateLoadingState(isLoading: Boolean) {
        updateState { copy(isLoading = isLoading) }
    }
    
    /**
     * 更新错误状态
     */
    fun updateErrorState(isError: Boolean, errorMessage: String? = null) {
        updateState { 
            copy(
                isError = isError,
                errorMessage = errorMessage
            ) 
        }
        
        if (isError) {
            LogUtil.e("WebViewViewModel", "Error loading page: $errorMessage")
        }
    }
    
    /**
     * 更新当前URL
     */
    fun updateCurrentUrl(url: String?) {
        updateState { copy(currentUrl = url) }
    }
    
    /**
     * 更新页面标题
     */
    fun updateTitle(title: String?) {
        updateState { copy(title = title) }
    }
    
    /**
     * 更新是否可以返回
     */
    fun updateCanGoBack(canGoBack: Boolean) {
        updateState { copy(canGoBack = canGoBack) }
    }
    
    /**
     * 重新加载页面
     */
    fun reload() {
        viewModelScope.launch {
            updateState { 
                copy(
                    isLoading = true,
                    isError = false,
                    errorMessage = null
                ) 
            }
        }
    }
} 