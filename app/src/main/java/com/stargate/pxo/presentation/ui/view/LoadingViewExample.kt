package com.stargate.pxo.presentation.ui.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stargate.pxo.common.util.coroutine.CoroutineManager
import com.stargate.pxo.presentation.ui.theme.PuxxiTheme
import kotlinx.coroutines.delay

/**
 * Loading View 使用示例
 */
@Composable
fun LoadingViewExample() {
    var showCustomLoading by remember { mutableStateOf(false) }
    val coroutineManager = CoroutineManager.getInstance()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Loading View 示例",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // 全局 Loading 控制按钮
        Button(
            onClick = {
                GlobalLoadingManager.show()
                coroutineManager.launchDefault(
                    scopeName = "LoadingExample",
                    jobName = "SimulateNetworkRequest"
                ) {
                    delay(3000) // 模拟网络请求
                    coroutineManager.runOnMainThread {
                        GlobalLoadingManager.hide()
                    }
                }
            }
        ) {
            Text("显示全局 Loading (3秒)")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 全局 Loading 自动隐藏按钮
        Button(
            onClick = {
                GlobalLoadingManager.showWithAutoHide(15000) // 15秒后自动隐藏
            }
        ) {
            Text("显示全局 Loading (15秒自动隐藏)")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 自定义 Loading 控制按钮
        Button(
            onClick = {
                showCustomLoading = true
                coroutineManager.launchDefault(
                    scopeName = "LoadingExample",
                    jobName = "SimulateCustomOperation"
                ) {
                    delay(2000) // 模拟操作
                    coroutineManager.runOnMainThread {
                        showCustomLoading = false
                    }
                }
            }
        ) {
            Text("显示自定义 Loading (2秒)")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 切换全局 Loading 状态
        Button(
            onClick = {
                GlobalLoadingManager.toggle()
            }
        ) {
            Text("切换全局 Loading 状态")
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        Text(
            text = "当前全局 Loading 状态: ${if (GlobalLoadingManager.isVisible()) "显示中" else "隐藏"}",
            style = MaterialTheme.typography.bodyMedium,
            color = if (GlobalLoadingManager.isVisible()) Color.Green else Color.Red
        )
    }
    
    // 全局 Loading View（必须在根 Composable 中调用）
    GlobalLoadingView()
    
    // 自定义 Loading View
    CustomLoadingView(
        isVisible = showCustomLoading,
        backgroundColor = Color.Black.copy(alpha = 0.5f),
        progressColor = Color.Blue,
        autoHideDelayMs = 15000, // 15秒自动隐藏
        onDismiss = { showCustomLoading = false }
    )
}

/**
 * 在实际应用中的集成示例
 */
@Composable
fun AppWithGlobalLoading(content: @Composable () -> Unit) {
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = MaterialTheme.colorScheme.background
    ) {
        // 应用主内容
        content()
        
        // 全局 Loading View（必须在最顶层）
        GlobalLoadingView()
    }
}

/**
 * ViewModel 或 Repository 中的使用示例
 */
class LoadingUsageExample {
    
    /**
     * 网络请求示例
     */
    suspend fun performNetworkRequest() {
        try {
            // 显示 Loading
            GlobalLoadingManager.show()
            
            // 模拟网络请求
            delay(2000)
            
            // 请求成功，隐藏 Loading
            GlobalLoadingManager.hide()
            
        } catch (e: Exception) {
            // 请求失败，也要隐藏 Loading
            GlobalLoadingManager.hide()
            throw e
        }
    }
    
    /**
     * 带自动隐藏的网络请求
     */
    suspend fun performNetworkRequestWithAutoHide() {
        GlobalLoadingManager.show()
        
        try {
            // 网络请求逻辑
            delay(1500)
            
        } finally {
            // 无论成功还是失败都隐藏 Loading
            GlobalLoadingManager.hide()
        }
    }
    
    /**
     * 多步骤操作示例
     */
    suspend fun performMultiStepOperation() {
        val coroutineManager = CoroutineManager.getInstance()
        
        coroutineManager.launchDefault(
            scopeName = "LoadingUsageExample",
            jobName = "MultiStepOperation"
        ) {
            GlobalLoadingManager.show()
            
            try {
                // 步骤 1
                delay(1000)
                
                // 步骤 2
                delay(1000)
                
                // 步骤 3
                delay(1000)
                
            } finally {
                coroutineManager.runOnMainThread {
                    GlobalLoadingManager.hide()
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LoadingViewExamplePreview() {
    PuxxiTheme {
        LoadingViewExample()
    }
}