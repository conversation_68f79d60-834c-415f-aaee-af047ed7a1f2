# DailyWeeklyToggle 设计图实现

## 🎯 设计要求

根据设计图的具体规格，重新设计 DailyWeeklyToggle 组件：

### 📏 尺寸规格
- **圆角**: 35.dp
- **长度**: 402.dp  
- **高度**: 70.dp
- **比例**: Daily 和 Weekly 为 1:1

### 🎨 颜色规格
- **背景渐变**: #A07E51 到 #FFF0CB，不透明度 10%
- **选中状态**: 
  - 文字颜色: #3A2E12
  - 背景: rank_type_bg.webp 图片
- **未选中状态**:
  - 文字颜色: 白色，不透明度 60%
  - 背景: 透明

## 🔧 实现方案

### 1. **整体容器设计**

```kotlin
@Composable
private fun DailyWeeklyToggle(
    selectedPeriod: RankPeriod,
    onPeriodSelected: (RankPeriod) -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        // 背景容器 - 渐变背景
        Box(
            modifier = Modifier
                .width(402.dp) // ✅ 设计图指定长度
                .height(70.dp) // ✅ 设计图指定高度
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0x1AA07E51), // #A07E51 with 10% opacity
                            Color(0x1AFFF0CB)  // #FFF0CB with 10% opacity
                        )
                    ),
                    shape = RoundedCornerShape(35.dp) // ✅ 设计图指定圆角
                )
        ) {
            // 内容行 - Daily 和 Weekly 按钮
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Daily 按钮 - 占据 1:1 比例
                PeriodToggleButton(
                    text = "Daily",
                    isSelected = selectedPeriod == RankPeriod.DAILY,
                    onClick = { onPeriodSelected(RankPeriod.DAILY) },
                    modifier = Modifier.weight(1f) // ✅ 1:1 比例
                )
                
                // Weekly 按钮 - 占据 1:1 比例
                PeriodToggleButton(
                    text = "Weekly",
                    isSelected = selectedPeriod == RankPeriod.WEEKLY,
                    onClick = { onPeriodSelected(RankPeriod.WEEKLY) },
                    modifier = Modifier.weight(1f) // ✅ 1:1 比例
                )
            }
        }
    }
}
```

### 2. **按钮组件设计**

```kotlin
@Composable
private fun PeriodToggleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxHeight()
            .padding(horizontal = 4.dp, vertical = 4.dp) // 内边距
            .clip(RoundedCornerShape(31.dp)) // 稍小于外层圆角
            .then(
                if (isSelected) {
                    // 选中时：使用背景图片
                    Modifier.background(Color.Transparent)
                } else {
                    // 未选中时：透明背景
                    Modifier.background(Color.Transparent)
                }
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // 选中时的背景图片
        if (isSelected) {
            ImageLoader.LoadImage(
                imageUrl = R.mipmap.rank_type_bg, // ✅ 设计图指定背景
                modifier = Modifier.fillMaxSize(),
                contentDescription = "Selected Background",
                contentScale = ContentScale.FillBounds,
                showLoading = false
            )
        }
        
        // 文字
        Text(
            text = text,
            color = if (isSelected) {
                Color(0xFF3A2E12) // ✅ 选中时：#3A2E12
            } else {
                Color.White.copy(alpha = 0.6f) // ✅ 未选中时：白色60%透明度
            },
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }
}
```

## 📊 设计对比

### 修改前 ❌ - 简单样式

```kotlin
// 旧版本 - 简单的背景色切换
Box(
    modifier = Modifier
        .background(
            if (isSelected) Color(0xFFFFD700) else Color(0xFF333333)
        )
        .padding(horizontal = 20.dp, vertical = 6.dp)
) {
    Text(
        color = if (isSelected) Color.Black else Color.White
    )
}
```

**问题：**
- ❌ 尺寸不符合设计图
- ❌ 没有渐变背景
- ❌ 颜色不符合设计规范
- ❌ 没有背景图片

### 修改后 ✅ - 设计图规范

```kotlin
// 新版本 - 完全按照设计图实现
Box(
    modifier = Modifier
        .width(402.dp) // 设计图尺寸
        .height(70.dp) // 设计图尺寸
        .background(
            brush = Brush.horizontalGradient(
                colors = listOf(
                    Color(0x1AA07E51), // 设计图渐变色
                    Color(0x1AFFF0CB)
                )
            ),
            shape = RoundedCornerShape(35.dp) // 设计图圆角
        )
) {
    // 选中时使用背景图片
    if (isSelected) {
        ImageLoader.LoadImage(imageUrl = R.mipmap.rank_type_bg)
    }
    
    Text(
        color = if (isSelected) {
            Color(0xFF3A2E12) // 设计图颜色
        } else {
            Color.White.copy(alpha = 0.6f) // 设计图透明度
        }
    )
}
```

**优势：**
- ✅ 尺寸完全符合设计图
- ✅ 渐变背景效果
- ✅ 颜色符合设计规范
- ✅ 选中时使用背景图片

## 🎨 视觉效果

### 整体布局：

```
┌─────────────────────────────────────────────────────────┐
│                                                         │
│    ┌─────────────────────────────────────────────────┐  │
│    │  ╭─────────────────┬─────────────────╮         │  │
│    │  │     Daily       │     Weekly      │         │  │
│    │  │   (1:1 比例)     │   (1:1 比例)     │         │  │
│    │  ╰─────────────────┴─────────────────╯         │  │
│    └─────────────────────────────────────────────────┘  │
│                                                         │
└─────────────────────────────────────────────────────────┘
     ←────────── 402.dp ──────────→
                                    ↑
                                  70.dp
                                    ↓
```

### 状态对比：

#### **未选中状态**:
```
┌─────────────────────────────────────────────────────────┐
│  ╭─────────────────┬─────────────────╮                 │
│  │     Daily       │     Weekly      │                 │
│  │  (白色60%透明)    │  (白色60%透明)    │                 │
│  │   透明背景        │   透明背景        │                 │
│  ╰─────────────────┴─────────────────╯                 │
└─────────────────────────────────────────────────────────┘
```

#### **选中状态 (Daily)**:
```
┌─────────────────────────────────────────────────────────┐
│  ╭─────────────────┬─────────────────╮                 │
│  │     Daily       │     Weekly      │                 │
│  │  (#3A2E12色)     │  (白色60%透明)    │                 │
│  │ [背景图片覆盖]     │   透明背景        │                 │
│  ╰─────────────────┴─────────────────╯                 │
└─────────────────────────────────────────────────────────┘
```

## 🔧 技术细节

### 1. **渐变背景实现**

```kotlin
.background(
    brush = Brush.horizontalGradient(
        colors = listOf(
            Color(0x1AA07E51), // #A07E51 with 10% opacity
            Color(0x1AFFF0CB)  // #FFF0CB with 10% opacity
        )
    ),
    shape = RoundedCornerShape(35.dp)
)
```

**说明：**
- `0x1A` = 26 (十进制) = 10.2% 不透明度
- `Brush.horizontalGradient` 创建水平渐变
- `RoundedCornerShape(35.dp)` 设置圆角

### 2. **背景图片处理**

```kotlin
if (isSelected) {
    ImageLoader.LoadImage(
        imageUrl = R.mipmap.rank_type_bg,
        modifier = Modifier.fillMaxSize(),
        contentDescription = "Selected Background",
        contentScale = ContentScale.FillBounds, // 填充整个区域
        showLoading = false // 不显示加载状态
    )
}
```

**说明：**
- `ContentScale.FillBounds` 确保图片填充整个按钮区域
- `showLoading = false` 避免加载动画影响用户体验
- 只在选中状态时显示背景图片

### 3. **颜色透明度计算**

```kotlin
// 设计图要求：白色 60% 透明度
Color.White.copy(alpha = 0.6f)

// 设计图要求：#A07E51 10% 透明度
Color(0x1AA07E51) // 0x1A = 26/255 ≈ 10.2%
```

### 4. **布局权重**

```kotlin
Row {
    PeriodToggleButton(
        modifier = Modifier.weight(1f) // Daily 占 50%
    )
    PeriodToggleButton(
        modifier = Modifier.weight(1f) // Weekly 占 50%
    )
}
```

## ✅ 设计规范符合度

### 尺寸规范 ✅
- ✅ **圆角**: 35.dp
- ✅ **长度**: 402.dp
- ✅ **高度**: 70.dp
- ✅ **比例**: 1:1

### 颜色规范 ✅
- ✅ **背景渐变**: #A07E51 到 #FFF0CB，10% 透明度
- ✅ **选中文字**: #3A2E12
- ✅ **未选中文字**: 白色 60% 透明度
- ✅ **选中背景**: rank_type_bg.webp
- ✅ **未选中背景**: 透明

### 交互规范 ✅
- ✅ **点击响应**: 立即切换状态
- ✅ **视觉反馈**: 背景图片和文字颜色变化
- ✅ **状态同步**: 与 ViewModel 状态保持同步

## 🚀 总结

**DailyWeeklyToggle 设计图实现完成！**

### 核心特性：
1. ✅ **精确尺寸** - 完全按照设计图的 402×70dp 规格
2. ✅ **渐变背景** - #A07E51 到 #FFF0CB 的水平渐变
3. ✅ **背景图片** - 选中时使用 rank_type_bg.webp
4. ✅ **颜色规范** - 严格按照设计图的颜色要求

### 视觉效果：
- 🎨 **设计一致** - 完全符合设计图规范
- 📱 **响应式布局** - 1:1 比例的按钮布局
- ✨ **视觉层次** - 渐变背景 + 背景图片 + 文字颜色
- 🔄 **状态反馈** - 清晰的选中/未选中状态区分

现在 DailyWeeklyToggle 组件完全按照设计图实现，包括精确的尺寸、渐变背景、背景图片和颜色规范！
