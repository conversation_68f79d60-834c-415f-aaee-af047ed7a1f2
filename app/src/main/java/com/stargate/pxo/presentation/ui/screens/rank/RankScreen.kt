package com.stargate.pxo.presentation.ui.screens.rank

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import com.king.ultraswiperefresh.NestedScrollMode
import com.king.ultraswiperefresh.UltraSwipeRefresh
import com.king.ultraswiperefresh.rememberUltraSwipeRefreshState
import com.stargate.pxo.R
import com.stargate.pxo.presentation.ui.navigation.NavigationManager
import com.stargate.pxo.presentation.ui.theme.noRippleClickable
import com.stargate.pxo.presentation.ui.view.ImageLoader
import kotlinx.coroutines.launch

/**
 * 排行榜屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RankScreen(
    viewModel: RankViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState(pageCount = { 3 })
    val scope = rememberCoroutineScope()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
    ) {
        // Background image
        ImageLoader.LoadImage(
            imageUrl = R.mipmap.bg_rank,
            modifier = Modifier.fillMaxSize(),
            contentDescription = "Rank Background",
            showLoading = false
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFF27152E)) // 使用和 HomeScreen 一样的背景色
                .statusBarsPadding()
        ) {
            // Top Bar with Back Button and Tab Row (合并在同一行)
            RankTopBarWithTabs(
                selectedTabIndex = pagerState.currentPage,
                onBackClick = {
                    NavigationManager.smartPopBack()
                },
                onTabSelected = { index ->
                    scope.launch {
                        pagerState.animateScrollToPage(index)
                    }
                }
            )

            // Daily/Weekly Toggle
            DailyWeeklyToggle(
                selectedPeriod = uiState.selectedPeriod,
                onPeriodSelected = { period ->
                    viewModel.selectPeriod(period)
                }
            )

            // Horizontal Pager with Lists
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val tabType = when (page) {
                    0 -> RankTabType.CHARM
                    1 -> RankTabType.RICK
                    2 -> RankTabType.COUPLE
                    else -> RankTabType.CHARM
                }

                RankListContent(
                    tabType = tabType,
                    uiState = uiState,
                    viewModel = viewModel
                )
            }
        }

        // Monitor page changes
        LaunchedEffect(pagerState.currentPage) {
            val tabType = when (pagerState.currentPage) {
                0 -> RankTabType.CHARM
                1 -> RankTabType.RICK
                2 -> RankTabType.COUPLE
                else -> RankTabType.CHARM
            }
            viewModel.selectTab(tabType)
        }
    }
}

@Composable
private fun RankTopBarWithTabs(
    selectedTabIndex: Int,
    onBackClick: () -> Unit,
    onTabSelected: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .padding(top = 45.dp)
            .fillMaxWidth()
            .height(85.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 标签栏 - 参考 HomeScreen 的 TopFilterTabsWithIcons 样式
            LazyRow(
                modifier = Modifier.padding(top = 25.dp).weight(1f).fillMaxHeight(),
            ) {
                itemsIndexed(listOf("Charm", "Rich", "Couple")) { index, title ->
                    RankTabItem(
                        title = title,
                        isSelected = index == selectedTabIndex,
                        onClick = { onTabSelected(index) }
                    )
                }
            }

            // 右侧占位，保持布局平衡
            Spacer(modifier = Modifier.width(48.dp))
        }
    }
}

@Composable
private fun RankTabItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    // 动画化的颜色和字体大小 - 参考 HomeScreen 的 TagTab
    val animatedTextColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFFE5C98B) else Color.White.copy(alpha = 0.6f),
        animationSpec = tween(durationMillis = 300),
        label = "text_color"
    )

    val animatedFontSize by animateFloatAsState(
        targetValue = if (isSelected) 42f else 32f,
        animationSpec = tween(durationMillis = 300),
        label = "font_size"
    )

    Box(
        modifier = Modifier
            .noRippleClickable(onClick = onClick)
            .padding(horizontal = 19.dp)
            .height(60.dp), // 固定高度确保对齐
        contentAlignment = if (isSelected) Alignment.BottomCenter else Alignment.Center
    ) {
        Column (
            verticalArrangement = if (isSelected) Arrangement.Bottom else Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                color = animatedTextColor,
                fontSize = animatedFontSize.sp,
                fontWeight = FontWeight.SemiBold
            )

            // 选中指示器动画 - 参考 HomeScreen 的样式
            AnimatedVisibility(
                visible = isSelected,
                enter = fadeIn(animationSpec = tween(300)) + slideInHorizontally(
                    initialOffsetX = { it / 2 },
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + slideOutHorizontally(
                    targetOffsetX = { it / 2 },
                    animationSpec = tween(200)
                )
            ) {
                // 使用简单的下划线代替图片
                Box(
                    modifier = Modifier
                        .width(60.dp)
                        .height(4.dp)
                        .background(
                            Color(0xFFE5C98B),
                            RoundedCornerShape(2.dp)
                        )
                        .offset(y = (-5).dp)
                )
            }
        }
    }
}

@Composable
private fun DailyWeeklyToggle(
    selectedPeriod: RankPeriod,
    onPeriodSelected: (RankPeriod) -> Unit
) {
    // 根据设计图的规格
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        // 背景容器 - 渐变背景
        Box(
            modifier = Modifier
                .width(402.dp)
                .height(70.dp)
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0x1AA07E51), // #A07E51 with 10% opacity
                            Color(0x1AFFF0CB)  // #FFF0CB with 10% opacity
                        )
                    ),
                    shape = RoundedCornerShape(35.dp)
                )
        ) {
            // 内容行 - Daily 和 Weekly 按钮
            Row(
                modifier = Modifier.fillMaxSize(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Daily 按钮 - 占据 1:1 比例
                PeriodToggleButton(
                    text = "Daily",
                    isSelected = selectedPeriod == RankPeriod.DAILY,
                    onClick = { onPeriodSelected(RankPeriod.DAILY) },
                    modifier = Modifier.weight(1f)
                )

                // Weekly 按钮 - 占据 1:1 比例
                PeriodToggleButton(
                    text = "Weekly",
                    isSelected = selectedPeriod == RankPeriod.WEEKLY,
                    onClick = { onPeriodSelected(RankPeriod.WEEKLY) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun PeriodToggleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxHeight()
            .padding(horizontal = 4.dp, vertical = 4.dp) // 内边距
            .clip(RoundedCornerShape(31.dp)) // 稍小于外层圆角
            .then(
                if (isSelected) {
                    // 选中时：使用背景图片
                    Modifier.background(Color.Transparent) // 先设置透明背景，图片会覆盖
                } else {
                    // 未选中时：透明背景
                    Modifier.background(Color.Transparent)
                }
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        // 选中时的背景图片
        if (isSelected) {
            ImageLoader.LoadImage(
                imageUrl = R.mipmap.rank_type_bg,
                modifier = Modifier.fillMaxSize(),
                contentDescription = "Selected Background",
                contentScale = ContentScale.FillBounds,
                showLoading = false
            )
        }

        // 文字
        Text(
            text = text,
            color = if (isSelected) {
                Color(0xFF3A2E12) // 选中时：#3A2E12
            } else {
                Color.White.copy(alpha = 0.6f) // 未选中时：白色60%透明度
            },
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun RankListContent(
    tabType: RankTabType,
    uiState: RankUiState,
    viewModel: RankViewModel
) {
    val refreshState = rememberUltraSwipeRefreshState()
    val listState = rememberLazyListState()

    // Get data based on current tab and period
    val rankItems = when (tabType) {
        RankTabType.CHARM -> uiState.charmRankItems
        RankTabType.RICK -> uiState.rickRankItems
        RankTabType.COUPLE -> uiState.coupleRankItems
    }

    // 更新刷新状态
    refreshState.isRefreshing = uiState.isRefreshing

    UltraSwipeRefresh(
        state = refreshState,
        onRefresh = {
            viewModel.refreshRankData(tabType)
        },
        onLoadMore = {},
        modifier = Modifier.fillMaxSize(),
        headerScrollMode = NestedScrollMode.Translate,
        footerScrollMode = NestedScrollMode.Translate,
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Special header item - 根据不同类型显示不同的头部
            item {
                when (tabType) {
                    RankTabType.CHARM -> CharmRankHeader()
                    RankTabType.RICK -> RichRankHeader()
                    RankTabType.COUPLE -> CoupleRankHeader()
                }
            }

            // Rank items - 根据不同类型显示不同的列表项
            items(rankItems) { rankItem ->
                when (tabType) {
                    RankTabType.CHARM -> CharmRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle charm item click */ }
                    )
                    RankTabType.RICK -> RichRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle rich item click */ }
                    )
                    RankTabType.COUPLE -> CoupleRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle couple item click */ }
                    )
                }
            }
        }
    }
}

// ============ 魅力排行榜头部 ============
@Composable
private fun CharmRankHeader() {
    // 根据设计图：Row 布局显示前三名，使用背景图片
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 40.dp) // 左右间隔 40.dp
            .height(160.dp), // 适当高度容纳背景图片和文字
        horizontalArrangement = Arrangement.spacedBy(40.dp), // item 之间 40.dp
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 第二名 (左侧)
        RankHeaderItem(
            rank = 2,
            backgroundImage = R.mipmap.rank_head_2,
            userName = "User2", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f)
        )

        // 第一名 (中间，稍大)
        RankHeaderItem(
            rank = 1,
            backgroundImage = R.mipmap.rank_head_1,
            userName = "User1", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f),
            isFirst = true // 第一名可能需要特殊样式
        )

        // 第三名 (右侧)
        RankHeaderItem(
            rank = 3,
            backgroundImage = R.mipmap.rank_head_3,
            userName = "User3", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f)
        )
    }
}

// ============ 财富排行榜头部 ============
@Composable
private fun RichRankHeader() {
    // 根据设计图：与 CharmRankHeader 相同的布局
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 40.dp) // 左右间隔 40.dp
            .height(160.dp), // 适当高度容纳背景图片和文字
        horizontalArrangement = Arrangement.spacedBy(40.dp), // item 之间 40.dp
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 第二名 (左侧)
        RankHeaderItem(
            rank = 2,
            backgroundImage = R.mipmap.rank_head_2,
            userName = "RichUser2", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f)
        )

        // 第一名 (中间，稍大)
        RankHeaderItem(
            rank = 1,
            backgroundImage = R.mipmap.rank_head_1,
            userName = "RichUser1", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f),
            isFirst = true // 第一名可能需要特殊样式
        )

        // 第三名 (右侧)
        RankHeaderItem(
            rank = 3,
            backgroundImage = R.mipmap.rank_head_3,
            userName = "RichUser3", // 这里应该从实际数据获取
            modifier = Modifier.weight(1f)
        )
    }
}

// ============ 排行榜头部单项组件 ============
@Composable
private fun RankHeaderItem(
    rank: Int,
    backgroundImage: Int,
    userName: String,
    modifier: Modifier = Modifier,
    isFirst: Boolean = false,
    userAvatar: String? = null // 用户头像URL
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 背景图片容器
        Box(
            modifier = Modifier
                .size(if (isFirst) 120.dp else 100.dp) // 第一名稍大
                .aspectRatio(1f),
            contentAlignment = Alignment.Center
        ) {
            // 背景图片 (rank_head_1/2/3.webp)
            ImageLoader.LoadImage(
                imageUrl = backgroundImage,
                modifier = Modifier.fillMaxSize(),
                contentDescription = "Rank $rank Background",
                contentScale = ContentScale.FillBounds,
                showLoading = false
            )

            // 镂空部分的用户头像
            Box(
                modifier = Modifier
                    .size(if (isFirst) 60.dp else 50.dp) // 头像大小
                    .background(
                        Color.Gray.copy(alpha = 0.3f), // 占位背景
                        RoundedCornerShape(50) // 圆形头像
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (userAvatar != null) {
                    // 加载真实头像
                    ImageLoader.LoadImage(
                        imageUrl = userAvatar,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(50)), // 圆形裁剪
                        contentDescription = "User Avatar",
                        contentScale = ContentScale.Crop,
                        showLoading = false
                    )
                } else {
                    // 占位图标
                    Text(
                        text = "👤",
                        fontSize = if (isFirst) 24.sp else 20.sp,
                        color = Color.White
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 用户名称
        Text(
            text = userName,
            color = Color.White,
            fontSize = if (isFirst) 16.sp else 14.sp,
            fontWeight = if (isFirst) FontWeight.Bold else FontWeight.Medium,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
    }
}

// ============ 情侣排行榜头部 ============
@Composable
private fun CoupleRankHeader() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(140.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF880E4F) // 粉红色背景
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "💕 COUPLE RANKING 💕",
                    color = Color(0xFFFF69B4),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Most Popular Couples",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    }
}

// ============ 魅力排行榜列表项 ============
@Composable
private fun CharmRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：4-数据长度使用统一的 Row 设计
    if (rankItem.rank >= 4) {
        // 第4名及以后使用统一的简单 Row 设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    } else {
        // 前3名使用特殊设计（如果需要的话，这里可以是空的，因为前3名在Header中显示）
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    }
}

// ============ 财富排行榜列表项 ============
@Composable
private fun RichRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：4-数据长度使用统一的 Row 设计
    if (rankItem.rank >= 4) {
        // 第4名及以后使用统一的简单 Row 设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    } else {
        // 前3名使用特殊设计（如果需要的话，这里可以是空的，因为前3名在Header中显示）
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    }
}

// ============ 情侣排行榜列表项 ============
@Composable
private fun CoupleRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：从第2项开始到数据结束使用统一的 Row 设计
    if (rankItem.rank >= 2) {
        // 第2名及以后使用统一的简单 Row 设计
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    } else {
        // 第1名使用特殊设计（如果需要的话）
        StandardRankItem(
            rankItem = rankItem,
            onClick = onClick
        )
    }
}

// ============ 统一的排行榜列表项 ============
@Composable
private fun StandardRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    // 根据设计图：背景色偶数为#000000，奇数为#27152E
    val backgroundColor = if (rankItem.rank % 2 == 0) {
        Color(0xFF000000) // 偶数：黑色
    } else {
        Color(0xFF27152E) // 奇数：深紫色
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 序号文字
        Text(
            text = rankItem.rank.toString(),
            color = Color.White,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.width(40.dp) // 固定宽度确保对齐
        )

        Spacer(modifier = Modifier.width(16.dp))

        // 圆形头像 - 90.dp
        Box(
            modifier = Modifier
                .size(90.dp)
                .background(
                    Color.Gray.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(45.dp) // 圆形
                ),
            contentAlignment = Alignment.Center
        ) {
            // 这里应该加载真实头像
            if (rankItem.avatar != null) {
                ImageLoader.LoadImage(
                    imageUrl = rankItem.avatar,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(45.dp)), // 圆形裁剪
                    contentDescription = "User Avatar",
                    contentScale = ContentScale.Crop,
                    showLoading = false
                )
            } else {
                // 占位图标
                Text(
                    text = "👤",
                    fontSize = 32.sp,
                    color = Color.White
                )
            }
        }

        Spacer(modifier = Modifier.width(16.dp))

        // 用户名称
        Text(
            text = rankItem.userName,
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}