package com.stargate.pxo.presentation.ui.screens.rank

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import com.king.ultraswiperefresh.NestedScrollMode
import com.king.ultraswiperefresh.UltraSwipeRefresh
import com.king.ultraswiperefresh.rememberUltraSwipeRefreshState
import kotlinx.coroutines.launch

/**
 * 排行榜屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RankScreen(
    viewModel: RankViewModel = hiltViewModel(),
    globalNavController: NavHostController? = null
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState(pageCount = { 3 })
    val scope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Tab Row
        RankTabRow(
            selectedTabIndex = pagerState.currentPage,
            onTabSelected = { index ->
                scope.launch {
                    pagerState.animateScrollToPage(index)
                }
            }
        )
        
        // Daily/Weekly Toggle
        DailyWeeklyToggle(
            selectedPeriod = uiState.selectedPeriod,
            onPeriodSelected = { period ->
                viewModel.selectPeriod(period)
            }
        )
        
        // Horizontal Pager with Lists
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val tabType = when (page) {
                0 -> RankTabType.CHARM
                1 -> RankTabType.RICK
                2 -> RankTabType.COUPLE
                else -> RankTabType.CHARM
            }
            
            RankListContent(
                tabType = tabType,
                uiState = uiState,
                viewModel = viewModel
            )
        }
    }
    
    // Monitor page changes
    LaunchedEffect(pagerState.currentPage) {
        val tabType = when (pagerState.currentPage) {
            0 -> RankTabType.CHARM
            1 -> RankTabType.RICK
            2 -> RankTabType.COUPLE
            else -> RankTabType.CHARM
        }
        viewModel.selectTab(tabType)
    }
}

@Composable
private fun RankTabRow(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit
) {
    val tabs = listOf("Charm", "Rick", "Couple")
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        tabs.forEachIndexed { index, title ->
            RankTab(
                title = title,
                isSelected = selectedTabIndex == index,
                onClick = { onTabSelected(index) }
            )
        }
    }
}

@Composable
private fun RankTab(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(
                if (isSelected) Color(0xFFFFD700) else Color.Transparent
            )
            .clickable { onClick() }
            .padding(horizontal = 24.dp, vertical = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = title,
            color = if (isSelected) Color.Black else Color.White,
            fontSize = 16.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun DailyWeeklyToggle(
    selectedPeriod: RankPeriod,
    onPeriodSelected: (RankPeriod) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        PeriodToggleButton(
            text = "Daily",
            isSelected = selectedPeriod == RankPeriod.DAILY,
            onClick = { onPeriodSelected(RankPeriod.DAILY) }
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        PeriodToggleButton(
            text = "Weekly",
            isSelected = selectedPeriod == RankPeriod.WEEKLY,
            onClick = { onPeriodSelected(RankPeriod.WEEKLY) }
        )
    }
}

@Composable
private fun PeriodToggleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(
                if (isSelected) Color(0xFFFFD700) else Color(0xFF333333)
            )
            .clickable { onClick() }
            .padding(horizontal = 20.dp, vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isSelected) Color.Black else Color.White,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun RankListContent(
    tabType: RankTabType,
    uiState: RankUiState,
    viewModel: RankViewModel
) {
    val refreshState = rememberUltraSwipeRefreshState()
    val listState = rememberLazyListState()
    
    // Get data based on current tab and period
    val rankItems = when (tabType) {
        RankTabType.CHARM -> uiState.charmRankItems
        RankTabType.RICK -> uiState.rickRankItems
        RankTabType.COUPLE -> uiState.coupleRankItems
    }
    
    LaunchedEffect(refreshState.isRefreshing) {
        if (refreshState.isRefreshing) {
            viewModel.refreshRankData(tabType)
            refreshState.isRefreshing = false
        }
    }
    
    // Load more when reaching end
    LaunchedEffect(listState) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
            .collect { lastVisibleIndex ->
                if (lastVisibleIndex != null && 
                    lastVisibleIndex >= rankItems.size - 3 && 
                    !uiState.isLoading) {
                    viewModel.loadMoreRankData(tabType)
                }
            }
    }
    
//    UltraSwipeRefresh(
//        state = refreshState,
//        onRefresh = {
//            viewModel.refreshRankData(tabType)
//        },
//        modifier = Modifier.fillMaxSize(),
//        nestedScrollMode = NestedScrollMode.FixedContent
//    ) {
//        LazyColumn(
//            state = listState,
//            modifier = Modifier.fillMaxSize(),
//            contentPadding = PaddingValues(16.dp),
//            verticalArrangement = Arrangement.spacedBy(8.dp)
//        ) {
//            // Special header item
//            item {
//                RankHeaderItem(tabType = tabType)
//            }
//
//            // Rank items
//            items(rankItems) { rankItem ->
//                RankListItem(
//                    rankItem = rankItem,
//                    onClick = {
//                        // Handle item click
//                    }
//                )
//            }
//
//            // Loading indicator
//            if (uiState.isLoading) {
//                item {
//                    Box(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(16.dp),
//                        contentAlignment = Alignment.Center
//                    ) {
//                        CircularProgressIndicator(
//                            color = Color(0xFFFFD700)
//                        )
//                    }
//                }
//            }
//        }
//    }
}

@Composable
private fun RankHeaderItem(tabType: RankTabType) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1A1A1A)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = when (tabType) {
                    RankTabType.CHARM -> "✨ Charm Ranking ✨"
                    RankTabType.RICK -> "🎯 Rick Ranking 🎯"
                    RankTabType.COUPLE -> "💕 Couple Ranking 💕"
                },
                color = Color.White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun RankListItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2A2A2A)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Rank number
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .background(
                        color = when (rankItem.rank) {
                            1 -> Color(0xFFFFD700) // Gold
                            2 -> Color(0xFFC0C0C0) // Silver
                            3 -> Color(0xFFCD7F32) // Bronze
                            else -> Color(0xFF666666)
                        },
                        shape = RoundedCornerShape(16.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = rankItem.rank.toString(),
                    color = Color.White,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // User info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rankItem.userName,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "Score: ${rankItem.score}",
                    color = Color.Gray,
                    fontSize = 14.sp
                )
            }
            
            // Additional info
            Text(
                text = rankItem.additionalInfo,
                color = Color(0xFFFFD700),
                fontSize = 12.sp
            )
        }
    }
} 