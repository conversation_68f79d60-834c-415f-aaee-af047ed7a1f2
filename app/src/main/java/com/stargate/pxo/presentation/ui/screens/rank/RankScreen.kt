package com.stargate.pxo.presentation.ui.screens.rank

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import com.king.ultraswiperefresh.NestedScrollMode
import com.king.ultraswiperefresh.UltraSwipeRefresh
import com.king.ultraswiperefresh.rememberUltraSwipeRefreshState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import com.stargate.pxo.R
import com.stargate.pxo.presentation.ui.navigation.NavigationManager
import com.stargate.pxo.presentation.ui.theme.noRippleClickable
import com.stargate.pxo.presentation.ui.view.ImageLoader
import kotlinx.coroutines.launch

/**
 * 排行榜屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RankScreen(
    viewModel: RankViewModel = hiltViewModel(),
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val pagerState = rememberPagerState(pageCount = { 3 })
    val scope = rememberCoroutineScope()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
    ) {
        // Background image
        ImageLoader.LoadImage(
            imageUrl = R.mipmap.bg_rank,
            modifier = Modifier.fillMaxSize(),
            contentDescription = "Rank Background",
            showLoading = false
        )

        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFF27152E)) // 使用和 HomeScreen 一样的背景色
                .statusBarsPadding()
        ) {
            // Top Bar with Back Button and Tab Row (合并在同一行)
            RankTopBarWithTabs(
                selectedTabIndex = pagerState.currentPage,
                onBackClick = {
                    NavigationManager.smartPopBack()
                },
                onTabSelected = { index ->
                    scope.launch {
                        pagerState.animateScrollToPage(index)
                    }
                }
            )

            // Daily/Weekly Toggle
            DailyWeeklyToggle(
                selectedPeriod = uiState.selectedPeriod,
                onPeriodSelected = { period ->
                    viewModel.selectPeriod(period)
                }
            )

            // Horizontal Pager with Lists
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val tabType = when (page) {
                    0 -> RankTabType.CHARM
                    1 -> RankTabType.RICK
                    2 -> RankTabType.COUPLE
                    else -> RankTabType.CHARM
                }

                RankListContent(
                    tabType = tabType,
                    uiState = uiState,
                    viewModel = viewModel
                )
            }
        }

        // Monitor page changes
        LaunchedEffect(pagerState.currentPage) {
            val tabType = when (pagerState.currentPage) {
                0 -> RankTabType.CHARM
                1 -> RankTabType.RICK
                2 -> RankTabType.COUPLE
                else -> RankTabType.CHARM
            }
            viewModel.selectTab(tabType)
        }
    }
}

@Composable
private fun RankTopBarWithTabs(
    selectedTabIndex: Int,
    onBackClick: () -> Unit,
    onTabSelected: (Int) -> Unit
) {
    Column(
        modifier = Modifier
            .padding(top = 45.dp)
            .fillMaxWidth()
            .height(85.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 返回按钮
            IconButton(
                onClick = onBackClick,
                modifier = Modifier.size(48.dp)
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // 标签栏 - 参考 HomeScreen 的 TopFilterTabsWithIcons 样式
            LazyRow(
                modifier = Modifier.padding(top = 25.dp).weight(1f).fillMaxHeight(),
            ) {
                itemsIndexed(listOf("Charm", "Rich", "Couple")) { index, title ->
                    RankTabItem(
                        title = title,
                        isSelected = index == selectedTabIndex,
                        onClick = { onTabSelected(index) }
                    )
                }
            }

            // 右侧占位，保持布局平衡
            Spacer(modifier = Modifier.width(48.dp))
        }
    }
}

@Composable
private fun RankTabItem(
    title: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    // 动画化的颜色和字体大小 - 参考 HomeScreen 的 TagTab
    val animatedTextColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFFE5C98B) else Color.White.copy(alpha = 0.6f),
        animationSpec = tween(durationMillis = 300),
        label = "text_color"
    )

    val animatedFontSize by animateFloatAsState(
        targetValue = if (isSelected) 42f else 32f,
        animationSpec = tween(durationMillis = 300),
        label = "font_size"
    )

    Box(
        modifier = Modifier
            .noRippleClickable(onClick = onClick)
            .padding(horizontal = 19.dp)
            .height(60.dp), // 固定高度确保对齐
        contentAlignment = if (isSelected) Alignment.BottomCenter else Alignment.Center
    ) {
        Column (
            verticalArrangement = if (isSelected) Arrangement.Bottom else Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = title,
                color = animatedTextColor,
                fontSize = animatedFontSize.sp,
                fontWeight = FontWeight.SemiBold
            )

            // 选中指示器动画 - 参考 HomeScreen 的样式
            AnimatedVisibility(
                visible = isSelected,
                enter = fadeIn(animationSpec = tween(300)) + slideInHorizontally(
                    initialOffsetX = { it / 2 },
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + slideOutHorizontally(
                    targetOffsetX = { it / 2 },
                    animationSpec = tween(200)
                )
            ) {
                // 使用简单的下划线代替图片
                Box(
                    modifier = Modifier
                        .width(60.dp)
                        .height(4.dp)
                        .background(
                            Color(0xFFE5C98B),
                            RoundedCornerShape(2.dp)
                        )
                        .offset(y = (-5).dp)
                )
            }
        }
    }
}

@Composable
private fun DailyWeeklyToggle(
    selectedPeriod: RankPeriod,
    onPeriodSelected: (RankPeriod) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        PeriodToggleButton(
            text = "Daily",
            isSelected = selectedPeriod == RankPeriod.DAILY,
            onClick = { onPeriodSelected(RankPeriod.DAILY) }
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        PeriodToggleButton(
            text = "Weekly",
            isSelected = selectedPeriod == RankPeriod.WEEKLY,
            onClick = { onPeriodSelected(RankPeriod.WEEKLY) }
        )
    }
}

@Composable
private fun PeriodToggleButton(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .background(
                if (isSelected) Color(0xFFFFD700) else Color(0xFF333333)
            )
            .clickable { onClick() }
            .padding(horizontal = 20.dp, vertical = 6.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = if (isSelected) Color.Black else Color.White,
            fontSize = 14.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
private fun RankListContent(
    tabType: RankTabType,
    uiState: RankUiState,
    viewModel: RankViewModel
) {
    val refreshState = rememberUltraSwipeRefreshState()
    val listState = rememberLazyListState()

    // Get data based on current tab and period
    val rankItems = when (tabType) {
        RankTabType.CHARM -> uiState.charmRankItems
        RankTabType.RICK -> uiState.rickRankItems
        RankTabType.COUPLE -> uiState.coupleRankItems
    }

    // 更新刷新状态
    refreshState.isRefreshing = uiState.isRefreshing

    UltraSwipeRefresh(
        state = refreshState,
        onRefresh = {
            viewModel.refreshRankData(tabType)
        },
        onLoadMore = {},
        modifier = Modifier.fillMaxSize(),
        headerScrollMode = NestedScrollMode.Translate,
        footerScrollMode = NestedScrollMode.Translate,
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Special header item - 根据不同类型显示不同的头部
            item {
                when (tabType) {
                    RankTabType.CHARM -> CharmRankHeader()
                    RankTabType.RICK -> RichRankHeader()
                    RankTabType.COUPLE -> CoupleRankHeader()
                }
            }

            // Rank items - 根据不同类型显示不同的列表项
            items(rankItems) { rankItem ->
                when (tabType) {
                    RankTabType.CHARM -> CharmRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle charm item click */ }
                    )
                    RankTabType.RICK -> RichRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle rich item click */ }
                    )
                    RankTabType.COUPLE -> CoupleRankItem(
                        rankItem = rankItem,
                        onClick = { /* Handle couple item click */ }
                    )
                }
            }
        }
    }
}

// ============ 魅力排行榜头部 ============
@Composable
private fun CharmRankHeader() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(140.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2D1B69) // 紫色渐变背景
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "✨ CHARM RANKING ✨",
                    color = Color(0xFFE040FB),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Most Charming Broadcasters",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    }
}

// ============ 财富排行榜头部 ============
@Composable
private fun RichRankHeader() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(140.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1B5E20) // 绿色背景
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "💰 RICH RANKING 💰",
                    color = Color(0xFFFFD700),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Wealthiest Users",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    }
}

// ============ 情侣排行榜头部 ============
@Composable
private fun CoupleRankHeader() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(140.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF880E4F) // 粉红色背景
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "💕 COUPLE RANKING 💕",
                    color = Color(0xFFFF69B4),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Most Popular Couples",
                    color = Color.White.copy(alpha = 0.8f),
                    fontSize = 14.sp
                )
            }
        }
    }
}

// ============ 魅力排行榜列表项 ============
@Composable
private fun CharmRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1A1A2E) // 深紫色背景
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 排名徽章 - 魅力风格
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = when (rankItem.rank) {
                            1 -> Color(0xFFE040FB) // 紫色
                            2 -> Color(0xFFAB47BC)
                            3 -> Color(0xFF8E24AA)
                            else -> Color(0xFF512DA8)
                        },
                        shape = RoundedCornerShape(20.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = rankItem.rank.toString(),
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 头像占位符
            Box(
                modifier = Modifier
                    .size(50.dp)
                    .background(
                        Color(0xFF9C27B0),
                        shape = RoundedCornerShape(25.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "✨",
                    fontSize = 20.sp
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rankItem.userName,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Charm: ${rankItem.score}",
                    color = Color(0xFFE040FB),
                    fontSize = 14.sp
                )
            }

            // 魅力值显示
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = rankItem.additionalInfo,
                    color = Color(0xFFE040FB),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Charm Points",
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = 12.sp
                )
            }
        }
    }
}

// ============ 财富排行榜列表项 ============
@Composable
private fun RichRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF0D2818) // 深绿色背景
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 排名徽章 - 财富风格
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = when (rankItem.rank) {
                            1 -> Color(0xFFFFD700) // 金色
                            2 -> Color(0xFFC0C0C0) // 银色
                            3 -> Color(0xFFCD7F32) // 铜色
                            else -> Color(0xFF4CAF50) // 绿色
                        },
                        shape = RoundedCornerShape(20.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = rankItem.rank.toString(),
                    color = if (rankItem.rank <= 3) Color.Black else Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 头像占位符
            Box(
                modifier = Modifier
                    .size(50.dp)
                    .background(
                        Color(0xFF4CAF50),
                        shape = RoundedCornerShape(25.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "💰",
                    fontSize = 20.sp
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rankItem.userName,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Wealth: ${rankItem.score}",
                    color = Color(0xFFFFD700),
                    fontSize = 14.sp
                )
            }

            // 财富值显示
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = rankItem.additionalInfo,
                    color = Color(0xFFFFD700),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Gold Coins",
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = 12.sp
                )
            }
        }
    }
}

// ============ 情侣排行榜列表项 ============
@Composable
private fun CoupleRankItem(
    rankItem: RankItem,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF2D1B2F) // 深粉色背景
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 排名徽章 - 情侣风格
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .background(
                        color = when (rankItem.rank) {
                            1 -> Color(0xFFFF69B4) // 粉红色
                            2 -> Color(0xFFFF1493) // 深粉色
                            3 -> Color(0xFFDC143C) // 深红色
                            else -> Color(0xFFAD1457) // 紫红色
                        },
                        shape = RoundedCornerShape(20.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = rankItem.rank.toString(),
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 情侣头像组合
            Row {
                // 用户头像
                Box(
                    modifier = Modifier
                        .size(45.dp)
                        .background(
                            Color(0xFFFF69B4),
                            shape = RoundedCornerShape(22.5.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "👤",
                        fontSize = 18.sp
                    )
                }

                // 重叠效果
                Box(
                    modifier = Modifier
                        .size(45.dp)
                        .offset(x = (-10).dp)
                        .background(
                            Color(0xFFAD1457),
                            shape = RoundedCornerShape(22.5.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "👤",
                        fontSize = 18.sp
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 情侣信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rankItem.userName,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Love Score: ${rankItem.score}",
                    color = Color(0xFFFF69B4),
                    fontSize = 14.sp
                )
            }

            // 爱心值显示
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = rankItem.additionalInfo,
                    color = Color(0xFFFF69B4),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Love Points",
                    color = Color.White.copy(alpha = 0.6f),
                    fontSize = 12.sp
                )
            }
        }
    }
}