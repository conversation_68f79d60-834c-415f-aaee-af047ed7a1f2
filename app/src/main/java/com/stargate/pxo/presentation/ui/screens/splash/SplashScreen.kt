package com.stargate.pxo.presentation.ui.screens.splash

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.stargate.pxo.R
import com.stargate.pxo.presentation.ui.view.ImageLoader

/**
 * Splash Screen
 * Displays a purple background with a gold logo
 */
@Composable
fun SplashScreen(
    navController: NavController,
    viewModel: SplashViewModel = hiltViewModel()
) {
    val state by viewModel.uiState.collectAsState()
    
    // Monitor navigation events
    LaunchedEffect(state.navigateToMain) {
        if (state.navigateToMain) {
            navController.navigate("main") {
                popUpTo("splash") { inclusive = true }
            }
        }
    }
    
    // Monitor login navigation
    LaunchedEffect(state.navigateToLogin) {
        if (state.navigateToLogin) {
            navController.navigate("login") {
                popUpTo("splash") { inclusive = true }
            }
        }
    }
    
    // Splash screen content
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
    ) {
        // Background image (using gradient background)
        ImageLoader.LoadImage(
            imageUrl = R.mipmap.bg_splash,
            modifier = Modifier.fillMaxSize(),
            contentDescription = stringResource(R.string.splash_description),
            contentScale = ContentScale.Crop,
            showLoading = false
        )
        
 //        // Centered Logo
 //        Box(
 //            modifier = Modifier.fillMaxSize(),
 //            contentAlignment = Alignment.Center
 //        ) {
 //            Image(
 //                painter = painterResource(id = R.drawable.logo_gold),
 //                contentDescription = stringResource(R.string.app_logo),
 //                modifier = Modifier.size(180.dp),
 //                contentScale = ContentScale.Fit
 //            )
 //        }
    }
    
    // Auto navigation
    LaunchedEffect(Unit) {
        viewModel.startSplashTimer()
    }
} 