# RankHeader 前三名设计实现

## 🎯 设计要求

根据设计图，CharmRankHeader 和 RichRankHeader 应该使用相同的布局结构：

### 📋 布局规格
- **布局方式**: Row 布局，显示前三名
- **背景图片**: rank_head_1.webp、rank_head_2.webp、rank_head_3.webp
- **镂空区域**: 用于显示用户/主播头像
- **下方文字**: 显示用户/主播名称
- **间距**: 左右 40.dp，item 之间 40.dp

### 🏆 排列顺序
```
┌─────────────────────────────────────────────────────────┐
│    [2nd]        [1st]        [3rd]                     │
│  rank_head_2  rank_head_1  rank_head_3                 │
│   (左侧)       (中间)       (右侧)                       │
└─────────────────────────────────────────────────────────┘
```

## 🔧 实现方案

### 1. **统一的头部布局**

```kotlin
// CharmRankHeader 和 RichRankHeader 使用相同结构
@Composable
private fun CharmRankHeader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 40.dp) // 左右间隔 40.dp
            .height(160.dp), // 适当高度容纳背景图片和文字
        horizontalArrangement = Arrangement.spacedBy(40.dp), // item 之间 40.dp
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 第二名 (左侧)
        RankHeaderItem(
            rank = 2,
            backgroundImage = R.mipmap.rank_head_2,
            userName = "User2",
            modifier = Modifier.weight(1f)
        )
        
        // 第一名 (中间，稍大)
        RankHeaderItem(
            rank = 1,
            backgroundImage = R.mipmap.rank_head_1,
            userName = "User1",
            modifier = Modifier.weight(1f),
            isFirst = true // 第一名特殊样式
        )
        
        // 第三名 (右侧)
        RankHeaderItem(
            rank = 3,
            backgroundImage = R.mipmap.rank_head_3,
            userName = "User3",
            modifier = Modifier.weight(1f)
        )
    }
}
```

### 2. **RankHeaderItem 组件**

```kotlin
@Composable
private fun RankHeaderItem(
    rank: Int,
    backgroundImage: Int,
    userName: String,
    modifier: Modifier = Modifier,
    isFirst: Boolean = false,
    userAvatar: String? = null
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 背景图片容器
        Box(
            modifier = Modifier
                .size(if (isFirst) 120.dp else 100.dp) // 第一名稍大
                .aspectRatio(1f),
            contentAlignment = Alignment.Center
        ) {
            // 背景图片 (rank_head_1/2/3.webp)
            ImageLoader.LoadImage(
                imageUrl = backgroundImage,
                modifier = Modifier.fillMaxSize(),
                contentDescription = "Rank $rank Background",
                contentScale = ContentScale.FillBounds,
                showLoading = false
            )
            
            // 镂空部分的用户头像
            Box(
                modifier = Modifier
                    .size(if (isFirst) 60.dp else 50.dp) // 头像大小
                    .background(
                        Color.Gray.copy(alpha = 0.3f), // 占位背景
                        RoundedCornerShape(50) // 圆形头像
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (userAvatar != null) {
                    // 加载真实头像
                    ImageLoader.LoadImage(
                        imageUrl = userAvatar,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(50)), // 圆形裁剪
                        contentDescription = "User Avatar",
                        contentScale = ContentScale.Crop,
                        showLoading = false
                    )
                } else {
                    // 占位图标
                    Text(
                        text = "👤",
                        fontSize = if (isFirst) 24.sp else 20.sp,
                        color = Color.White
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 用户名称
        Text(
            text = userName,
            color = Color.White,
            fontSize = if (isFirst) 16.sp else 14.sp,
            fontWeight = if (isFirst) FontWeight.Bold else FontWeight.Medium,
            maxLines = 1,
            textAlign = TextAlign.Center
        )
    }
}
```

## 🎨 视觉效果

### 布局结构：

```
┌─────────────────────────────────────────────────────────┐
│  40dp  ┌─────┐  40dp  ┌─────┐  40dp  ┌─────┐  40dp     │
│        │  2  │        │  1  │        │  3  │           │
│        │ 👤  │        │ 👤  │        │ 👤  │           │
│        │User2│        │User1│        │User3│           │
│        └─────┘        └─────┘        └─────┘           │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 尺寸对比：

#### **第一名 (中间)**:
- 背景图片: 120.dp × 120.dp
- 头像: 60.dp × 60.dp
- 文字: 16.sp, FontWeight.Bold

#### **第二名/第三名 (两侧)**:
- 背景图片: 100.dp × 100.dp
- 头像: 50.dp × 50.dp
- 文字: 14.sp, FontWeight.Medium

## 📊 设计对比

### 修改前 ❌ - 简单卡片样式

```kotlin
// 旧版本 - 简单的文字卡片
Card(
    colors = CardDefaults.cardColors(
        containerColor = Color(0xFF2D1B69)
    )
) {
    Column {
        Text("✨ CHARM RANKING ✨")
        Text("Most Charming Broadcasters")
    }
}
```

**问题：**
- ❌ 没有显示前三名用户
- ❌ 没有使用背景图片
- ❌ 布局不符合设计图
- ❌ 缺少用户头像展示

### 修改后 ✅ - 前三名展示

```kotlin
// 新版本 - 前三名展示布局
Row(
    modifier = Modifier.padding(horizontal = 40.dp),
    horizontalArrangement = Arrangement.spacedBy(40.dp)
) {
    // 第二名
    RankHeaderItem(
        rank = 2,
        backgroundImage = R.mipmap.rank_head_2,
        userName = "User2"
    )
    
    // 第一名 (中间，更大)
    RankHeaderItem(
        rank = 1,
        backgroundImage = R.mipmap.rank_head_1,
        userName = "User1",
        isFirst = true
    )
    
    // 第三名
    RankHeaderItem(
        rank = 3,
        backgroundImage = R.mipmap.rank_head_3,
        userName = "User3"
    )
}
```

**优势：**
- ✅ 显示前三名用户/主播
- ✅ 使用设计图指定的背景图片
- ✅ 镂空区域显示用户头像
- ✅ 符合设计图的布局和间距

## 🔧 技术细节

### 1. **背景图片处理**

```kotlin
// 背景图片填充整个容器
ImageLoader.LoadImage(
    imageUrl = backgroundImage, // R.mipmap.rank_head_1/2/3
    modifier = Modifier.fillMaxSize(),
    contentScale = ContentScale.FillBounds, // 填充边界
    showLoading = false // 不显示加载状态
)
```

### 2. **镂空头像实现**

```kotlin
// 镂空区域的头像容器
Box(
    modifier = Modifier
        .size(if (isFirst) 60.dp else 50.dp)
        .background(
            Color.Gray.copy(alpha = 0.3f), // 半透明占位背景
            RoundedCornerShape(50) // 圆形
        ),
    contentAlignment = Alignment.Center
) {
    // 真实头像或占位图标
    if (userAvatar != null) {
        ImageLoader.LoadImage(
            imageUrl = userAvatar,
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(50)), // 圆形裁剪
            contentScale = ContentScale.Crop
        )
    } else {
        Text(text = "👤") // 占位图标
    }
}
```

### 3. **响应式尺寸**

```kotlin
// 第一名使用更大的尺寸
.size(if (isFirst) 120.dp else 100.dp) // 背景图片
.size(if (isFirst) 60.dp else 50.dp)   // 头像
fontSize = if (isFirst) 16.sp else 14.sp // 文字
fontWeight = if (isFirst) FontWeight.Bold else FontWeight.Medium
```

### 4. **布局权重**

```kotlin
Row {
    RankHeaderItem(modifier = Modifier.weight(1f)) // 第二名
    RankHeaderItem(modifier = Modifier.weight(1f)) // 第一名
    RankHeaderItem(modifier = Modifier.weight(1f)) // 第三名
}
```

## ✅ 数据集成

### 1. **从 ViewModel 获取数据**

```kotlin
// 实际使用时应该从 ViewModel 获取前三名数据
@Composable
private fun CharmRankHeader(topThreeUsers: List<RankItem>) {
    Row(...) {
        // 第二名
        RankHeaderItem(
            rank = 2,
            backgroundImage = R.mipmap.rank_head_2,
            userName = topThreeUsers.getOrNull(1)?.userName ?: "Unknown",
            userAvatar = topThreeUsers.getOrNull(1)?.avatar
        )
        
        // 第一名
        RankHeaderItem(
            rank = 1,
            backgroundImage = R.mipmap.rank_head_1,
            userName = topThreeUsers.getOrNull(0)?.userName ?: "Unknown",
            userAvatar = topThreeUsers.getOrNull(0)?.avatar,
            isFirst = true
        )
        
        // 第三名
        RankHeaderItem(
            rank = 3,
            backgroundImage = R.mipmap.rank_head_3,
            userName = topThreeUsers.getOrNull(2)?.userName ?: "Unknown",
            userAvatar = topThreeUsers.getOrNull(2)?.avatar
        )
    }
}
```

### 2. **空数据处理**

```kotlin
// 当没有足够数据时的占位显示
userName = topThreeUsers.getOrNull(index)?.userName ?: "No Data"
userAvatar = topThreeUsers.getOrNull(index)?.avatar // null 时显示占位图标
```

## 🚀 扩展性

### 1. **可配置的背景图片**

```kotlin
// 可以根据不同排行榜类型使用不同的背景图片
val backgroundImages = when (rankType) {
    RankTabType.CHARM -> listOf(R.mipmap.charm_head_1, R.mipmap.charm_head_2, R.mipmap.charm_head_3)
    RankTabType.RICK -> listOf(R.mipmap.rich_head_1, R.mipmap.rich_head_2, R.mipmap.rich_head_3)
    else -> listOf(R.mipmap.rank_head_1, R.mipmap.rank_head_2, R.mipmap.rank_head_3)
}
```

### 2. **动画效果**

```kotlin
// 可以添加头像加载动画
AnimatedVisibility(
    visible = userAvatar != null,
    enter = fadeIn() + scaleIn(),
    exit = fadeOut() + scaleOut()
) {
    ImageLoader.LoadImage(imageUrl = userAvatar)
}
```

## 🎉 总结

**RankHeader 前三名设计实现完成！**

### 核心特性：
1. ✅ **统一布局** - CharmRankHeader 和 RichRankHeader 使用相同结构
2. ✅ **背景图片** - 使用 rank_head_1/2/3.webp 作为背景
3. ✅ **镂空头像** - 在背景图片中央显示用户头像
4. ✅ **响应式设计** - 第一名使用更大的尺寸突出显示

### 视觉效果：
- 🏆 **突出第一名** - 中间位置，更大尺寸，粗体文字
- 🎨 **设计一致** - 完全符合设计图的布局和间距
- 📱 **用户友好** - 清晰的排名展示和用户信息
- ✨ **细节完善** - 圆形头像、占位图标、文字样式

现在 CharmRankHeader 和 RichRankHeader 都使用统一的前三名展示布局，完全符合设计图要求！
