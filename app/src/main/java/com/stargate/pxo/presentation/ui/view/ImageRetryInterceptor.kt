package com.stargate.pxo.presentation.ui.view

import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

/**
 * 图片加载重试拦截器
 * 在网络请求失败时自动重试
 */
class ImageRetryInterceptor(
    private val maxRetries: Int = 3,
    private val retryDelayMs: Long = 1000L
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        var response: Response? = null
        var lastException: IOException? = null
        
        repeat(maxRetries + 1) { attempt ->
            try {
                // 关闭之前的响应
                response?.close()
                
                // 执行请求
                response = chain.proceed(request)
                
                // 如果响应成功，直接返回
                if (response!!.isSuccessful) {
                    if (attempt > 0) {
                        android.util.Log.d("ImageRetry", "Image loaded successfully after $attempt retries")
                    }
                    return response!!
                }
                
                // 如果是客户端错误（4xx），不重试
                if (response!!.code in 400..499) {
                    android.util.Log.w("ImageRetry", "Client error ${response!!.code}, not retrying")
                    return response!!
                }
                
                // 服务器错误（5xx），记录并准备重试
                android.util.Log.w("ImageRetry", "Server error ${response!!.code}, attempt ${attempt + 1}/${maxRetries + 1}")
                
            } catch (e: IOException) {
                lastException = e
                android.util.Log.w("ImageRetry", "Network error on attempt ${attempt + 1}/${maxRetries + 1}: ${e.message}")
            }
            
            // 如果不是最后一次尝试，等待后重试
            if (attempt < maxRetries) {
                val delayMs = retryDelayMs * (attempt + 1) // 递增延迟：1s, 2s, 3s...
                try {
                    android.util.Log.d("ImageRetry", "Waiting ${delayMs}ms before retry...")
                    Thread.sleep(delayMs)
                } catch (e: InterruptedException) {
                    Thread.currentThread().interrupt()
                    android.util.Log.w("ImageRetry", "Retry interrupted")
                }
            }
        }
        
        // 如果所有重试都失败了
        android.util.Log.e("ImageRetry", "All retry attempts failed")
        return response ?: throw (lastException ?: IOException("Unknown error after $maxRetries retries"))
    }
    
    /**
     * 判断是否应该重试
     */
    private fun shouldRetry(response: Response?, exception: IOException?): Boolean {
        return when {
            // 网络异常，应该重试
            exception != null -> true
            
            // 响应为空，应该重试
            response == null -> true
            
            // 服务器错误（5xx），应该重试
            response.code in 500..599 -> true
            
            // 请求超时（408）或限流（429），应该重试
            response.code == 408 || response.code == 429 -> true
            
            // 其他情况不重试
            else -> false
        }
    }
}
