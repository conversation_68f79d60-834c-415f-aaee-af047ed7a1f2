package com.stargate.pxo.presentation.ui.navigation

import android.annotation.SuppressLint
import androidx.navigation.NavHostController
import com.stargate.pxo.common.util.log.LogUtil

/**
 * 导航状态数据类
 */
data class NavigationState(
    val currentRoute: String? = null,
    val previousRoute: String? = null,
    val canNavigateUp: Boolean = false,
    val isLoggedIn: Boolean = false,
    val canPopBack: Boolean = false
)

/**
 * 全局导航管理器
 * 提供统一的导航操作，支持以 MainScreen 为根栈的导航逻辑
 */
object NavigationManager {
    
    @SuppressLint("StaticFieldLeak")
    private var globalNavController: NavHostController? = null
    private var isLoggedIn: Boolean = false
    
    /**
     * 初始化导航控制器
     */
    fun initialize(navController: NavHostController) {
        globalNavController = navController
        LogUtil.d("NavigationManager", "导航控制器已初始化")
    }
    
    /**
     * 设置登录状态
     */
    fun setLoginStatus(loggedIn: Boolean) {
        isLoggedIn = loggedIn
        LogUtil.d("NavigationManager", "登录状态更新: $loggedIn")
    }
    
    /**
     * 获取当前导航控制器
     */
    fun getNavController(): NavHostController? = globalNavController
    
    /**
     * 智能返回 - 根据登录状态决定返回行为
     * 如果已登录，最多返回到 MainScreen
     * 如果未登录，正常返回
     */
    fun smartPopBack(): Boolean {
        val navController = globalNavController ?: return false
        
        return try {
            val currentDestination = navController.currentDestination?.route
            LogUtil.d("NavigationManager", "当前页面: $currentDestination")
            
            when {
                // 如果已经在 MainScreen，不再返回
                currentDestination == "main" && isLoggedIn -> {
                    LogUtil.d("NavigationManager", "已在主页，不执行返回")
                    false
                }
                
                // 如果未登录且在 LoginScreen，不再返回
                currentDestination == "login" && !isLoggedIn -> {
                    LogUtil.d("NavigationManager", "已在登录页，不执行返回")
                    false
                }
                
                // 如果在 SplashScreen，不执行返回
                currentDestination == "splash" -> {
                    LogUtil.d("NavigationManager", "在启动页，不执行返回")
                    false
                }
                
                // 其他情况正常返回
                else -> {
                    val canPop = navController.popBackStack()
                    LogUtil.d("NavigationManager", "执行返回操作: $canPop")
                    
                    // 如果已登录，检查返回后是否需要限制在 MainScreen
                    if (isLoggedIn && canPop) {
                        val newDestination = navController.currentDestination?.route
                        LogUtil.d("NavigationManager", "返回后页面: $newDestination")
                        
                        // 如果返回到了登录前的页面，强制跳转到 MainScreen
                        if (newDestination in listOf("splash", "login")) {
                            LogUtil.d("NavigationManager", "返回到登录前页面，重定向到主页")
                            navigateToMain(clearStack = true)
                        }
                    }
                    
                    canPop
                }
            }
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "返回操作失败", e)
            false
        }
    }
    
    /**
     * 强制返回到指定页面
     */
    fun popBackTo(route: String, inclusive: Boolean = false): Boolean {
        val navController = globalNavController ?: return false
        
        return try {
            val result = navController.popBackStack(route, inclusive)
            LogUtil.d("NavigationManager", "返回到页面 $route: $result")
            result
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "返回到指定页面失败: $route", e)
            false
        }
    }
    
    /**
     * 导航到主页
     */
    fun navigateToMain(clearStack: Boolean = false) {
        val navController = globalNavController ?: return
        
        try {
            if (clearStack) {
                // 清空栈并导航到主页
                navController.navigate("main") {
                    popUpTo(navController.graph.startDestinationId) {
                        inclusive = true
                    }
                    launchSingleTop = true
                }
                LogUtil.d("NavigationManager", "清空栈并导航到主页")
            } else {
                navController.navigate("main")
                LogUtil.d("NavigationManager", "导航到主页")
            }
            setLoginStatus(true)
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "导航到主页失败", e)
        }
    }
    
    /**
     * 导航到登录页
     */
    fun navigateToLogin(clearStack: Boolean = false) {
        val navController = globalNavController ?: return
        
        try {
            if (clearStack) {
                navController.navigate("login") {
                    popUpTo(navController.graph.startDestinationId) {
                        inclusive = true
                    }
                    launchSingleTop = true
                }
                LogUtil.d("NavigationManager", "清空栈并导航到登录页")
            } else {
                navController.navigate("login")
                LogUtil.d("NavigationManager", "导航到登录页")
            }
            setLoginStatus(false)
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "导航到登录页失败", e)
        }
    }
    
    /**
     * 通用导航方法
     */
    fun navigate(route: String, clearStack: Boolean = false, popUpToRoute: String? = null) {
        val navController = globalNavController ?: return
        
        try {
            navController.navigate(route) {
                if (clearStack) {
                    popUpTo(navController.graph.startDestinationId) {
                        inclusive = true
                    }
                } else if (popUpToRoute != null) {
                    popUpTo(popUpToRoute) {
                        inclusive = false
                    }
                }
                launchSingleTop = true
            }
            LogUtil.d("NavigationManager", "导航到: $route")
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "导航失败: $route", e)
        }
    }
    
    /**
     * 获取当前路由
     */
    fun getCurrentRoute(): String? {
        return globalNavController?.currentDestination?.route
    }
    
    /**
     * 检查是否可以返回
     */
    fun canPopBack(): Boolean {
        val navController = globalNavController ?: return false
        val currentRoute = getCurrentRoute()
        
        return when {
            // 如果已登录且在主页，不能返回
            currentRoute == "main" && isLoggedIn -> false
            
            // 如果未登录且在登录页，不能返回
            currentRoute == "login" && !isLoggedIn -> false
            
            // 如果在启动页，不能返回
            currentRoute == "splash" -> false
            
            // 其他情况检查导航栈
            else -> navController.previousBackStackEntry != null
        }
    }
    
    /**
     * 退出应用（清除所有状态）
     */
    fun exitApp() {
        try {
            setLoginStatus(false)
            globalNavController = null
            LogUtil.d("NavigationManager", "应用退出，清除导航状态")
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "退出应用失败", e)
        }
    }
    
    /**
     * 获取导航栈信息（调试用）
     */
    fun getBackStackInfo(): String {
        val navController = globalNavController ?: return "NavController is null"

        return try {
            val currentDestination = navController.currentDestination?.route ?: "unknown"
            val previousDestination = navController.previousBackStackEntry?.destination?.route ?: "none"
            val canNavigateUp = navController.previousBackStackEntry != null
            "Current: $currentDestination | Previous: $previousDestination | CanGoBack: $canNavigateUp"
        } catch (e: Exception) {
            "Error getting backstack: ${e.message}"
        }
    }

    /**
     * 获取详细的导航状态信息（调试用）
     */
    fun getNavigationState(): NavigationState {
        val navController = globalNavController ?: return NavigationState()

        return try {
            NavigationState(
                currentRoute = navController.currentDestination?.route,
                previousRoute = navController.previousBackStackEntry?.destination?.route,
                canNavigateUp = navController.previousBackStackEntry != null,
                isLoggedIn = isLoggedIn,
                canPopBack = canPopBack()
            )
        } catch (e: Exception) {
            LogUtil.e("NavigationManager", "获取导航状态失败", e)
            NavigationState()
        }
    }
}

/**
 * Compose 扩展函数，方便在 Composable 中使用
 */
@androidx.compose.runtime.Composable
fun rememberNavigationManager(): NavigationManager {
    return NavigationManager
}
