package com.stargate.pxo.presentation.ui.screens.profile

import android.net.Uri
import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.FileUploadDownloadUtil
import com.stargate.pxo.common.util.UriUtils
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.model.OssPolicyResult
import com.stargate.pxo.data.network.model.UserInfo
import com.stargate.pxo.data.repository.UserInfoRepository
import com.stargate.pxo.presentation.ui.view.GlobalLoadingManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject



// 定义个人资料的状态
data class ProfileUiState(
    val invitationCode: String = "",
    val isLoading: Boolean = false,
    val error: String? = null,
    val userInfo: UserInfo? = null,
    val avatarUploadState: AvatarUploadState = AvatarUploadState.Idle,
    val submitSuccess: Boolean = false
) : UiState

/**
 * 头像上传状态
 */
sealed class AvatarUploadState {
    object Idle : AvatarUploadState()
    object GettingOssPolicy : AvatarUploadState()
    data class Uploading(val progress: Int) : AvatarUploadState()
    object UpdatingAvatar : AvatarUploadState()
    object Success : AvatarUploadState()
    data class Error(val message: String) : AvatarUploadState()
}

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val userInfoRepository: UserInfoRepository,
    private val fileUploadDownloadUtil: FileUploadDownloadUtil
) : BaseViewModel<ProfileUiState>() {

    // 用户信息StateFlow
    private val userStateFlow: StateFlow<UserInfo?> = UserManager.currentUser

    override fun createInitialState(): ProfileUiState = ProfileUiState()



    init {
        // 监听用户信息变化并更新UI状态
        viewModelScope.launch {
            userStateFlow.collectLatest { userInfo ->
                updateState {
                    this.copy(userInfo = userInfo)
                }
            }
        }
        
        // 监听头像上传状态变化，更新加载动画
        viewModelScope.launch {
            uiState.collectLatest { state ->
                when (state.avatarUploadState) {
                    is AvatarUploadState.Idle -> {
                        // 空闲状态，不显示加载动画
                        GlobalLoadingManager.hide()
                    }
                    is AvatarUploadState.GettingOssPolicy -> {
                        // 获取OSS策略中，显示加载动画
                        GlobalLoadingManager.show()
                    }
                    is AvatarUploadState.Uploading -> {
                        // 上传中，显示加载动画
                        GlobalLoadingManager.show()
                    }
                    is AvatarUploadState.UpdatingAvatar -> {
                        // 更新头像中，显示加载动画
                        GlobalLoadingManager.show()
                    }
                    is AvatarUploadState.Success -> {
                        // 上传成功，隐藏加载动画
                        GlobalLoadingManager.hide()
                    }
                    is AvatarUploadState.Error -> {
                        // 上传失败，隐藏加载动画
                        GlobalLoadingManager.hide()
                    }
                }
            }
        }
    }

    fun submitProfile(name: String, age: String, gender: String, invitationCode: String) {
        // 验证名字和年龄不能为空
        if (name.isBlank() || age.isBlank()) {
            updateState {
                this.copy(error = "姓名和生日不能为空")
            }
            return
        }
        
        // 验证年龄是否大于18岁
        try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val birthDate = dateFormat.parse(age)
            if (birthDate != null) {
                val ageYears = calculateAge(birthDate)
                if (ageYears < 18) {
                    updateState {
                        this.copy(error = "年龄必须大于18岁")
                    }
                    return
                }
            }
        } catch (e: Exception) {
            updateState {
                this.copy(error = "生日格式不正确")
            }
            return
        }
        
        // 更新本地状态
        updateState {
            this.copy(invitationCode = invitationCode, isLoading = true, error = null)
        }
        
        // 显示全局加载动画
        GlobalLoadingManager.show()

        // 准备提交的数据
        try {
            val genderInt = gender.toIntOrNull() ?: 1
            
            // 准备提交到服务器的数据
            val submitData = mapOf(
                "nickname" to name,
                "gender" to genderInt,
                "birthday" to age,
                "invitationCode" to invitationCode
            )
            
            // 使用 UserInfoRepository 保存用户信息到服务器
            PuxxiCoroutine.io {
                try {
                    userInfoRepository.saveUserInfo(data = submitData)
                        .collect { resource ->
                            when (resource) {
                                is Resource.Success -> {
                                    LogUtil.d("ProfileViewModel", "用户信息保存成功")
                                    refreshUserInfo()
                                    updateState { 
                                        copy(isLoading = false, error = null, submitSuccess = true) 
                                    }
                                    GlobalLoadingManager.hide()
                                }
                                is Resource.Error -> {
                                    LogUtil.e("ProfileViewModel", "用户信息保存失败: ${resource.message}")
                                    updateState { 
                                        copy(error = resource.message, isLoading = false) 
                                    }
                                    GlobalLoadingManager.hide()
                                }
                                is Resource.Loading -> {
                                    GlobalLoadingManager.show()
                                }
                            }
                        }
                } catch (e: Exception) {
                    LogUtil.e("ProfileViewModel", "提交用户信息异常", e)
                    updateState {
                        copy(error = "提交信息失败: ${e.message}", isLoading = false)
                    }
                    GlobalLoadingManager.hide()
                }
            }
            
        } catch (e: Exception) {
            // 处理数据准备阶段的错误
            updateState {
                this.copy(error = "数据准备失败: ${e.message}", isLoading = false)
            }
            GlobalLoadingManager.hide()
        }
    }
    
    /**
     * 根据生日计算年龄
     */
    private fun calculateAge(birthDate: Date): Int {
        val today = Calendar.getInstance()
        val birthDay = Calendar.getInstance()
        birthDay.time = birthDate
        
        var age = today.get(Calendar.YEAR) - birthDay.get(Calendar.YEAR)
        
        // 如果今年的生日还没到，年龄减1
        if (today.get(Calendar.MONTH) < birthDay.get(Calendar.MONTH) ||
            (today.get(Calendar.MONTH) == birthDay.get(Calendar.MONTH) && 
             today.get(Calendar.DAY_OF_MONTH) < birthDay.get(Calendar.DAY_OF_MONTH))) {
            age--
        }
        
        return if (age < 0) 0 else age
    }
    
    /**
     * 更新用户头像
     * 完整流程：获取OSS策略 -> 上传文件 -> 调用更新头像接口 -> 更新本地用户信息
     */
    fun updateAvatar(avatarUri: Uri) {
        updateState {
            copy(
                isLoading = true,
                error = null,
                avatarUploadState = AvatarUploadState.GettingOssPolicy
            )
        }
        
        // 显示全局加载动画
        GlobalLoadingManager.show()

        PuxxiCoroutine.io {
            try {
                // 步骤1: 获取OSS上传策略
                LogUtil.d("ProfileViewModel", "开始获取OSS上传策略")
                var ossPolicy: OssPolicyResult? = null

                userInfoRepository.getUserOssPolicyPostV2()
                    .collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                LogUtil.d("ProfileViewModel", "OSS策略获取成功")
                                ossPolicy = resource.data
                                return@collect // 成功后停止收集
                            }
                            is Resource.Error -> {
                                LogUtil.e("ProfileViewModel", "OSS策略获取失败: ${resource.message}")
                                updateState {
                                    copy(
                                        error = "获取上传权限失败: ${resource.message}",
                                        isLoading = false,
                                        avatarUploadState = AvatarUploadState.Error(resource.message)
                                    )
                                }
                                // 隐藏全局加载动画
                                GlobalLoadingManager.hide()
                                return@collect // 失败后停止收集
                            }
                            is Resource.Loading -> {
                                LogUtil.d("ProfileViewModel", "正在获取OSS策略...")
                                // 继续等待
                            }
                        }
                    }

                if (ossPolicy == null) {
                    LogUtil.e("ProfileViewModel", "OSS策略获取失败")
                    updateState {
                        copy(
                            error = "获取上传权限失败",
                            isLoading = false,
                            avatarUploadState = AvatarUploadState.Error("获取上传权限失败")
                        )
                    }
                    // 隐藏全局加载动画
                    GlobalLoadingManager.hide()
                    return@io
                }

                LogUtil.d("ProfileViewModel", "OSS策略获取成功: ${ossPolicy.host}")

                // 步骤2: 上传文件到OSS
                LogUtil.d("ProfileViewModel", "开始上传头像文件")
                updateState { copy(avatarUploadState = AvatarUploadState.Uploading(0)) }

                var uploadResult: FileUploadDownloadUtil.UploadState.Success? = null
                var isUploadCompleted = false

                try {
                    // 上传文件并收集状态
                fileUploadDownloadUtil.uploadFile(
                    uri = avatarUri,
                    ossPolicyResult = ossPolicy,
                    headers = mapOf("Content-Type" to "image/jpeg"),
                    fieldName = "file"
                ).collect { uploadState ->
                    when (uploadState) {
                        is FileUploadDownloadUtil.UploadState.Success -> {
                            uploadResult = uploadState
                                isUploadCompleted = true
                            LogUtil.d("ProfileViewModel", "头像文件上传成功: ${uploadState.result.filename}")
                        }
                        is FileUploadDownloadUtil.UploadState.Progress -> {
                            LogUtil.d("ProfileViewModel", "头像上传进度: ${uploadState.progress}%")
                            updateState {
                                copy(avatarUploadState = AvatarUploadState.Uploading(uploadState.progress))
                            }
                        }
                        is FileUploadDownloadUtil.UploadState.Error -> {
                            LogUtil.e("ProfileViewModel", "头像文件上传失败: ${uploadState.message}")
                                isUploadCompleted = true
                            updateState {
                                copy(
                                    error = "头像上传失败: ${uploadState.message}",
                                    isLoading = false,
                                    avatarUploadState = AvatarUploadState.Error(uploadState.message)
                                )
                            }
                                // 隐藏全局加载动画
                                GlobalLoadingManager.hide()
                            }
                        }
                    }
                } catch (e: Exception) {
                    LogUtil.e("ProfileViewModel", "上传过程中发生异常", e)
                    updateState {
                        copy(
                            error = "上传过程中发生异常: ${e.message}",
                            isLoading = false,
                            avatarUploadState = AvatarUploadState.Error(e.message ?: "未知错误")
                        )
                    }
                    // 隐藏全局加载动画
                    GlobalLoadingManager.hide()
                    return@io
                }

                // 添加调试日志，确认流程到达了这一步
                LogUtil.d("ProfileViewModel", "文件上传流程结束，uploadResult是否为空: ${uploadResult == null}, isUploadCompleted: $isUploadCompleted")

                // 如果上传没有完成，直接返回
                if (!isUploadCompleted) {
                    LogUtil.e("ProfileViewModel", "上传流程未正常完成")
                    updateState {
                        copy(
                            error = "上传未完成",
                            isLoading = false,
                            avatarUploadState = AvatarUploadState.Error("上传未完成")
                        )
                    }
                    // 隐藏全局加载动画
                    GlobalLoadingManager.hide()
                    return@io
                }

                // 步骤3: 调用更新头像接口
                val xFileUploadResult = uploadResult?.result
                if (xFileUploadResult != null) {
                    // 检查文件名是否有效
                    if (xFileUploadResult.filename.isNullOrEmpty()) {
                        LogUtil.e("ProfileViewModel", "文件名为空，无法更新头像")
                        updateState {
                            copy(
                                error = "上传失败：无效的文件名",
                                isLoading = false,
                                avatarUploadState = AvatarUploadState.Error("上传失败：无效的文件名")
                            )
                        }
                        // 隐藏全局加载动画
                        GlobalLoadingManager.hide()
                        return@io
                    }
                    
                    LogUtil.d("ProfileViewModel", "开始调用更新头像接口，文件名: ${xFileUploadResult.filename}")
                    updateState { copy(avatarUploadState = AvatarUploadState.UpdatingAvatar) }

                    val updateData = mapOf("avatarPath" to xFileUploadResult.filename)

                    userInfoRepository.userUpdateAvatar(data = updateData)
                        .collect { resource ->
                            when (resource) {
                                is Resource.Success -> {
                                    LogUtil.d("ProfileViewModel", "头像更新接口调用成功")
                                    val updateAvatarResult = resource.data

                                    // 步骤4: 更新本地用户信息，使用mediaUrl更新avatar
                                    UserManager.updateUserInfo { currentUser ->
                                        currentUser.copy(
                                            avatar = updateAvatarResult.mediaUrl,
                                            avatarUrl = updateAvatarResult.mediaUrl
                                        )
                                    }

                                    LogUtil.d("ProfileViewModel", "本地用户头像更新成功: ${updateAvatarResult.mediaUrl}")
                                    updateState {
                                        copy(
                                            isLoading = false,
                                            error = null,
                                            avatarUploadState = AvatarUploadState.Success
                                        )
                                    }
                                    // 隐藏全局加载动画
                                    GlobalLoadingManager.hide()
                                }
                                is Resource.Error -> {
                                    LogUtil.e("ProfileViewModel", "头像更新接口调用失败: ${resource.message}")
                                    updateState {
                                        copy(
                                            error = "头像更新失败: ${resource.message}",
                                            isLoading = false,
                                            avatarUploadState = AvatarUploadState.Error(resource.message)
                                        )
                                    }
                                    // 隐藏全局加载动画
                                    GlobalLoadingManager.hide()
                                }
                                is Resource.Loading -> {
                                    LogUtil.d("ProfileViewModel", "正在调用头像更新接口...")
                                }
                            }
                        }
                } else {
                    LogUtil.e("ProfileViewModel", "文件上传结果为空")
                    updateState {
                        copy(
                            error = "文件上传失败",
                            isLoading = false,
                            avatarUploadState = AvatarUploadState.Error("文件上传失败")
                        )
                    }
                    // 隐藏全局加载动画
                    GlobalLoadingManager.hide()
                }

            } catch (e: Exception) {
                LogUtil.e("ProfileViewModel", "更新头像过程中发生异常", e)
                updateState {
                    copy(
                        error = "更新头像失败: ${e.message}",
                        isLoading = false,
                        avatarUploadState = AvatarUploadState.Error(e.message ?: "未知错误")
                    )
                }
                // 隐藏全局加载动画
                GlobalLoadingManager.hide()
            }
        }
    }

    /**
     * 重置头像上传状态
     */
    fun resetAvatarUploadState() {
        updateState { copy(avatarUploadState = AvatarUploadState.Idle) }
    }

    /**
     * 重置提交成功状态
     */
    fun resetSubmitSuccess() {
        updateState { copy(submitSuccess = false) }
    }

    /**
     * 刷新用户信息
     * 从服务器获取最新的用户信息并更新本地数据
     */
    fun refreshUserInfo() {
        viewModelScope.launch {
            try {
                UserManager.refreshUserInfo(userInfoRepository) { success ->
                    // 记录日志
                    if (success) {
                        LogUtil.d("ProfileViewModel", "用户信息刷新成功")
                    } else {
                        LogUtil.e("ProfileViewModel", "用户信息刷新失败")
                    }
                }
            } catch (e: Exception) {
                // 处理异常
                LogUtil.e("ProfileViewModel", "刷新用户信息过程中发生异常", e)
            }
        }
    }
}