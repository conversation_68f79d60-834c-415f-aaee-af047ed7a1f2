package com.stargate.pxo.presentation.ui.view

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay

/**
 * 全局 Loading 管理器
 * 提供显示和隐藏 Loading 的方法
 */
object GlobalLoadingManager {
    
    private var isShowing by mutableStateOf(false)
    private var autoHideJob: Job? = null
    
    /**
     * 显示 Loading
     */
    fun show() {
        isShowing = true
        cancelAutoHide()
    }
    
    /**
     * 显示 Loading 并在指定时间后自动隐藏
     * @param autoHideDelayMs 自动隐藏延迟时间（毫秒），默认15秒
     */
    fun showWithAutoHide(autoHideDelayMs: Long = 15000L) {
        isShowing = true
        startAutoHide(autoHideDelayMs)
    }
    
    /**
     * 隐藏 Loading
     */
    fun hide() {
        isShowing = false
        cancelAutoHide()
    }
    
    /**
     * 获取当前显示状态
     */
    fun isVisible(): Boolean = isShowing
    
    /**
     * 切换显示状态
     */
    fun toggle() {
        if (isShowing) {
            hide()
        } else {
            show()
        }
    }
    
    /**
     * 开始自动隐藏倒计时
     */
    private fun startAutoHide(delayMs: Long) {
        cancelAutoHide()
        autoHideJob = PuxxiCoroutine.background(tag = "AutoHideLoading") {
            delay(delayMs)
            if (isShowing) {
                PuxxiCoroutine.withMain {
                    isShowing = false
                }
            }
        }
    }
    
    /**
     * 取消自动隐藏
     */
    private fun cancelAutoHide() {
        autoHideJob?.cancel()
        autoHideJob = null
    }
}

/**
 * 全局 Loading View Composable
 * 需要在主 Activity 或根 Composable 中调用
 */
@Composable
fun GlobalLoadingView() {
    val isVisible = GlobalLoadingManager.isVisible()

    if (isVisible) {
        Popup(
            alignment = Alignment.Center,
            properties = PopupProperties(
                focusable = true,
                dismissOnBackPress = true,
                dismissOnClickOutside = false
            ),
            onDismissRequest = {
                GlobalLoadingManager.hide()
            }
        ) {
            LoadingContent()
        }
    }
}

/**
 * Loading 内容 Composable
 */
@Composable
private fun LoadingContent() {
    AnimatedVisibility(
        visible = true,
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(12.dp))
                .background(Color.Black.copy(alpha = 0.4f)),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(32.dp),
                color = Color.White,
                strokeWidth = 2.5.dp
            )
        }
    }
}

/**
 * 可定制的 Loading View
 * 支持自定义大小、颜色等参数
 */
@Composable
fun CustomLoadingView(
    isVisible: Boolean,
    boxSize: Int = 50,
    cornerRadius: Int = 25,
    backgroundColor: Color = Color.White.copy(alpha = 0.3f),
    progressSize: Int = 25,
    progressColor: Color = Color.White,
    strokeWidth: Int = 2,
    autoHideDelayMs: Long? = null,
    onDismiss: (() -> Unit)? = null
) {
    if (isVisible) {
        // 自动隐藏逻辑
        autoHideDelayMs?.let { delay ->
            LaunchedEffect(isVisible) {
                delay(delay)
                onDismiss?.invoke()
            }
        }
        
        Popup(
            alignment = Alignment.Center,
            properties = PopupProperties(
                focusable = true,
                dismissOnBackPress = false,
                dismissOnClickOutside = false
            )
        ) {
            AnimatedVisibility(
                visible = true,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .size(boxSize.dp)
                        .clip(RoundedCornerShape(cornerRadius.dp))
                        .background(backgroundColor),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(progressSize.dp),
                        color = progressColor,
                        strokeWidth = strokeWidth.dp
                    )
                }
            }
        }
    }
}