package com.stargate.pxo.presentation.ui.screens.main

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.stargate.pxo.R
import com.stargate.pxo.presentation.ui.screens.main.home.HomeScreen
import com.stargate.pxo.presentation.ui.screens.main.profile.ProfileScreen
import com.stargate.pxo.presentation.ui.theme.noRippleClickable
import com.stargate.pxo.presentation.ui.theme.noRippleClickableFast
import com.stargate.pxo.presentation.ui.view.GlobalLoadingView

/**
 * 主页面的Screen
 */
@Composable
fun MainScreen(
    viewModel: MainViewModel = hiltViewModel(),
    globalNavController: NavHostController? = null
) {
    // 创建底部导航的 NavController
    val bottomNavController = rememberNavController()
    Scaffold(
        bottomBar = { BottomNavigationBar(bottomNavController) },
        modifier = Modifier.windowInsetsPadding(WindowInsets.navigationBars)
    ) { innerPadding ->
        NavHost(
            navController = bottomNavController,
            startDestination = BottomNavItem.Home.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(BottomNavItem.Home.route) {
                HomeScreen(globalNavController = globalNavController)
            }
            composable(BottomNavItem.Message.route) {
                MessageScreen()
            }
            composable(BottomNavItem.Discover.route) {
                DiscoverScreen()
            }
            composable(BottomNavItem.Profile.route) {
                ProfileScreen()
            }
        }
    }
}

/**
 * 底部导航栏
 */
@Composable
fun BottomNavigationBar(navController: NavHostController) {
    val items = listOf(
        BottomNavItem.Home,
        BottomNavItem.Message,
        BottomNavItem.Discover,
        BottomNavItem.Profile
    )
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(140.dp)
            .background(Color(0xff4A3452))
    ) {
        val navBackStackEntry by navController.currentBackStackEntryAsState()
        val currentDestination = navBackStackEntry?.destination
        
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            items.forEach { item ->
                val selected = currentDestination?.hierarchy?.any { it.route == item.route } == true
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .weight(1f)
                        .noRippleClickableFast(onClick = {
                            navController.navigate(item.route) {
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                launchSingleTop = true
                                restoreState = true
                            }
                        })
                ) {
                    Icon(
                        painter = painterResource(id = if (selected) item.selectedIcon else item.icon),
                        contentDescription = stringResource(id = item.titleResId),
                        modifier = Modifier.size(90.dp),
                        tint = if (selected) Color(0xFFE5C98B) else Color.White
                    )
                    /*Text(
                        text = stringResource(id = item.titleResId),
                        fontSize = 12.sp,
                        fontWeight = if (selected) FontWeight.Bold else FontWeight.Normal,
                        color = if (selected) Color(0xFFE5C98B) else Color.White
                    )*/
                }
            }
        }
    }
}

/**
 * 底部导航项
 */
sealed class BottomNavItem(
    val route: String,
    val icon: Int,
    val selectedIcon: Int,
    val titleResId: Int
) {
    object Home : BottomNavItem(
        route = "home",
        icon = R.mipmap.icon_anchor_wall_n,
        selectedIcon = R.mipmap.icon_anchor_wall_s,
        titleResId = R.string.home
    )
    
    object Message : BottomNavItem(
        route = "message",
        icon = R.mipmap.icon_message_n,
        selectedIcon = R.mipmap.icon_message_s,
        titleResId = R.string.message
    )
    
    object Discover : BottomNavItem(
        route = "discover",
        icon = R.mipmap.icon_match_n,
        selectedIcon = R.mipmap.icon_match_s,
        titleResId = R.string.discover
    )
    
    object Profile : BottomNavItem(
        route = "profile",
        icon = R.mipmap.icon_me_n,
        selectedIcon = R.mipmap.icon_me_s,
        titleResId = R.string.profile
    )
}

/**
 * 消息页面内容
 */
@Composable
fun MessageScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        Text(
            text = "Message Screen",
            color = MaterialTheme.colorScheme.onBackground,
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

/**
 * 发现页面内容
 */
@Composable
fun DiscoverScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.surface)
                    .padding(vertical = 8.dp)
            ) {
                // 分类导航
                val categories = listOf("All", "Beauty", "Alien", "Curve", "Furniture")

                // 这里应该是一个滚动的分类列表，简化实现
                Text(
                    text = "Categories: ${categories.joinToString(", ")}",
                    color = MaterialTheme.colorScheme.onSurface,
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.align(Alignment.Center)
                )
            }

            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(MaterialTheme.colorScheme.background)
            ) {
                Text(
                    text = "Discover Content",
                    color = MaterialTheme.colorScheme.onBackground,
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

 