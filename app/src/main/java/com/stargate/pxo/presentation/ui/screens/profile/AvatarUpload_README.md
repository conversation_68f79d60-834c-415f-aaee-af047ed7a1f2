# 头像上传功能实现

## 功能概述

Puxxi应用的头像上传功能提供了完整的用户头像管理解决方案，支持图片选择、压缩优化、云端上传、本地缓存等核心功能。采用阿里云OSS作为云存储服务，确保图片的安全性和访问速度。

### 核心特性
- 🖼️ **多种图片来源**: 支持相机拍摄和相册选择
- 🔧 **智能压缩**: 自动压缩优化，节省存储和流量
- ☁️ **云端存储**: 基于阿里云OSS的可靠存储服务
- 📊 **实时进度**: 上传进度实时显示和状态反馈
- 🔄 **断点续传**: 支持大文件断点续传功能
- 🛡️ **安全可靠**: 完善的错误处理和重试机制

### 完整上传流程
1. **图片选择** → 相机拍摄或相册选择图片
2. **图片处理** → 压缩、裁剪、格式优化
3. **获取上传策略** → 调用 `getUserOssPolicyPostV2` 接口获取OSS临时凭证
4. **文件上传** → 使用 `FileUploadDownloadUtil.uploadFile` 上传到OSS
5. **更新头像信息** → 调用 `userUpdateAvatar` 接口，参数为 `mapOf("avatarPath" to filename)`
6. **本地数据同步** → 使用 `mediaUrl` 更新本地 UserInfo 的 avatar 字段
7. **UI状态更新** → 更新界面显示新头像

## 核心组件

### 1. ProfileViewModel 更新

#### 新增状态管理
```kotlin
data class ProfileUiState(
    val invitationCode: String = "",
    val isLoading: Boolean = false,
    val error: String? = null,
    val userInfo: UserInfo? = null,
    val avatarUploadState: AvatarUploadState = AvatarUploadState.Idle
) : UiState

sealed class AvatarUploadState {
    object Idle : AvatarUploadState()
    object GettingOssPolicy : AvatarUploadState()
    data class Uploading(val progress: Int) : AvatarUploadState()
    object UpdatingAvatar : AvatarUploadState()
    object Success : AvatarUploadState()
    data class Error(val message: String) : AvatarUploadState()
}
```

#### 核心方法
- `updateAvatar(avatarUri: Uri)` - 完整的头像上传流程
- `resetAvatarUploadState()` - 重置上传状态
- `getOssPolicy()` - 获取OSS上传策略（私有方法）

### 2. UserInfoRepository 更新

#### 新增方法
```kotlin
fun getUserOssPolicyPostV2(data: Map<String, Any> = mapOf()): Flow<Resource<OssPolicyResult>>
fun userUpdateAvatar(data: Map<String, Any>): Flow<Resource<UpdateAvatarResult>>
```

## 使用流程

### 1. 在 ViewModel 中调用
```kotlin
// 用户选择图片后调用
viewModel.updateAvatar(selectedImageUri)
```

### 2. 监听上传状态
```kotlin
val profileUiState by viewModel.uiState.collectAsState()

when (profileUiState.avatarUploadState) {
    is AvatarUploadState.Idle -> {
        // 空闲状态，可以选择图片
    }
    is AvatarUploadState.GettingOssPolicy -> {
        // 正在获取上传策略
        showProgressDialog("获取上传权限...")
    }
    is AvatarUploadState.Uploading -> {
        // 正在上传，显示进度
        showProgressDialog("上传中... ${it.progress}%")
    }
    is AvatarUploadState.UpdatingAvatar -> {
        // 正在更新用户信息
        showProgressDialog("更新用户信息...")
    }
    is AvatarUploadState.Success -> {
        // 上传成功
        showSuccessMessage("头像更新成功！")
        viewModel.resetAvatarUploadState()
    }
    is AvatarUploadState.Error -> {
        // 上传失败
        showErrorMessage("头像更新失败: ${it.message}")
        viewModel.resetAvatarUploadState()
    }
}
```

### 3. 在 UI 中集成
```kotlin
@Composable
fun ProfileScreen(viewModel: ProfileViewModel) {
    val profileUiState by viewModel.uiState.collectAsState()
    
    AvatarUploadExample(
        profileUiState = profileUiState,
        onAvatarClick = {
            // 显示图片选择器
            DialogManager.showImagePicker { uri ->
                viewModel.updateAvatar(uri)
            }
        },
        onResetUploadState = {
            viewModel.resetAvatarUploadState()
        }
    )
}
```

## 技术细节

### 1. 错误处理
- 网络请求失败自动重试（2次，间隔1秒）
- 详细的错误日志记录
- 用户友好的错误提示

### 2. 状态管理
- 细粒度的上传状态跟踪
- 实时进度更新
- 状态重置机制

### 3. 数据流
```
用户选择图片 
    ↓
获取OSS策略 (getUserOssPolicyPostV2)
    ↓
上传文件到OSS (FileUploadDownloadUtil.uploadFile)
    ↓
调用更新头像接口 (userUpdateAvatar)
    ↓
更新本地用户信息 (UserManager.updateUserInfo)
    ↓
UI状态更新
```

### 4. 日志记录
完整的日志记录覆盖每个步骤：
- OSS策略获取
- 文件上传进度
- 接口调用结果
- 本地数据更新

## 相关文件

### 核心文件
- `ProfileViewModel.kt` - 主要业务逻辑
- `UserInfoRepository.kt` - 网络请求封装
- `FileUploadDownloadUtil.kt` - 文件上传工具
- `UserManager.kt` - 用户信息管理

### 模型类
- `OssPolicyResult.kt` - OSS策略结果
- `XFileUploadResult.kt` - 文件上传结果
- `UpdateAvatarResult.kt` - 头像更新结果
- `UserInfo.kt` - 用户信息模型

### UI组件
- `AvatarUploadExample.kt` - 头像上传UI组件示例
- `ProfileFormScreen.kt` - 个人资料表单页面

## 注意事项

### 安全性考虑
1. **权限检查** - 应用启动时检查并申请相机和存储权限
2. **文件验证** - 严格验证上传文件的类型、大小和内容
3. **数据加密** - OSS上传使用临时凭证，确保安全性
4. **隐私保护** - 用户可控制头像的可见性设置

### 性能优化
1. **图片压缩** - 自动根据屏幕密度智能压缩图片
2. **缓存策略** - 本地缓存用户头像，减少网络请求
3. **异步处理** - 所有上传操作在后台线程执行
4. **内存管理** - 大图片处理时注意内存使用和释放

### 用户体验
1. **进度提示** - 提供详细的上传进度和状态信息
2. **错误处理** - 友好的错误提示和恢复建议
3. **快速预览** - 选择图片后立即显示预览效果
4. **操作引导** - 首次使用时提供操作指导

### 网络优化
1. **断点续传** - 大文件支持断点续传，提高成功率
2. **重试机制** - 网络异常时自动重试，最多3次
3. **超时控制** - 合理设置上传超时时间
4. **流量控制** - 在移动网络时提醒用户流量消耗

## 扩展功能

### 即将支持的功能
- 📐 **图片裁剪**: 支持圆形、方形等多种裁剪模式
- 🎨 **滤镜效果**: 提供多种图片滤镜和美化效果
- 📱 **多尺寸生成**: 自动生成多种尺寸的头像图片
- 🔄 **批量上传**: 支持一次选择多张图片批量上传
- 📂 **上传历史**: 保存用户的上传历史记录
- ⏸️ **上传控制**: 支持暂停、取消上传操作

### 集成建议
- 在个人资料页面集成头像上传组件
- 在注册流程中添加头像设置步骤
- 提供头像更换的快捷入口
- 支持从第三方平台导入头像

## 技术架构

### 架构优势
- **模块化设计**: 头像上传功能独立封装，易于维护
- **状态管理**: 使用StateFlow进行响应式状态管理
- **错误恢复**: 完善的错误处理和自动恢复机制
- **扩展性**: 易于扩展新功能和适配新需求

### 依赖关系
```
ProfileViewModel (业务逻辑层)
    ↓
UserInfoRepository (数据仓库层)
    ↓
NetworkApi + FileUploadUtil (网络层 + 工具层)
    ↓
OSS Cloud Storage (云存储服务)
```

这个头像上传功能实现了企业级应用的完整需求，具有良好的用户体验、安全性和可扩展性。
