package com.stargate.pxo.presentation.ui.screens.login

import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.DeviceInfoUtil
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.repository.AppNetInterfaceRepository
import com.stargate.pxo.presentation.ui.view.GlobalLoadingManager
import com.stargate.pxo.presentation.ui.view.GlobalLoadingView
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

/**
 * Login screen state
 */
data class LoginState(
    val isLoading: Boolean = false,
    val username: String? = "Gimi",
    val isLoggedIn: Boolean = false,
    val termsAccepted: Boolean = true,
    val error: String? = null
) : UiState

/**
 * Login screen ViewModel
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val appNetInterfaceRepository: AppNetInterfaceRepository,
    private val deviceInfoUtil: DeviceInfoUtil,
) : BaseViewModel<LoginState>() {
    
    override fun createInitialState(): LoginState = LoginState()
    
    fun setTermsAccepted(accepted: Boolean) {
        updateState { copy(termsAccepted = accepted) }
    }

    fun login() {
        // 由于按钮已经在 UI 层面禁用，这里不再需要检查条款是否接受
        updateState { copy(isLoading = true, error = null) }
        
        PuxxiCoroutine.io{
            try {
                appNetInterfaceRepository.login(data = mapOf(
                    "oauthType" to "4",
                    "token" to deviceInfoUtil.getUniqueDeviceId()
                ))
                    .collect { resource ->
                        when (resource) {
                            is Resource.Success -> {
                                getStrategyPostV2()
                            }
                            is Resource.Error -> {
                                updateState { copy(error = resource.message, isLoading = false) }
                            }
                            is Resource.Loading -> {
                            }
                        }
                    }
            } catch (e: Exception) {
                updateState { copy(error = e.message ?: "Login failed", isLoading = false) }
            }
        }
    }


    /**
     * 获取策略配置
     * 登录成功后必须获取策略配置，确保HomeScreen能正常工作
     */
    private fun getStrategyPostV2(){
        PuxxiCoroutine.io(
            tag = "Login_StrategyConfig",
            onError = { error ->
                LogUtil.e("LoginViewModel", "获取策略配置失败: ${error.message}")
                // 即使策略配置获取失败，也允许进入主页面，使用默认配置
                updateState {
                    copy(
                        isLoggedIn = false,
                        isLoading = false,
                        error = "策略配置获取失败，使用默认配置"
                    )
                }
                GlobalLoadingManager.hide()
            }
        ) {
            appNetInterfaceRepository.getStrategyPostV2().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        LogUtil.d("LoginViewModel", "策略配置获取成功")
                        LogUtil.json("LoginViewModel", resource.data.toString())
                        // 策略配置获取成功，更新登录状态
                        updateState { copy(isLoggedIn = true, isLoading = false, error = null) }
                        GlobalLoadingManager.hide()
                    }
                    is Resource.Error -> {
                        LogUtil.e("LoginViewModel", "策略配置获取失败: ${resource.message}")
                        // ✅ 策略配置获取失败，不应该进入主页面
                        updateState {
                            copy(
                                isLoggedIn = false,  // ✅ 保持未登录状态
                                isLoading = false,
                                error = "策略配置获取失败，请重试: ${resource.message}"
                            )
                        }
                        GlobalLoadingManager.hide()
                    }
                    is Resource.Loading -> {
                        LogUtil.d("LoginViewModel", "正在获取策略配置...")
                        GlobalLoadingManager.show()
                    }
                }
            }
        }
    }



}