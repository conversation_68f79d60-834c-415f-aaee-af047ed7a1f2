package com.stargate.pxo.presentation.ui.theme
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.graphics.Color

val OutlinedTextFieldText = TextStyle(
    color = Color.White,
    fontSize = 30.sp,
    lineHeight = 42.sp,
    fontWeight = FontWeight.SemiBold
)


val OutlinedTextFieldHintText = TextStyle(
    color = Color.White.copy(0.30f),
    fontSize = 30.sp,
    lineHeight = 42.sp,
    fontWeight = FontWeight.SemiBold
)