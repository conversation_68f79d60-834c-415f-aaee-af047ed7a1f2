package com.stargate.pxo.presentation.ui.theme

import androidx.compose.ui.graphics.Color

// 旧的颜色定义（保留兼容性）
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 新的主题颜色
val Primary = Color(0xFF9C27B0)
val Secondary = Color(0xFF7B1FA2)
val Tertiary = Color(0xFFE040FB)
val Background = Color(0xFF27152E)
val Surface = Color(0xFF372038)
val Error = Color(0xFFCF6679)

// 文字和控件颜色
val OnPrimary = Color.White
val OnSecondary = Color.White
val OnTertiary = Color.White
val OnBackground = Color.White
val OnSurface = Color.White
val OnError = Color.Black

// 白色透明度变体
val White10 = Color(0x1AFFFFFF)
val White20 = Color(0x33FFFFFF)
val White30 = Color(0x4DFFFFFF)
val White50 = Color(0x80FFFFFF)
val White70 = Color(0xB3FFFFFF)