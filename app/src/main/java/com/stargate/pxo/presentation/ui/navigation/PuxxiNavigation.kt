package com.stargate.pxo.presentation.ui.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.presentation.ui.screens.login.LoginScreen
import com.stargate.pxo.presentation.ui.screens.main.MainScreen
import com.stargate.pxo.presentation.ui.screens.country.CountrySelectionScreen
import com.stargate.pxo.presentation.ui.screens.profile.ProfileFormScreen
import com.stargate.pxo.presentation.ui.screens.rank.RankScreen
import com.stargate.pxo.presentation.ui.screens.splash.SplashScreen
import com.stargate.pxo.presentation.ui.screens.webview.WebViewScreen
import com.stargate.pxo.presentation.ui.screens.webview.WebViewType
import com.stargate.pxo.presentation.ui.view.GlobalLoadingView
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

/**
 * Application Navigation Graph
 * Central navigation configuration for the entire app
 */
@Composable
fun PuxxiNavigation(navController: NavHostController) {

    NavHost(
        navController = navController,
        startDestination = "splash"
    ) {
        // Splash Screen
        composable("splash") {
            SplashScreen(navController = navController)
        }
        
        // Login Screen
        composable("login") {
            LoginScreen(navController = navController)
        }
        
        // Main Screen
        composable("main") {
            MainScreen(globalNavController = navController)
        }
        
        // Profile Form Screen
        composable("profile_form") {
            ProfileFormScreen(navController = navController)
        }
        
        // User Profile Screen
        composable("user/{userId}") { backStackEntry ->
            val userId = backStackEntry.arguments?.getString("userId")?.toLongOrNull() ?: 1L
            UserProfileScreen(userId = userId, navController = navController)
        }

        // Country Selection Screen
        composable("country_selection?initialCountry={initialCountry}") { backStackEntry ->
            val initialCountry = backStackEntry.arguments?.getString("initialCountry")
            CountrySelectionScreen(
                onCountrySelected = { country ->
                    // 将选择结果传递给上一个页面
                    navController.previousBackStackEntry
                        ?.savedStateHandle
                        ?.set("selected_country", country)
                    navController.popBackStack()
                },
                onBack = { navController.popBackStack() },
                initialCountryCode = if (initialCountry.isNullOrEmpty()) null else initialCountry
            )
        }
        
        // Rank Screen
        composable("rank") {
            RankScreen()
        }
        
        // WebView Screen - Terms & Conditions
        composable("webview/terms") {
            WebViewScreen(
                navController = navController,
                url = ApiConstants.TERM_CONDITIONS,
                type = WebViewType.TERMS_CONDITIONS
            )
        }
        
        // WebView Screen - Privacy Policy
        composable("webview/privacy") {
            WebViewScreen(
                navController = navController,
                url = ApiConstants.PRIVACY_POLICY,
                type = WebViewType.PRIVACY_POLICY
            )
        }
        
        // WebView Screen - Custom URL
        composable(
            route = "webview/custom/{encodedUrl}/{type}",
            arguments = listOf(
                navArgument("encodedUrl") { type = androidx.navigation.NavType.StringType },
                navArgument("type") { type = androidx.navigation.NavType.StringType }
            )
        ) { backStackEntry ->
            val encodedUrl = backStackEntry.arguments?.getString("encodedUrl") ?: ""
            val typeString = backStackEntry.arguments?.getString("type") ?: "TERMS_CONDITIONS"
            
            val url = java.net.URLDecoder.decode(encodedUrl, StandardCharsets.UTF_8.name())
            val type = try {
                WebViewType.valueOf(typeString)
            } catch (e: Exception) {
                WebViewType.TERMS_CONDITIONS
            }
            
            WebViewScreen(
                navController = navController,
                url = url,
                type = type
            )
        }
    }
}

/**
 * User Profile Screen
 */
@Composable
fun UserProfileScreen(userId: Long, navController: NavHostController) {
    // User profile screen content
}

/**
 * 生成自定义 WebView URL 的导航路由
 */
fun getCustomWebViewRoute(url: String, type: WebViewType): String {
    val encodedUrl = URLEncoder.encode(url, StandardCharsets.UTF_8.name())
    return "webview/custom/$encodedUrl/${type.name}"
}