package com.stargate.pxo.presentation.ui.view

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import coil.size.Scale

object ImageLoader {
    
    @Composable
    fun LoadImage(
        imageUrl: Any?,
        modifier: Modifier = Modifier,
        contentDescription: String? = null,
        contentScale: ContentScale = ContentScale.Crop,
        placeholder: Int? = null,
        error: Int? = null,
        crossfade: Boolean = true,
        crossfadeDuration: Int = 300,
        shape: Shape = RoundedCornerShape(0.dp),
        showLoading: Boolean = true,
        scale: Scale = Scale.FILL,
        onSuccess: ((AsyncImagePainter.State.Success) -> Unit)? = null,
        onError: ((AsyncImagePainter.State.Error) -> Unit)? = null,
        onLoading: ((AsyncImagePainter.State.Loading) -> Unit)? = null
    ) {
        when {
            imageUrl == null -> {
                error?.let { errorRes ->
                    Image(
                        painter = painterResource(id = errorRes),
                        contentDescription = contentDescription,
                        modifier = modifier.clip(shape),
                        contentScale = contentScale
                    )
                }
            }
            isLocalResource(imageUrl) -> {
                val resourceId = when (imageUrl) {
                    is Int -> imageUrl
                    is String -> {
                        val context = LocalContext.current
                        val cleanName = imageUrl.removePrefix("@drawable/")
                            .removePrefix("drawable/")
                            .removePrefix("@mipmap/")
                            .removePrefix("mipmap/")
                        
                        // 先尝试drawable资源
                        var resId = context.resources.getIdentifier(
                            cleanName,
                            "drawable",
                            context.packageName
                        )
                        
                        // 如果drawable中没有找到，尝试mipmap资源
                        if (resId == 0) {
                            resId = context.resources.getIdentifier(
                                cleanName,
                                "mipmap",
                                context.packageName
                            )
                        }
                        
                        if (resId != 0) resId else error
                    }
                    else -> error
                }
                
                resourceId?.let { resId ->
                    Image(
                        painter = painterResource(id = resId),
                        contentDescription = contentDescription,
                        modifier = modifier.clip(shape),
                        contentScale = contentScale
                    )
                }
            }
            else -> {
                val imageRequest = ImageRequest.Builder(LocalContext.current)
                    .data(imageUrl)
                    .scale(scale)
                    .crossfade(crossfade)
                    .crossfade(crossfadeDuration)
                    .build()
                
                // 使用 AsyncImage 并跟踪其状态
                Box(modifier = modifier) {
                    var isLoading by remember { mutableStateOf(true) }
                    var isError by remember { mutableStateOf(false) }
                    
                    AsyncImage(
                        model = imageRequest,
                        contentDescription = contentDescription,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(shape),
                        contentScale = contentScale,
                        onState = { state ->
                            when (state) {
                                is AsyncImagePainter.State.Loading -> {
                                    isLoading = true
                                    isError = false
                                    android.util.Log.d("ImageLoader", "Loading image: $imageUrl")
                                    onLoading?.invoke(state)
                                }
                                is AsyncImagePainter.State.Success -> {
                                    isLoading = false
                                    isError = false
                                    android.util.Log.d("ImageLoader", "Image loaded successfully: $imageUrl")
                                    onSuccess?.invoke(state)
                                }
                                is AsyncImagePainter.State.Error -> {
                                    isLoading = false
                                    isError = true
                                    android.util.Log.e("ImageLoader", "Image load failed: $imageUrl")
                                    android.util.Log.e("ImageLoader", "Error: ${state.result.throwable?.message}")
                                    onError?.invoke(state)
                                }
                                else -> {
                                    isLoading = false
                                    isError = false
                                }
                            }
                        }
                    )
                    
                    // 显示加载指示器
                    if (showLoading && isLoading) {
                        placeholder?.let { placeholderRes ->
                            Image(
                                painter = painterResource(id = placeholderRes),
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = contentScale
                            )
                        }
                        
                        CircularProgressIndicator(
                            modifier = Modifier
                                .size(24.dp)
                                .align(Alignment.Center),
                            color = Color.White.copy(alpha = 0.7f),
                            strokeWidth = 2.dp
                        )
                    }
                    
                    // 显示错误图片
                    if (isError) {
                        error?.let { errorRes ->
                            Image(
                                painter = painterResource(id = errorRes),
                                contentDescription = null,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = contentScale
                            )
                        }
                    }
                }
            }
        }
    }
    
    @Composable
    fun LoadThumbnail(
        imageUrl: Any?,
        modifier: Modifier = Modifier,
        size: Dp = 100.dp,
        contentDescription: String? = null,
        placeholder: Int? = null,
        error: Int? = null,
        shape: Shape = RoundedCornerShape(8.dp)
    ) {
        LoadImage(
            imageUrl = imageUrl,
            modifier = modifier.size(size),
            contentDescription = contentDescription,
            placeholder = placeholder,
            error = error,
            shape = shape,
            scale = Scale.FIT,
            contentScale = ContentScale.Crop,
            crossfadeDuration = 300
        )
    }
    
    fun preloadImage(context: android.content.Context, imageUrl: String) {
        val imageRequest = ImageRequest.Builder(context)
            .data(imageUrl)
            .build()
        
        ImageLoaderConfig.getImageLoader(context).enqueue(imageRequest)
    }
    
    fun isLocalResource(imageUrl: Any?): Boolean {
        return when (imageUrl) {
            is Int -> true
            is String -> {
                imageUrl.startsWith("@drawable/") || 
                imageUrl.startsWith("drawable/") ||
                imageUrl.startsWith("@mipmap/") ||
                imageUrl.startsWith("mipmap/") ||
                imageUrl.matches(Regex("^[a-zA-Z_][a-zA-Z0-9_]*$"))
            }
            else -> false
        }
    }
}