package com.stargate.pxo.presentation.ui.screens.main.home

import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.StrategyConfigManager
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.manager.UserManager
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.network.model.BannerInfo
import com.stargate.pxo.data.network.model.BroadcasterItem
import com.stargate.pxo.data.network.model.BroadcasterWallTag
import com.stargate.pxo.data.repository.BroadcasterInfoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.presentation.ui.view.GlobalLoadingManager
import com.stargate.pxo.common.util.BroadcasterStatusManager
import com.stargate.pxo.data.model.CountryRegion

import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 首页UI状态
 */
data class HomeUiState(
    val broadcasterWallTagList: List<BroadcasterWallTag> = emptyList(), // 直接使用策略配置的标签列表
    val selectedTagIndex: Int = 0, // 选中的主标签索引
    val selectedSubTagIndex: Int = 0, // 选中的子标签索引
    val banners: List<BannerInfo> = emptyList(),
    val broadcasters: List<BroadcasterItem> = emptyList(), // 主播墙数据
    val currentPage: Int = 1, // 当前页码
    val hasMoreData: Boolean = true, // 是否还有更多数据
    val isLoading: Boolean = false,
    val isLoadingMore: Boolean = false, // 是否正在加载更多
    val error: String? = null,
    val selectedRegion: String = "" // 选中的地区（从国家选择页面传回）
) : UiState {

    // 便捷属性：获取当前选中的主标签
    val selectedTag: BroadcasterWallTag?
        get() = broadcasterWallTagList.getOrNull(selectedTagIndex)

    // 便捷属性：获取当前选中的子标签
    val selectedSubTag: String?
        get() = selectedTag?.subTagList?.getOrNull(selectedSubTagIndex)
}

/**
 * 首页ViewModel
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val broadcasterInfoRepository: BroadcasterInfoRepository,
    private val strategyConfigManager: StrategyConfigManager,
    private val broadcasterStatusManager: BroadcasterStatusManager
) : BaseViewModel<HomeUiState>() {

    // 初始化默认状态
    override fun createInitialState(): HomeUiState = HomeUiState(
        broadcasterWallTagList = emptyList(),
        selectedTagIndex = 0,
        selectedSubTagIndex = 0,
        selectedRegion = "",
    )



    init {
        // 初始化加载数据
        loadInitialData()

        // 监听用户信息变化
        viewModelScope.launch {
            UserManager.currentUser.collectLatest { userInfo ->
                // 用户信息变化时可以在这里处理
            }
        }
    }

    /**
     * 加载初始数据
     */
    private fun loadInitialData() {
        GlobalLoadingManager.show()
        viewModelScope.launch {
            LogUtil.d("HomeViewModel", "开始加载初始数据")
            updateState {
                copy(isLoading = true, error = null) }

            try {
                // 加载策略配置中的标签数据
                loadCategoryData()

                // 加载Banner数据
                loadBanners()

                // 刷新主播列表
                loadBroadcasterWall()

                LogUtil.d("HomeViewModel", "初始数据加载完成")
            } catch (e: Exception) {
                LogUtil.e("HomeViewModel", "Failed to load initial data", e)
                updateState { copy(isLoading = false, error = "Failed to load data: ${e.message}") }
            } finally {
                updateState { copy(isLoading = false) }
            }
        }
    }

    /**
     * 从StrategyConfig中加载分类数据
     * 如果策略配置不存在，使用默认配置
     */
    private suspend fun loadCategoryData() {
        try {
            // 从缓存获取策略配置数据
            val strategyConfig = strategyConfigManager.getStrategyConfig()

            if (strategyConfig != null) {
                LogUtil.d("HomeViewModel", "使用缓存的策略配置数据加载标签")
                // 直接从缓存配置中加载标签
                processBroadcasterWallTags(strategyConfig.broadcasterWallTagList)
            } else {
                LogUtil.w("HomeViewModel", "策略配置数据不存在，使用默认标签")
                // 策略配置不存在时使用默认标签
                loadDefaultTags()
                // 尝试重新获取策略配置
                tryRefreshStrategyConfig()
            }
        } catch (e: Exception) {
            LogUtil.e("HomeViewModel", "Failed to load category data", e)
            // 异常情况下使用默认标签保证应用稳定性
            loadDefaultTags()
        }
    }

    /**
     * 尝试重新获取策略配置
     */
    private fun tryRefreshStrategyConfig() {
        viewModelScope.launch {
            try {
                LogUtil.d("HomeViewModel", "尝试重新获取策略配置")
                // 这里可以调用 repository 重新获取策略配置
                // 但不阻塞当前的UI显示
            } catch (e: Exception) {
                LogUtil.e("HomeViewModel", "重新获取策略配置失败", e)
            }
        }
    }



    /**
     * 处理从StrategyConfig获取的标签数据
     * 直接使用服务器配置的标签数据，更简洁高效
     */
    private fun processBroadcasterWallTags(tagList: List<BroadcasterWallTag>) {
        try {
            LogUtil.d("HomeViewModel", "标签数据处理完成 ${tagList.toString()}")
            updateState {
                copy(
                    broadcasterWallTagList = tagList,
                    selectedTagIndex = 0, // 默认选中第一个（All）
                    selectedSubTagIndex = 0 // 默认选中第一个子标签
                )
            }
            LogUtil.d("HomeViewModel", "标签数据处理完成，标签数量: ${tagList.size}")
        } catch (e: Exception) {
            LogUtil.e("HomeViewModel", "处理标签数据失败", e)
            // 使用默认标签
            loadDefaultTags()
        }
    }

    /**
     * 加载默认标签（在无法获取服务器配置时使用）
     */
    private fun loadDefaultTags() {
        val defaultTags = listOf(
            BroadcasterWallTag(
                tagName = "all",
                showTagName = "All",
                subTagList = listOf("All"),
                subTagInitIndex = 0
            ),
            BroadcasterWallTag(
                tagName = "Popular",
                showTagName = "Popular",
                subTagList = listOf("All", "USA", "English", "Asian", "Europe"),
                subTagInitIndex = 0
            ),
            BroadcasterWallTag(
                tagName = "Style",
                showTagName = "Style",
                subTagList = listOf("All", "Mature", "Babe", "Wild", "College"),
                subTagInitIndex = 0
            ),
            BroadcasterWallTag(
                tagName = "Look",
                showTagName = "Look",
                subTagList = listOf("All", "Goddess", "Curvy", "Plump", "Cute"),
                subTagInitIndex = 0
            )
        )

        updateState {
            copy(
                broadcasterWallTagList = defaultTags,
                selectedTagIndex = 0,
                selectedSubTagIndex = 0
            )
        }
    }




    
    /**
     * 加载Banner数据
     */
    private suspend fun loadBanners() {
        LogUtil.d("HomeViewModel", "开始加载Banner数据")
        try {
            broadcasterInfoRepository.getBannerInfo().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        LogUtil.d("HomeViewModel", "Banner数据加载成功，数量: ${resource.data.size}")
                        updateState { copy(banners = resource.data) }
                    }
                    is Resource.Error -> {
                        LogUtil.e("HomeViewModel", "Failed to load banners: ${resource.message}")
                    }
                    is Resource.Loading -> {
                        LogUtil.d("HomeViewModel", "Banner数据加载中...")
                        // 处理加载状态
                    }
                }
            }
        } catch (e: Exception) {
            LogUtil.e("HomeViewModel", "加载Banner数据时发生异常", e)
        }
    }
    

    

    
    /**
     * 选择主标签
     */
    fun selectTag(tagIndex: Int) {
        val currentState = uiState.value
        if (tagIndex == currentState.selectedTagIndex ||
            tagIndex < 0 ||
            tagIndex >= currentState.broadcasterWallTagList.size) return

        viewModelScope.launch {
            updateState {
                copy(
                    selectedTagIndex = tagIndex,
                    selectedSubTagIndex = 0, // 重置子标签选择
                    isLoading = true
                )
            }

            // 刷新主播列表（重置到第一页）
            loadBroadcasterWall(page = 1, isLoadMore = false)
        }
    }

    /**
     * 选择子标签
     */
    fun selectSubTag(subTagIndex: Int) {
        val currentState = uiState.value
        val selectedTag = currentState.selectedTag

        if (subTagIndex == currentState.selectedSubTagIndex ||
            selectedTag == null ||
            subTagIndex < 0 ||
            subTagIndex >= selectedTag.subTagList.size) return

        viewModelScope.launch {
            updateState { copy(selectedSubTagIndex = subTagIndex) }

            // 刷新主播列表（重置到第一页）
            loadBroadcasterWall(page = 1, isLoadMore = false)
        }
    }
    
    /**
     * 下拉刷新数据
     * 只刷新主播墙数据，简化逻辑
     */
    fun refresh() {
        LogUtil.d("HomeViewModel", "开始下拉刷新")
        loadBroadcasterWall(page = 1, isLoadMore = false)
    }
    
    /**
     * 加载更多主播数据
     */
    fun loadMoreBroadcasters() {
        val currentState = uiState.value
        if (currentState.isLoadingMore || !currentState.hasMoreData) {
            LogUtil.d("HomeViewModel", "跳过加载更多：isLoadingMore=${currentState.isLoadingMore}, hasMoreData=${currentState.hasMoreData}")
            return
        }

        val nextPage = currentState.currentPage + 1
        LogUtil.d("HomeViewModel", "开始加载更多数据，页码: $nextPage")
        loadBroadcasterWall(page = nextPage, isLoadMore = true)
    }

    /**
     * 处理Banner点击
     */
    fun onBannerClick(banner: BannerInfo) {
        LogUtil.d("HomeViewModel", "Banner clicked: ${banner.broadcasterId}")
        // 处理Banner点击事件，例如跳转到详情页或执行特定操作
        // 可以根据 banner.jumpUrl 进行页面跳转
    }
    
    /**
     * 处理主播点击
     */
    fun onBroadcasterClick(broadcaster: BroadcasterItem) {
        LogUtil.d("HomeViewModel", "Broadcaster clicked: ${broadcaster.userId}")
        // 处理主播点击事件，例如跳转到主播详情页
    }

    /**
     * 处理通话按钮点击
     */
    fun onCallBroadcaster(broadcaster: BroadcasterItem) {
        LogUtil.d("HomeViewModel", "Call broadcaster: ${broadcaster.userId}")
        // 处理通话按钮点击事件，例如发起通话请求
    }

    /**
     * 加载主播墙数据
     * 使用新的 BroadcasterInfoRepository，数据直接更新到 uiState.broadcasters
     * @param page 页码，默认为1（首次加载或刷新）
     * @param isLoadMore 是否为加载更多操作
     */
    fun loadBroadcasterWall(page: Int = 1, isLoadMore: Boolean = false) {
        viewModelScope.launch {
            val currentState = uiState.value
            val selectedTag = currentState.selectedTag?.tagName ?: "all"
            val selectedSubTag = currentState.selectedSubTag ?: "All"

            val requestData = mapOf(
                "page" to page,
                "limit" to 20,
                "isPageMode" to true,
                "isRemoteImageUrl" to true,
                "category" to selectedTag,
                "region" to currentState.selectedRegion,
                "tag" to selectedSubTag,
                "supportMgRoom" to false
            )

            broadcasterInfoRepository.getBroadcasterWall(requestData)
                .collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            if (isLoadMore) {
                                updateState { copy(isLoadingMore = true) }
                            } else {
                                updateState { copy(isLoading = true) }
                                GlobalLoadingManager.show()
                            }
                        }
                        is Resource.Success -> {
                            // Repository已经处理了数据库存储
                            // 后台已经处理好了主播数据，直接使用
                            val newBroadcasters = resource.data
                            LogUtil.d("HomeViewModel", "主播墙数据加载成功，页码: $page, 数量: ${newBroadcasters.size}")

                            // 判断是否还有更多数据（如果返回的数据少于请求的数量，说明没有更多数据了）
                            val hasMore = newBroadcasters.size >= 20

                            // 更新到 uiState
                            updateState {
                                val updatedBroadcasters = if (isLoadMore) {
                                    // 加载更多：合并数据
                                    val existingBroadcasters = broadcasters
                                    val mergedBroadcasters = (existingBroadcasters + newBroadcasters)
                                        .distinctBy { it.userId } // 去重，以userId为唯一标识
                                    mergedBroadcasters
                                } else {
                                    // 首次加载或刷新：替换数据
                                    newBroadcasters
                                }

                                copy(
                                    broadcasters = updatedBroadcasters,
                                    currentPage = page,
                                    hasMoreData = hasMore,
                                    isLoading = false,
                                    isLoadingMore = false,
                                    error = null
                                )
                            }

                            PuxxiCoroutine.delay(500L)
                            GlobalLoadingManager.hide()
                        }
                        is Resource.Error -> {
                            LogUtil.e("HomeViewModel", "主播墙数据加载失败，页码: $page, 错误: ${resource.message}")

                            if (isLoadMore) {
                                // 加载更多失败，只更新加载状态，保持现有数据
                                updateState {
                                    copy(
                                        isLoadingMore = false,
                                        error = "加载更多失败: ${resource.message}"
                                    )
                                }
                            } else {
                                // 首次加载或刷新失败，尝试从数据库获取缓存数据
                                val cachedBroadcasters = broadcasterInfoRepository.getBroadcastersFromDatabase()
                                if (cachedBroadcasters.isNotEmpty()) {
                                    LogUtil.d("HomeViewModel", "使用数据库缓存数据，数量: ${cachedBroadcasters.size}")
                                    // 后台已经处理好了数据，直接使用缓存数据
                                    updateState {
                                        copy(
                                            broadcasters = cachedBroadcasters,
                                            currentPage = 1,
                                            hasMoreData = false,
                                            isLoading = false,
                                            error = "网络请求失败，显示缓存数据"
                                        )
                                    }
                                } else {
                                    updateState {
                                        copy(
                                            isLoading = false,
                                            error = "加载主播数据失败: ${resource.message}"
                                        )
                                    }
                                }
                                PuxxiCoroutine.delay(500L)
                                GlobalLoadingManager.hide()
                            }
                        }
                    }
                }
        }
    }


    /**
     * 更新可见范围内的主播状态轮询
     * @param visibleBroadcasterIds 当前屏幕可见的主播ID列表
     */
    fun updateVisibleBroadcasterPolling(visibleBroadcasterIds: List<String>) {
        if (visibleBroadcasterIds.isEmpty()) {
            LogUtil.d("HomeViewModel", "No visible broadcasters, stopping polling")
            return
        }

        // 请求可见主播的状态轮询
        broadcasterStatusManager.requestStatusBatch(visibleBroadcasterIds)
        LogUtil.d("HomeViewModel", "Updated polling for ${visibleBroadcasterIds.size} visible broadcasters: $visibleBroadcasterIds")
    }

    /**
     * 停止所有主播状态轮询
     */
    fun stopBroadcasterPolling() {
        broadcasterStatusManager.clearCache()
        LogUtil.d("HomeViewModel", "Stopped all broadcaster polling")
    }

    /**
     * 手动刷新指定主播的状态
     * @param broadcasterId 主播ID
     */
    fun refreshBroadcasterStatus(broadcasterId: String) {
        broadcasterStatusManager.requestStatusImmediately(broadcasterId)
        LogUtil.d("HomeViewModel", "Manually refreshed status for broadcaster: $broadcasterId")
    }



    /**
     * 选择国家/地区
     * 从 CountrySelectionScreen 页面选择国家后调用此方法
     */
    fun selectCountryRegion(countryRegion: CountryRegion) {
        LogUtil.d("HomeViewModel", "从国家选择页面接收到选择: ${countryRegion.name} (${countryRegion.code})")

        // 更新选中的地区
        updateState {
            copy(selectedRegion = countryRegion.code)
        }

        // 根据选择的国家重新加载主播墙数据
        LogUtil.d("HomeViewModel", "根据选择的国家 ${countryRegion.code} 重新加载主播数据")
        loadBroadcasterWall(page = 1, isLoadMore = false)
    }

    /**
     * ViewModel 销毁时的清理工作
     */
    override fun onCleared() {
        super.onCleared()
        // 停止主播状态轮询，释放资源
        stopBroadcasterPolling()
        LogUtil.d("HomeViewModel", "ViewModel cleared, stopped broadcaster polling")
    }


}