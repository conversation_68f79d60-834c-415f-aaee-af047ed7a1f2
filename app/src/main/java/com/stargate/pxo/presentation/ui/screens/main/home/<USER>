# HomeScreen 屏幕滑动切换标签功能

## 🎯 功能需求

在 HomeScreen 中监听整个屏幕的左右滑动手势，通过滑动来切换 TopFilterTabs 中的标签，类似 ViewPager 的体验。

## 🔧 实现方案

### 核心思路：
- **保持原有 UI 结构** - TopFilterTabsWithIcons 保持不变
- **在根容器添加手势监听** - 在 HomeScreen 的主 Column 上添加滑动手势检测
- **手势触发标签切换** - 滑动手势触发 ViewModel 的标签切换方法

## 📋 具体实现

### 1. **恢复原有的标签栏结构**

```kotlin
// ✅ 保持原有的 LazyRow 结构
@Composable
fun TopFilterTabsWithIcons(...) {
    LazyRow(
        modifier = Modifier.padding(top = 25.dp).weight(1f).fillMaxHeight(),
    ) {
        itemsIndexed(tagList) { index, tag ->
            TagTab(
                tag = tag,
                isSelected = index == selectedTagIndex,
                onClick = { onTagSelected(index) }
            )
        }
    }
}
```

### 2. **在 HomeScreen 根容器添加手势监听**

```kotlin
@Composable
fun HomeScreen(...) {
    // 用于累积滑动距离
    var totalDragAmount by remember { mutableFloatStateOf(0f) }
    var hasTriggered by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF27152E))
            .pointerInput(uiState.tags.size) { // ✅ 关键：添加手势监听
                detectHorizontalDragGestures(
                    onDragStart = {
                        // 开始滑动时重置状态
                        totalDragAmount = 0f
                        hasTriggered = false
                    },
                    onDragEnd = {
                        // 滑动结束时重置状态
                        totalDragAmount = 0f
                        hasTriggered = false
                    }
                ) { change, dragAmount ->
                    // 累积滑动距离
                    totalDragAmount += dragAmount
                    
                    // 滑动阈值
                    val threshold = 150f
                    
                    // 避免在一次滑动中多次触发
                    if (!hasTriggered && abs(totalDragAmount) > threshold) {
                        hasTriggered = true
                        
                        val currentIndex = uiState.selectedTagIndex
                        val maxIndex = uiState.tags.size - 1
                        
                        when {
                            totalDragAmount > 0 && currentIndex > 0 -> {
                                // 右滑，切换到上一个标签
                                viewModel.selectTag(currentIndex - 1)
                            }
                            totalDragAmount < 0 && currentIndex < maxIndex -> {
                                // 左滑，切换到下一个标签
                                viewModel.selectTag(currentIndex + 1)
                            }
                        }
                    }
                }
            }
    ) {
        // 原有的 HomeScreen 内容
        TopFilterTabsWithIcons(...)
        // 其他组件...
    }
}
```

## 🎨 用户体验

### 滑动逻辑：

#### **右滑（向右拖拽）**
```
用户手指向右滑动 → totalDragAmount > 0 → 切换到上一个标签
[全部] ← [美女] [帅哥] [才艺]
```

#### **左滑（向左拖拽）**
```
用户手指向左滑动 → totalDragAmount < 0 → 切换到下一个标签
[全部] [美女] → [帅哥] [才艺]
```

### 边界处理：
- ✅ **第一个标签** - 右滑无效，防止越界
- ✅ **最后一个标签** - 左滑无效，防止越界
- ✅ **中间标签** - 左右滑动都有效

## ✅ 技术实现细节

### 1. **手势检测参数**

```kotlin
.pointerInput(uiState.tags.size) { // key 确保标签数量变化时重新创建
    detectHorizontalDragGestures(
        onDragStart = { /* 开始滑动 */ },
        onDragEnd = { /* 结束滑动 */ }
    ) { change, dragAmount ->
        // 滑动过程中的处理
    }
}
```

### 2. **累积滑动距离**

```kotlin
var totalDragAmount by remember { mutableFloatStateOf(0f) }

// 在滑动过程中累积
totalDragAmount += dragAmount

// 达到阈值时触发切换
if (abs(totalDragAmount) > threshold) {
    // 执行标签切换
}
```

### 3. **防重复触发机制**

```kotlin
var hasTriggered by remember { mutableStateOf(false) }

// 只在未触发且达到阈值时执行
if (!hasTriggered && abs(totalDragAmount) > threshold) {
    hasTriggered = true // 标记已触发
    // 执行切换逻辑
}

// 滑动结束时重置
onDragEnd = {
    hasTriggered = false
}
```

### 4. **滑动阈值配置**

```kotlin
val threshold = 150f // 滑动阈值，可以调整

// 较小的阈值 = 更敏感的滑动
// 较大的阈值 = 需要更大的滑动距离
```

## 🎯 功能特性

### 1. **全屏手势监听**
- ✅ **整个屏幕** - 在屏幕任意位置滑动都能切换标签
- ✅ **不影响其他交互** - 不影响列表滚动、按钮点击等操作
- ✅ **优先级合理** - 手势检测不会干扰子组件的交互

### 2. **智能切换逻辑**
- ✅ **方向识别** - 正确识别左滑和右滑
- ✅ **边界保护** - 防止越界切换
- ✅ **阈值控制** - 需要足够的滑动距离才触发

### 3. **状态管理**
- ✅ **状态同步** - 手势切换后 UI 立即更新
- ✅ **防重复触发** - 一次滑动只触发一次切换
- ✅ **状态重置** - 滑动结束后重置内部状态

## 🔧 可调整参数

### 1. **滑动阈值**
```kotlin
val threshold = 150f // 默认值，可以调整

// 建议值：
// 100f - 敏感，轻微滑动就切换
// 150f - 适中，需要明确的滑动意图
// 200f - 不敏感，需要较大的滑动距离
```

### 2. **手势检测范围**
```kotlin
// 当前：整个 Column 都监听手势
.pointerInput(uiState.tags.size) { ... }

// 可以限制到特定区域：
.pointerInput(uiState.tags.size) {
    // 只在顶部区域监听
    if (it.position.y < 200.dp.toPx()) {
        detectHorizontalDragGestures { ... }
    }
}
```

### 3. **滑动方向映射**
```kotlin
when {
    totalDragAmount > 0 && currentIndex > 0 -> {
        // 右滑 → 上一个标签
        viewModel.selectTag(currentIndex - 1)
    }
    totalDragAmount < 0 && currentIndex < maxIndex -> {
        // 左滑 → 下一个标签
        viewModel.selectTag(currentIndex + 1)
    }
}

// 可以反转方向：
// 右滑 → 下一个标签
// 左滑 → 上一个标签
```

## 📊 与之前方案的对比

### 之前的错误方案 ❌
```kotlin
// 把标签组件本身变成了可滑动的 HorizontalPager
HorizontalPager(state = pagerState) { page ->
    TagTab(tag = tagList[page])
}
```

**问题：**
- ❌ 改变了 UI 结构
- ❌ 只能在标签区域滑动
- ❌ 复杂的状态同步

### 当前的正确方案 ✅
```kotlin
// 保持原有 UI，在根容器监听手势
Column(
    modifier = Modifier.pointerInput { detectHorizontalDragGestures { ... } }
) {
    TopFilterTabsWithIcons(...) // 保持不变
    // 其他内容
}
```

**优势：**
- ✅ 保持原有 UI 结构
- ✅ 全屏手势监听
- ✅ 简单的实现逻辑

## 🚀 总结

**HomeScreen 屏幕滑动切换标签功能实现完成！**

### 核心特性：
1. ✅ **全屏手势监听** - 在屏幕任意位置滑动都能切换标签
2. ✅ **保持原有 UI** - TopFilterTabsWithIcons 结构不变
3. ✅ **智能切换逻辑** - 左滑下一个，右滑上一个，有边界保护
4. ✅ **防重复触发** - 一次滑动只触发一次切换

### 用户体验：
- 🎯 **直观操作** - 左右滑动切换标签，符合用户习惯
- 📱 **全屏响应** - 不限制滑动区域，操作更自由
- ⚡ **即时反馈** - 滑动后立即切换标签和更新内容
- 🛡️ **边界保护** - 防止越界操作，体验更稳定

### 技术实现：
- 🔧 **手势检测** - 使用 `detectHorizontalDragGestures` 监听滑动
- 📊 **累积距离** - 累积滑动距离，达到阈值才触发
- 🔄 **状态管理** - 防重复触发和状态重置机制
- 🎯 **参数可调** - 滑动阈值和方向映射可以调整

现在用户可以在 HomeScreen 的任意位置左右滑动来切换顶部的标签，就像使用 ViewPager 一样自然！
