package com.stargate.pxo.presentation.ui.screens.main.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridItemSpan
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.compose.runtime.DisposableEffect
import com.stargate.pxo.data.model.CountryRegion
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.text.style.TextAlign
import kotlinx.coroutines.flow.distinctUntilChanged
import com.king.ultraswiperefresh.NestedScrollMode
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.ui.graphics.graphicsLayer
import kotlin.math.abs
import com.king.ultraswiperefresh.UltraSwipeRefresh
import com.king.ultraswiperefresh.rememberUltraSwipeRefreshState
import com.stargate.pxo.R
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.data.network.model.BannerInfo
import com.stargate.pxo.data.network.model.BroadcasterItem
import com.stargate.pxo.data.network.model.BroadcasterWallTag
import com.stargate.pxo.presentation.ui.theme.noRippleClickable
import com.stargate.pxo.common.util.BroadcasterStatusUtils
import com.stargate.pxo.common.util.getStatusColor
import com.stargate.pxo.presentation.ui.view.ImageLoader
import kotlinx.coroutines.delay

/**
 * 首页屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    viewModel: HomeViewModel = hiltViewModel(),
    globalNavController: NavHostController? = null
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val lazyGridState = rememberLazyStaggeredGridState()
    val refreshState = rememberUltraSwipeRefreshState()

    DisposableEffect(globalNavController) {
        val navController = globalNavController
        val listener = NavController.OnDestinationChangedListener { _, _, _ ->
            // 当导航发生变化时检查是否有国家选择结果
            navController?.currentBackStackEntry?.savedStateHandle?.let { savedStateHandle ->
                val selectedCountry = savedStateHandle.get<CountryRegion>("selected_country")
                if (selectedCountry != null) {
                    LogUtil.d(
                        "HomeScreen",
                        "接收到国家选择结果: ${selectedCountry.name} (${selectedCountry.code})"
                    )
                    // 如果选中的是"All"（code为空），则传递空的CountryRegion
                    if (selectedCountry.code.isEmpty()) {
                        // 选中"All"，清空地区筛选
                        viewModel.selectCountryRegion(
                            CountryRegion(
                                code = "",
                                name = "All",
                                flag = "🌍"
                            )
                        )
                    } else {
                        // 选中具体国家
                        viewModel.selectCountryRegion(selectedCountry)
                    }
                    // 清除结果，避免重复处理
                    savedStateHandle.remove<CountryRegion>("selected_country")
                }
            }
        }

        navController?.addOnDestinationChangedListener(listener)

        onDispose {
            navController?.removeOnDestinationChangedListener(listener)
        }
    }

    // 监听可见范围变化，更新主播状态轮询
    LaunchedEffect(lazyGridState) {
        snapshotFlow {
            lazyGridState.layoutInfo.visibleItemsInfo.mapNotNull { itemInfo ->
                // 获取可见项目的索引，排除非主播项目（如加载指示器等）
                val index = itemInfo.index
                if (index < uiState.broadcasters.size) {
                    uiState.broadcasters[index].userId
                } else {
                    null
                }
            }
        }
            .distinctUntilChanged() // 只在可见列表真正变化时触发
            .collect { visibleBroadcasterIds ->
                if (visibleBroadcasterIds.isNotEmpty()) {
                    viewModel.updateVisibleBroadcasterPolling(visibleBroadcasterIds)
                }
            }
    }

    // 用于累积滑动距离
    var totalDragAmount by remember { mutableFloatStateOf(0f) }
    var hasTriggered by remember { mutableStateOf(false) }

    // 滑动动画状态
    var isSwipeAnimating by remember { mutableStateOf(false) }
    var swipeOffset by remember { mutableFloatStateOf(0f) }

    // 动画化的偏移值
    val animatedOffset by animateFloatAsState(
        targetValue = if (isSwipeAnimating) 0f else swipeOffset,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        finishedListener = {
            isSwipeAnimating = false
            swipeOffset = 0f
        },
        label = "swipe_animation"
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF27152E))
            .pointerInput(uiState.broadcasterWallTagList.size) {
                // 监听水平滑动手势
                detectHorizontalDragGestures(
                    onDragStart = {
                        // 开始滑动时重置状态
                        totalDragAmount = 0f
                        hasTriggered = false
                        isSwipeAnimating = false
                    },
                    onDragEnd = {
                        // 滑动结束时触发回弹动画
                        if (!hasTriggered) {
                            isSwipeAnimating = true
                        }
                        // 重置状态
                        totalDragAmount = 0f
                        hasTriggered = false
                    }
                ) { change, dragAmount ->
                    // 累积滑动距离
                    totalDragAmount += dragAmount

                    // 实时更新滑动偏移（用于视觉反馈）
                    val maxOffset = 100f // 最大偏移量
                    swipeOffset = (totalDragAmount * 0.3f).coerceIn(-maxOffset, maxOffset)

                    // 滑动阈值
                    val threshold = 150f

                    // 避免在一次滑动中多次触发
                    if (!hasTriggered && abs(totalDragAmount) > threshold) {
                        hasTriggered = true
                        isSwipeAnimating = true

                        val currentIndex = uiState.selectedTagIndex
                        val maxIndex = uiState.broadcasterWallTagList.size - 1

                        when {
                            totalDragAmount > 0 && currentIndex > 0 -> {
                                // 右滑，切换到上一个标签
                                viewModel.selectTag(currentIndex - 1)
                            }

                            totalDragAmount < 0 && currentIndex < maxIndex -> {
                                // 左滑，切换到下一个标签
                                viewModel.selectTag(currentIndex + 1)
                            }
                        }
                    }
                }
            }
    ) {
        // 内容区域 - 添加动画变换
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    // 添加水平偏移动画
                    translationX = animatedOffset
                    // 可选：添加轻微的缩放效果
                    scaleX = 1f - abs(animatedOffset) * 0.0005f
                    scaleY = 1f - abs(animatedOffset) * 0.0005f
                }
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                // 顶部过滤标签栏和图标
                TopFilterTabsWithIcons(
                    uiState,
                    tagList = uiState.broadcasterWallTagList,
                    selectedTagIndex = uiState.selectedTagIndex,
                    onTagSelected = { viewModel.selectTag(it) },
                    onRankClick = { viewModel.onRankClick() },
                    onCountryClick = {
                        // 使用全局导航跳转到国家选择页面，带上当前选中的国家
                        val currentCountry =
                            if (uiState.selectedRegion.isEmpty()) null else uiState.selectedRegion
                        globalNavController?.navigate("country_selection?initialCountry=${currentCountry ?: ""}")
                    }
                )

                // 子标签栏
                SubTagTabs(
                    selectedTag = uiState.selectedTag,
                    selectedSubTagIndex = uiState.selectedSubTagIndex,
                    onSubTagSelected = { viewModel.selectSubTag(it) }
                )

                // Banner 轮播
                BannerCarousel(
                    banners = uiState.banners,
                    onBannerClick = { viewModel.onBannerClick(it) }
                )

                // 更新刷新状态
                refreshState.isRefreshing = uiState.isLoading
                refreshState.isLoading = uiState.isLoadingMore

                UltraSwipeRefresh(
                    state = refreshState,
                    onRefresh = {
                        viewModel.refresh()
                    },
                    onLoadMore = {
                        viewModel.loadMoreBroadcasters()
                    },
                    headerScrollMode = NestedScrollMode.Translate,
                    footerScrollMode = NestedScrollMode.Translate,
//                    headerIndicator = { offset ->
//                        ClassicRefreshHeader(offset)
//                    },
//                    footerIndicator = {
//                        ClassicRefreshFooter(it)
//                    }
                ) {

                    if (uiState.broadcasters.isEmpty() && !uiState.isLoading) {
                        // 空状态 - 使用LazyColumn以支持下拉刷新
                        LazyColumn(
                            modifier = Modifier.fillMaxSize(),
                            contentPadding = PaddingValues(32.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            item {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    ImageLoader.LoadImage(
                                        R.mipmap.no_anchor_bg,
                                        contentDescription = "Empty State",
                                        modifier = Modifier.size(388.dp, 235.dp),
                                    )
                                    Text(
                                        text = "No anchor",
                                        color = Color.White.copy(alpha = 0.6f),
                                        fontSize = 32.sp,
                                        textAlign = TextAlign.Center,
                                        lineHeight = 45.sp,
                                        modifier = Modifier.padding(top = 26.dp)
                                    )
                                }
                            }
                        }
                    } else {
                        LazyVerticalStaggeredGrid(
                            columns = StaggeredGridCells.Fixed(2),
                            state = lazyGridState, // 添加状态管理
                            modifier = Modifier.fillMaxSize().padding(horizontal = 15.dp),
                            horizontalArrangement = Arrangement.spacedBy(15.dp),
                            verticalItemSpacing = 15.dp,
                        ) {
                            items(
                                items = uiState.broadcasters,
                                key = { broadcaster -> broadcaster.userId } // 添加唯一key提升性能
                            ) { broadcaster ->
                                BroadcasterItem(
                                    broadcaster = broadcaster,
                                    onItemClick = { viewModel.onBroadcasterClick(broadcaster) },
                                    onCallClick = { viewModel.onCallBroadcaster(broadcaster) }
                                )
                            }

                            // 没有更多数据提示
                            if (!uiState.hasMoreData && uiState.broadcasters.isNotEmpty() && !uiState.isLoadingMore && !uiState.isLoading) {
                                item(span = StaggeredGridItemSpan.FullLine) {
                                    NoMoreDataComponent()
                                }
                            }
                        }
                    }

                    // 错误状态 - 在网格外部显示
                    uiState.error?.let { error ->
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = error,
                                color = Color.Red.copy(alpha = 0.8f),
                                fontSize = 14.sp,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 顶部过滤标签栏和图标
 */
@Composable
fun TopFilterTabsWithIcons(
    uiState: HomeUiState,
    tagList: List<BroadcasterWallTag>,
    selectedTagIndex: Int,
    onTagSelected: (Int) -> Unit,
    onRankClick: () -> Unit,
    onCountryClick: () -> Unit
) {
    if (tagList.isEmpty()) return

    Column(
        modifier = Modifier
            .padding(top = 45.dp)
            .fillMaxWidth()
            .height(85.dp)

    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 标签栏 - 恢复原来的 LazyRow
            LazyRow(
                modifier = Modifier.padding(top = 25.dp).weight(1f).fillMaxHeight(),
            ) {
                itemsIndexed(tagList) { index, tag ->
                    TagTab(
                        tag = tag,
                        isSelected = index == selectedTagIndex,
                        onClick = { onTagSelected(index) }
                    )
                }
            }

            // 右侧图标
            Box(
                modifier = Modifier.padding(top = 15.dp).offset(x = (-10).dp)
                    .background(
                        Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFF27152E).copy(alpha = 0.90f),
                            Color(0xFF27152E).copy(alpha = 0.0f)
                        ))
                    ).padding(start = 10.dp)
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(30.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    // 排行榜图标
                    ImageLoader.LoadImage(
                        imageUrl = R.mipmap.rank_icon,
                        contentDescription = "Rank",
                        modifier = Modifier.size(44.dp).noRippleClickable(onClick = onRankClick)
                    )

                    // 国家图标
                    Box(
                        modifier = Modifier
                            .background(
                                color = Color.White.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(28.dp)
                            )
                            .padding(horizontal = 16.dp, vertical = 8.dp)
                            .noRippleClickable (onClick = {onCountryClick()}),
                        contentAlignment = Alignment.Center
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(11.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // 国家图标 - 显示国旗或默认图标
                            if (uiState.selectedRegion.isNotEmpty()) {
                                // 显示选中国家的国旗
                                Box(
                                    modifier = Modifier.size(40.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = getCountryFlag(uiState.selectedRegion),
                                        fontSize = 30.sp,
                                        textAlign = TextAlign.Center,
                                    )
                                }
                            } else {
                                // 显示默认的地球图标（All状态）
                                Box(
                                    modifier = Modifier.size(40.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "🌍",
                                        fontSize = 20.sp,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }

                            Icon(
                                painter = painterResource(id = R.drawable.ic_arrow_down),
                                contentDescription = "Dropdown Arrow",
                                modifier = Modifier.size(width = 20.dp, height = 15.dp),
                                tint = Color.White.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))
    }
}


/**
 * 主标签
 */
@Composable
fun TagTab(
    tag: BroadcasterWallTag,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    // 动画化的颜色和字体大小
    val animatedTextColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFFE5C98B) else Color.White.copy(alpha = 0.6f),
        animationSpec = tween(durationMillis = 300),
        label = "text_color"
    )

    val animatedFontSize by animateFloatAsState(
        targetValue = if (isSelected) 42f else 32f,
        animationSpec = tween(durationMillis = 300),
        label = "font_size"
    )

    Box(
        modifier = Modifier
            .noRippleClickable(onClick = onClick)
            .padding(horizontal = 19.dp)
            .height(60.dp), // 固定高度确保对齐
        contentAlignment = if (isSelected) Alignment.BottomCenter else Alignment.Center // 选中时底部对齐，未选中时居中
    ) {
        Column (
            verticalArrangement = if (isSelected) Arrangement.Bottom else Arrangement.Center, // 选中时底部对齐，未选中时居中
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = tag.showTagName.ifEmpty { tag.tagName },
                color = animatedTextColor, // 使用动画颜色
                fontSize = animatedFontSize.sp, // 使用动画字体大小
                fontWeight = FontWeight.SemiBold
            )

            // 选中指示器动画
            AnimatedVisibility(
                visible = isSelected,
                enter = fadeIn(animationSpec = tween(300)) + slideInHorizontally(
                    initialOffsetX = { it / 2 },
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + slideOutHorizontally(
                    targetOffsetX = { it / 2 },
                    animationSpec = tween(200)
                )
            ) {
                ImageLoader.LoadImage(
                    imageUrl = R.mipmap.tab_select_img,
                    modifier = Modifier.size(95.dp,37.dp).offset(y = (-5).dp),
                    contentDescription = "bg",
                )
            }
        }

    }
}

/**
 * 子标签栏
 */
@Composable
fun SubTagTabs(
    selectedTag: BroadcasterWallTag?,
    selectedSubTagIndex: Int,
    onSubTagSelected: (Int) -> Unit
) {
    // 如果没有选中标签、子标签为空、或者只有一个"all"标签时，隐藏子标签栏
    if (selectedTag == null ||
        selectedTag.subTagList.isEmpty() ||
        (selectedTag.subTagList.size == 1 && selectedTag.subTagList.first().equals("all", ignoreCase = true))
    ) return



    Column(
        modifier = Modifier
            .padding(top = 10.dp)
            .fillMaxWidth()
            .padding(bottom = 8.dp)
    ) {
        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 15.dp),
            horizontalArrangement = Arrangement.spacedBy(30.dp),
            contentPadding = PaddingValues(horizontal = 8.dp)
        ) {
            itemsIndexed(selectedTag.subTagList) { index, subTag ->
                SubTagTab(
                    subTag = subTag,
                    isSelected = index == selectedSubTagIndex,
                    onClick = { onSubTagSelected(index) }
                )
            }
        }
    }
}

/**
 * 子标签
 */
@Composable
fun SubTagTab(
    subTag: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {

    val gradient =if (isSelected) Brush.horizontalGradient(
        listOf(Color(0xFFD6B979), Color(0xFFE6CA8C))
    ) else Brush.horizontalGradient(
        listOf(Color.White.copy(alpha = 0.1f), Color.White.copy(alpha = 0.1f))
    )
    val textColor = if (isSelected) Color(0xFF3A2E12) else Color.White.copy(alpha = 0.6f)

    Box(
        modifier = Modifier
            .background(shape = RoundedCornerShape(40.dp), brush = gradient)
            .clickable(onClick = onClick)
            .padding(horizontal = 20.dp, vertical = 5.dp),
        contentAlignment = Alignment.Center
    ){
        Text(
            text = subTag,
            color = textColor,
            fontSize = 28.sp,
            fontWeight = FontWeight.SemiBold,
            lineHeight = 40.sp
        )
    }
}

/**
 * Banner 轮播
 */
@Composable
fun BannerCarousel(
    banners: List<BannerInfo>,
    onBannerClick: (BannerInfo) -> Unit
) {
    if (banners.isEmpty()) return

    val pagerState = rememberPagerState(pageCount = { banners.size })

    // 自动轮播
    LaunchedEffect(pagerState, banners.size) {
        if (banners.size > 1) {
            while (true) {
                delay(10000) // 10秒轮播一次
                val nextPage = (pagerState.currentPage + 1) % banners.size
                pagerState.animateScrollToPage(nextPage)
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(200.dp)
            .padding(top = 20.dp, bottom = 10.dp)
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable { onBannerClick(banners[page]) }
            ) {
                ImageLoader.LoadImage(
                    imageUrl = banners[page].pic,
                    contentDescription = "Banner ${page + 1}",
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }

        // 指示器
        if (banners.size > 1) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 16.dp)
            ) {
                PagerIndicator(
                    pagerState = pagerState,
                    pageCount = banners.size,
                    activeColor = Color(0xFFE5C98B),
                    inactiveColor = Color.White.copy(alpha = 0.5f)
                )
            }
        }
    }
}

/**
 * 自定义分页指示器
 */
@Composable
private fun PagerIndicator(
    pagerState: PagerState,
    pageCount: Int,
    activeColor: Color = Color.White,
    inactiveColor: Color = Color.White.copy(alpha = 0.4f),
    indicatorSize: Dp = 12.dp,
    spacing: Dp = 11.dp
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(spacing),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(pageCount) { index ->
            val isActive = pagerState.currentPage == index
            Box(
                modifier = Modifier
                    .size(indicatorSize)
                    .clip(CircleShape)
                    .background(if (isActive) activeColor else inactiveColor)
            )
        }
    }
}

/**
 * 主播网格项
 */
@Composable
fun BroadcasterItem(
    broadcaster: BroadcasterItem,
    onItemClick: () -> Unit,
    onCallClick: () -> Unit
) {
    // 使用 remember 缓存计算结果，避免重复计算
    val statusColor = remember(broadcaster.status) {
        broadcaster.status.getStatusColor()
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(0.75f) // 4:3 宽高比
            .clip(RoundedCornerShape(15.dp))
            .clickable(onClick = onItemClick)
    ) {
        // 背景图片
        ImageLoader.LoadImage(
            imageUrl = broadcaster.avatar ?: broadcaster.avatarUrl ?: "",
            contentDescription = "Broadcaster ${broadcaster.nickname}",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize(),
        )

        // 渐变遮罩层，让底部文字更清晰
        Box(
            modifier = Modifier.fillMaxWidth().height(height = 100.dp)
                .align(Alignment.BottomCenter)
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Black
                        ),
                        startY = 0f,
                        endY = Float.POSITIVE_INFINITY
                    )
                )
        )

        // 直播标记（单独显示）
        if (broadcaster.activityTagUrl?.isNotEmpty() == true) {
            Box(
                modifier = Modifier
                    .padding(top = 10.dp)
                    .background(Color.Transparent)
                    .align(Alignment.TopEnd)
            ) {
                ImageLoader.LoadImage(
                    imageUrl = broadcaster.activityTagUrl,
                    contentDescription = "Live Badge",
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.size(169.dp, 41.dp)
                )
            }
        }

        // 底部信息区域
        Column(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            // 主播名字和状态圆点
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                // 在线状态指示器（圆点）
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clip(CircleShape)
                        .background(statusColor)
                )

                Spacer(modifier = Modifier.width(8.dp))

                // 主播名字
                Text(
                    text = broadcaster.nickname,
                    color = Color.White,
                    fontSize = 28.sp,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.widthIn(max = 168.dp),
                    lineHeight = 40.sp
                )
            }

            // 年龄和国家信息
            if (broadcaster.age > 0 || broadcaster.country.isNotEmpty()) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {

                    Icon(
                        painter = if (broadcaster.gender == 1) painterResource(id = R.drawable.profile_male_icon) else painterResource(id = R.drawable.profile_female_icon),
                        contentDescription = "gender",
                        tint = Color.Unspecified,
                        modifier = Modifier.size(20.dp).padding(top = 5.dp)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    if (broadcaster.age > 0) {
                        Text(
                            text = "${broadcaster.age}",
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 22.sp,
                            lineHeight = 30.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }

                    if (broadcaster.age > 0 && broadcaster.country.isNotEmpty()) {
                        Text(
                            text = " • ",
                            color = Color.White.copy(alpha = 0.6f),
                            fontSize = 22.sp,
                            lineHeight = 30.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }

                    if (broadcaster.country.isNotEmpty()) {
                        Text(
                            text = broadcaster.country,
                            color = Color.White.copy(alpha = 0.6f),
                            fontSize = 22.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            lineHeight = 30.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }
                }
            }
        }


        // 通话按钮
        IconButton(
            onClick = onCallClick,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .size(137.dp)
        ) {
            ImageLoader.LoadImage(
                imageUrl =  if (broadcaster.isCallable()) R.mipmap.call_icon else R.mipmap.call_not_icon,
                contentDescription = "Call",
                modifier = Modifier.size(137.dp),
                contentScale = ContentScale.FillBounds
            )
        }
    }
}





/**
 * 判断主播是否可以通话
 */
private fun BroadcasterItem.isCallable(): Boolean {
    return BroadcasterStatusUtils.isOnline(this.status)
}

/**
 * 没有更多数据组件
 */
@Composable
private fun NoMoreDataComponent() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 5.dp, horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 主要文字
            Text(
                text = "没有更多主播数据",
                color = Color.White.copy(alpha = 0.9f),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(6.dp))
        }
    }
}

/**
 * 判断主播是否离线
 */
private fun BroadcasterItem.isOffline(): Boolean {
    return this.status == ApiConstants.BroadcasterStatus.STATUS_OFFLINE || this.status == null
}

/**
 * 获取国家旗帜emoji
 */
private fun getCountryFlag(countryCode: String): String {
    return when (countryCode.uppercase()) {
        "" -> "🌍"      // All - 地球图标
        "CO" -> "🇨🇴"   // Colombia - 哥伦比亚
        "MA" -> "🇲🇦"   // Morocco - 摩洛哥
        "BR" -> "🇧🇷"   // Brazil - 巴西
        "VE" -> "🇻🇪"   // Venezuela - 委内瑞拉
        "VN" -> "🇻🇳"   // Vietnam - 越南
        "UA" -> "🇺🇦"   // Ukraine - 乌克兰
        "PH" -> "🇵🇭"   // Philippines - 菲律宾
        "IN" -> "🇮🇳"   // India - 印度
        "TH" -> "🇹🇭"   // Thailand - 泰国
        "PE" -> "🇵🇪"   // Peru - 秘鲁
        "EC" -> "🇪🇨"   // Ecuador - 厄瓜多尔
        "TR" -> "🇹🇷"   // Turkey - 土耳其
        "AR" -> "🇦🇷"   // Argentina - 阿根廷
        "AZ" -> "🇦🇿"   // Azerbaijan - 阿塞拜疆
        "US" -> "🇺🇸"   // United States - 美国
        "RU" -> "🇷🇺"   // Russia - 俄罗斯
        "ID" -> "🇮🇩"   // Indonesia - 印度尼西亚
        else -> "🏳️"    // 默认旗帜
    }
}