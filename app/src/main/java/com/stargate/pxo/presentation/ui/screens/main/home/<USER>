# HomeScreen 国家选择功能完整集成

## 🎯 集成目标

完善 HomeScreen 的国家选择功能，确保：
1. **UI 显示正确** - 国家图标显示为对应的国旗文本
2. **状态同步** - 正确接收和处理国家选择结果
3. **参数传递** - 跳转时带上当前选中的国家

## 🔧 具体修改

### 1. **国家图标显示修改**

#### 修改前 ❌ - 固定图片
```kotlin
ImageLoader.LoadImage(
    imageUrl = R.mipmap.country_icon,
    contentDescription = "Country",
    modifier = Modifier.size(40.dp)
)
```

#### 修改后 ✅ - 动态国旗显示
```kotlin
// 国家图标 - 显示国旗或默认图标
if (uiState.selectedRegion.isNotEmpty()) {
    // 显示选中国家的国旗
    Box(
        modifier = Modifier.size(40.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = getCountryFlag(uiState.selectedRegion),
            fontSize = 20.sp,
            textAlign = TextAlign.Center
        )
    }
} else {
    // 显示默认的地球图标（All状态）
    Box(
        modifier = Modifier.size(40.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "🌍",
            fontSize = 20.sp,
            textAlign = TextAlign.Center
        )
    }
}
```

### 2. **添加 getCountryFlag 函数**

```kotlin
/**
 * 获取国家旗帜emoji
 */
private fun getCountryFlag(countryCode: String): String {
    return when (countryCode.uppercase()) {
        "" -> "🌍"      // All - 地球图标
        "CO" -> "🇨🇴"   // Colombia - 哥伦比亚
        "MA" -> "🇲🇦"   // Morocco - 摩洛哥
        "BR" -> "🇧🇷"   // Brazil - 巴西
        "VE" -> "🇻🇪"   // Venezuela - 委内瑞拉
        "VN" -> "🇻🇳"   // Vietnam - 越南
        "UA" -> "🇺🇦"   // Ukraine - 乌克兰
        "PH" -> "🇵🇭"   // Philippines - 菲律宾
        "IN" -> "🇮🇳"   // India - 印度
        "TH" -> "🇹🇭"   // Thailand - 泰国
        "PE" -> "🇵🇪"   // Peru - 秘鲁
        "EC" -> "🇪🇨"   // Ecuador - 厄瓜多尔
        "TR" -> "🇹🇷"   // Turkey - 土耳其
        "AR" -> "🇦🇷"   // Argentina - 阿根廷
        "AZ" -> "🇦🇿"   // Azerbaijan - 阿塞拜疆
        "US" -> "🇺🇸"   // United States - 美国
        "RU" -> "🇷🇺"   // Russia - 俄罗斯
        "ID" -> "🇮🇩"   // Indonesia - 印度尼西亚
        else -> "🏳️"    // 默认旗帜
    }
}
```

### 3. **跳转参数传递修改**

#### 修改前 ❌ - 不传递当前状态
```kotlin
onCountryClick = {
    globalNavController?.navigate("country_selection")
}
```

#### 修改后 ✅ - 传递当前选中的国家
```kotlin
onCountryClick = {
    // 使用全局导航跳转到国家选择页面，带上当前选中的国家
    val currentCountry = if (uiState.selectedRegion.isEmpty()) null else uiState.selectedRegion
    globalNavController?.navigate("country_selection?initialCountry=${currentCountry ?: ""}")
}
```

### 4. **国家选择结果处理优化**

#### 修改前 ❌ - 简单处理
```kotlin
val selectedCountry = savedStateHandle.get<CountryRegion>("selected_country")
if (selectedCountry != null) {
    viewModel.selectCountryRegion(selectedCountry)
}
```

#### 修改后 ✅ - 完整的"All"状态处理
```kotlin
val selectedCountry = savedStateHandle.get<CountryRegion>("selected_country")
if (selectedCountry != null) {
    LogUtil.d("HomeScreen", "接收到国家选择结果: ${selectedCountry.name} (${selectedCountry.code})")
    // 如果选中的是"All"（code为空），则传递空的CountryRegion
    if (selectedCountry.code.isEmpty()) {
        // 选中"All"，清空地区筛选
        viewModel.selectCountryRegion(CountryRegion(code = "", name = "All", flag = "🌍"))
    } else {
        // 选中具体国家
        viewModel.selectCountryRegion(selectedCountry)
    }
    // 清除结果，避免重复处理
    savedStateHandle.remove<CountryRegion>("selected_country")
}
```

## 📋 功能流程

### 完整的国家选择流程：

#### 1. **初始状态**
```
HomeScreen 显示: 🌍 (地球图标，表示All状态)
uiState.selectedRegion = ""
```

#### 2. **用户点击国家选择**
```
点击国家图标
    ↓
跳转到 CountrySelectionScreen
参数: initialCountry="" (空字符串表示All)
    ↓
CountrySelectionScreen 显示 "🌍 All" 为选中状态
```

#### 3. **用户选择具体国家**
```
用户选择 "🇺🇸 United States"
    ↓
返回 CountryRegion(code="US", name="United States", flag="🇺🇸")
    ↓
HomeScreen 接收结果
    ↓
viewModel.selectCountryRegion(selectedCountry)
    ↓
uiState.selectedRegion = "US"
    ↓
UI 更新: 显示 🇺🇸 图标
```

#### 4. **用户再次选择"All"**
```
用户选择 "🌍 All"
    ↓
返回 CountryRegion(code="", name="All", flag="🌍")
    ↓
HomeScreen 接收结果，检测到 code 为空
    ↓
viewModel.selectCountryRegion(CountryRegion(code="", name="All", flag="🌍"))
    ↓
uiState.selectedRegion = ""
    ↓
UI 更新: 显示 🌍 图标
```

## 🎨 UI 显示效果

### 不同状态下的国家图标显示：

#### All 状态（默认）：
```
┌─────────────────────────────────────────┐
│ [标签1] [标签2] [标签3]    🌍 ↓        │
└─────────────────────────────────────────┘
```

#### 选中美国：
```
┌─────────────────────────────────────────┐
│ [标签1] [标签2] [标签3]    🇺🇸 ↓        │
└─────────────────────────────────────────┘
```

#### 选中巴西：
```
┌─────────────────────────────────────────┐
│ [标签1] [标签2] [标签3]    🇧🇷 ↓        │
└─────────────────────────────────────────┘
```

### 状态同步表：

| HomeScreen 状态 | 显示图标 | CountrySelectionScreen 选中项 | 传递参数 |
|-----------------|----------|------------------------------|----------|
| `selectedRegion = ""` | 🌍 | "🌍 All" | `initialCountry=""` |
| `selectedRegion = "US"` | 🇺🇸 | "🇺🇸 United States" | `initialCountry="US"` |
| `selectedRegion = "BR"` | 🇧🇷 | "🇧🇷 Brazil" | `initialCountry="BR"` |

## ✅ 集成优势

### 1. **视觉一致性**
- ✅ **统一的国旗显示** - HomeScreen 和 CountrySelectionScreen 使用相同的国旗文本
- ✅ **状态同步** - 选中的国家在两个页面间正确同步
- ✅ **直观反馈** - 用户能立即看到当前选中的国家

### 2. **用户体验**
- ✅ **状态保持** - 跳转到选择页面时显示当前选中项
- ✅ **操作连贯** - 选择后立即在主页面看到变化
- ✅ **逻辑清晰** - "All"状态和具体国家状态处理一致

### 3. **代码质量**
- ✅ **函数复用** - getCountryFlag 函数与 CountrySelectionScreen 保持一致
- ✅ **状态管理** - 正确处理空字符串和具体国家代码
- ✅ **错误处理** - 包含日志和异常情况处理

## 🔧 技术实现细节

### 1. **条件渲染**
```kotlin
if (uiState.selectedRegion.isNotEmpty()) {
    // 显示具体国家国旗
    Text(text = getCountryFlag(uiState.selectedRegion))
} else {
    // 显示默认地球图标
    Text(text = "🌍")
}
```

### 2. **参数传递**
```kotlin
val currentCountry = if (uiState.selectedRegion.isEmpty()) null else uiState.selectedRegion
globalNavController?.navigate("country_selection?initialCountry=${currentCountry ?: ""}")
```

### 3. **状态处理**
```kotlin
if (selectedCountry.code.isEmpty()) {
    // All 状态
    viewModel.selectCountryRegion(CountryRegion(code = "", name = "All", flag = "🌍"))
} else {
    // 具体国家
    viewModel.selectCountryRegion(selectedCountry)
}
```

## 🚀 总结

**HomeScreen 国家选择功能完整集成完成！**

### 实现的功能：
1. ✅ **动态国旗显示** - 根据选中状态显示对应国旗或地球图标
2. ✅ **状态正确传递** - 跳转时带上当前选中的国家
3. ✅ **完整的结果处理** - 正确处理"All"和具体国家的选择结果
4. ✅ **视觉一致性** - 与 CountrySelectionScreen 保持一致的显示效果

### 用户体验：
- 🎯 **直观反馈** - 国家图标实时反映当前选择
- 🔄 **状态同步** - 选择页面正确显示当前状态
- 📱 **操作流畅** - 选择后立即看到变化
- ✨ **视觉统一** - 两个页面使用相同的国旗显示

现在 HomeScreen 的国家选择功能已经完整集成，用户可以看到当前选中的国家国旗，并且状态会在页面间正确同步！
