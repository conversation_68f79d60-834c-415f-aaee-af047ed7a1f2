package com.stargate.pxo.presentation.ui.screens.country

import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stargate.pxo.R
import com.stargate.pxo.data.model.CountryRegion
import com.stargate.pxo.presentation.ui.navigation.NavigationManager
import com.stargate.pxo.presentation.ui.theme.noRippleClickable

/**
 * 国家选择页面
 */
@Composable
fun CountrySelectionScreen(
    onCountrySelected: (CountryRegion) -> Unit,
    onBack: () -> Unit,
    initialCountryCode: String? = null, // 初始选中的国家代码
    viewModel: CountrySelectionViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    // 设置初始选中的国家
    LaunchedEffect(initialCountryCode) {
        viewModel.setInitialSelectedCountry(initialCountryCode)
    }

    val backgroundColor = Color(0xFF27152E)
    
    // 监听导航事件
    LaunchedEffect(viewModel) {
        viewModel.navigationEvent.collect { event ->
            when (event) {
                is CountryNavigationEvent.NavigateBackWithResult -> {
                    onCountrySelected(event.country)
                }
                is CountryNavigationEvent.NavigateBack -> {
                    onBack()
                }
            }
        }
    }

    // 处理返回键 - 使用智能返回
    BackHandler {
        NavigationManager.smartPopBack()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .statusBarsPadding() // 添加状态栏 padding
            .navigationBarsPadding() // 添加底部导航栏 padding
    ) {
        // 顶部标题栏
        TopAppBar()

        // 主要内容
        when {
            uiState.isLoading -> {
                LoadingContent()
            }
            uiState.error != null -> {
                ErrorContent(
                    error = uiState.error?: "Unknown error",
                    onRetry = { viewModel.retry() }
                )
            }
            else -> {
                CountryList(
                    countries = uiState.countryRegions,
                    selectedRegion = uiState.selectedRegion,
                    onCountryClick = { country ->
                        viewModel.selectCountryRegion(country)
                    }
                )
            }
        }
    }
}

/**
 * 顶部应用栏
 */
@Composable
private fun TopAppBar(
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 9.dp, top = 37.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier.size(60.dp).noRippleClickable(onClick = { NavigationManager.smartPopBack() })
        ) {
            Icon(
                painter = painterResource(id = R.drawable.nav_btn_black_icon),
                contentDescription = "Back",
                tint = Color.White
            )
        }
        
        Text(
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            text = "Choose region",
            color = Color.White,
            fontSize = 38.sp,
            fontWeight = FontWeight.Medium,
            lineHeight = 53.sp
        )
    }
}

/**
 * 加载中内容
 */
@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            color = Color.White,
            modifier = Modifier.size(48.dp)
        )
    }
}

/**
 * 错误内容
 */
@Composable
private fun ErrorContent(
    error: String,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = error,
            color = Color.Red.copy(alpha = 0.8f),
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = onRetry,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.White.copy(alpha = 0.1f)
            )
        ) {
            Text(
                text = "Retry",
                color = Color.White
            )
        }
    }
}

/**
 * 国家列表
 */
@Composable
private fun CountryList(
    countries: List<CountryRegion>,
    selectedRegion: String,
    onCountryClick: (CountryRegion) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(vertical = 8.dp)
    ) {
        items(countries) { country ->
            CountryItem(
                country = country,
                isSelected = country.code == selectedRegion,
                onClick = { onCountryClick(country) }
            )
        }
    }
}

/**
 * 国家选项
 */
@Composable
private fun CountryItem(
    country: CountryRegion,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // 外层 Box 用于选中状态的背景
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(if (isSelected) Color.Black else Color.Transparent)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { onClick() }
                    .padding(32.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 国旗 - 居中显示
                Box(
                    modifier = Modifier.size(44.dp, 58.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = country.flag,
                        fontSize = 24.sp,
                        textAlign = TextAlign.Center
                    )
                }

                // 国家名称
                Text(
                    text = country.name,
                    color = Color.White,
                    fontSize = 30.sp,
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier.padding(start = 15.dp).weight(1f),
                    lineHeight = 42.sp
                )

                // 选中指示器
                if (isSelected) {
                    Icon(
                        painter = painterResource(id = R.drawable.selected_icon),
                        contentDescription = "Selected",
                        tint = Color.Unspecified,
                        modifier = Modifier.size(29.dp, 21.dp)
                    )
                }
            }
        }

        // 分割线
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(1.dp)
                .background(Color.White.copy(alpha = 0.1f))
        )
    }
}
