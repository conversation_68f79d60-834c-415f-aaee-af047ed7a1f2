package com.stargate.pxo.presentation.ui.screens.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.stargate.pxo.R
import com.stargate.pxo.common.util.ToastUtil
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.presentation.ui.screens.webview.WebViewType
import com.stargate.pxo.presentation.ui.view.GlobalLoadingManager
import com.stargate.pxo.presentation.ui.view.ImageLoader
import androidx.compose.foundation.interaction.MutableInteractionSource
import android.os.Handler
import android.os.Looper

/**
 * Login Screen
 * Displays login page with logo, greeting, login button and terms checkbox
 */
@Composable
fun LoginScreen(
    navController: NavController,
    viewModel: LoginViewModel = hiltViewModel()
) {
    val state by viewModel.uiState.collectAsState()
    var termsAccepted by rememberSaveable { mutableStateOf(false) }

    // 显示欢迎对话框的状态 - 使用 rememberSaveable 保持状态
    var showWelcomeDialog by rememberSaveable { mutableStateOf(true) }
    
    // Monitor login success
    LaunchedEffect(state.isLoggedIn) {
        if (state.isLoggedIn) {
            navController.navigate("main") {
                popUpTo("login") { inclusive = true }
            }
        }
    }
    
    // Background with system bar padding
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)
    ) {
        // Background image
        ImageLoader.LoadImage(
            imageUrl = R.mipmap.bg_login,
            modifier = Modifier.fillMaxSize(),
            contentDescription = stringResource(R.string.login_background),
            contentScale = ContentScale.Crop,
            showLoading = false
        )
        
        // Content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Spacer(modifier = Modifier.height(697.dp))

            Text(
                text = "Hi,Gimi",
                fontSize = 60.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFFE5C98B),
                modifier = Modifier.height(84.dp)
            )

            Spacer(modifier = Modifier.height(300.dp))

            val tips = stringResource(R.string.please_accept_terms)
            // Login Button
            Button(
                onClick = { if (termsAccepted){
                    viewModel.login()
                } else{
                    showWelcomeDialog = true
                    ToastUtil.showInfo(tips)
                } },
                contentPadding = PaddingValues(), // 去除内边距干扰背景
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent // 设置透明背景
                ),
                // 移除 enabled 属性，让按钮始终可点击
                modifier = Modifier
                    .padding(horizontal = 80.dp)
                    .fillMaxWidth()
                    .height(98.dp)
                    .background(
                        shape = RoundedCornerShape(49.dp),
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0xFFD6B979),
                                Color(0xFFE6CA8C)
                            )
                        ),
                    ),

            ) {
                Text(
                    text = stringResource(R.string.login),
                    fontSize = 42.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF3A2E12)
                )
            }
            
            Spacer(modifier = Modifier.height(40.dp))
            
            // Terms and Conditions
            Box(
                modifier = Modifier.padding(horizontal = 80.dp).fillMaxWidth(),
                contentAlignment = Alignment.Center,
            ) {
                Row(
                    horizontalArrangement = Arrangement.Center,
                ) {
                    CustomCheckbox(
                        checked = termsAccepted,
                        onCheckedChange = {
                            termsAccepted = it
                            viewModel.setTermsAccepted(it)
                        }
                    )

                    val annotatedString = buildAnnotatedString {
                        append(stringResource(R.string.terms_prefix))
                        append(" ")

                        pushStringAnnotation(tag = "TERMS", annotation = "terms")
                        withStyle(style = SpanStyle(
                            color = Color(0xFFD4C19C),
                            textDecoration = TextDecoration.Underline
                        )) {
                            append(stringResource(R.string.terms_conditions))
                        }
                        pop()

                        append(" ")
                        append(stringResource(R.string.terms_and))
                        append(" ")

                        pushStringAnnotation(tag = "PRIVACY", annotation = "privacy")
                        withStyle(style = SpanStyle(
                            color = Color(0xFFD4C19C),
                            textDecoration = TextDecoration.Underline
                        )) {
                            append(stringResource(R.string.privacy_policy))
                        }
                        pop()
                    }

                    // 使用 ClickableText 代替 Text + clickable
                    ClickableText(
                        text = annotatedString,
                        style = androidx.compose.ui.text.TextStyle(
                            color = Color.White,
                            fontSize = 24.sp,
                            fontWeight = FontWeight.SemiBold,
                            lineHeight = 37.sp
                        ),
                        modifier = Modifier.padding(start = 10.dp,  top = 5.dp),
                        onClick = { offset ->
                            annotatedString.getStringAnnotations(
                                tag = "TERMS",
                                start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                // 导航到条款页面
                                LogUtil.d("LoginScreen", "点击了条款和条件")
                                navController.navigate("webview/terms") {
                                    launchSingleTop = true
                                }
                            }

                            annotatedString.getStringAnnotations(
                                tag = "PRIVACY",
                                start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                // 导航到隐私政策页面
                                LogUtil.d("LoginScreen", "点击了隐私政策")
                                navController.navigate("webview/privacy") {
                                    launchSingleTop = true
                                }
                            }
                        }
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(0.1f))
        }
        
        // 显示欢迎对话框
        if (showWelcomeDialog) {
            WelcomeDialog(
                navController = navController,
                onDismiss = { 
                    LogUtil.d("LoginScreen", "关闭按钮点击")
                    showWelcomeDialog = false 
                },
                onAgree = {
                    LogUtil.d("LoginScreen", "同意按钮点击")
                    termsAccepted = true
                    viewModel.setTermsAccepted(true)
                    showWelcomeDialog = false
                },
                onDisagree = {
                    LogUtil.d("LoginScreen", "不同意按钮点击")
                    termsAccepted = false
                    viewModel.setTermsAccepted(false)
                    showWelcomeDialog = false
                }
            )
        }
        
        // Show loading or error state if needed
        if (state.isLoading) {
            GlobalLoadingManager.show()
        }else{
            GlobalLoadingManager.hide()
        }
        
        state.error?.let { error ->
            ToastUtil.showError(error)
        }
    }
}

/**
 * 欢迎对话框 - 使用自定义实现而不是系统 Dialog
 */
@Composable
fun WelcomeDialog(
    navController: NavController,
    onDismiss: () -> Unit,
    onAgree: () -> Unit,
    onDisagree: () -> Unit
) {
    // 创建全屏半透明背景
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f))
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {  }, // 点击背景关闭对话框
        contentAlignment = Alignment.Center
    ) {
        // 对话框内容 - 使用 Surface 而不是 Dialog
        Surface(
            modifier = Modifier
                .padding(horizontal = 44.dp)
                .fillMaxWidth()
                .height(564.dp)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) { /* 防止点击传递到背景 */ },
            shape = RoundedCornerShape(16.dp),
            color = Color.Transparent // 透明背景
        ) {
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                // 背景图片
                Image(
                    painter = painterResource(id = R.mipmap.bg_welcome),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.fillMaxSize()
                )

                // 关闭按钮
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.TopEnd
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.close_icon),
                        contentDescription = "Close",
                        tint = Color.Unspecified,
                        modifier = Modifier
                            .padding(top = 37.dp, end = 31.dp)
                            .size(40.dp)
                            .clickable { onDismiss() }
                    )
                }
                
                // 内容
                Column(
                    modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(49.dp))
                    // 标题
                    Text(
                        text = stringResource(R.string.welcome_to_luxvi),
                        color = Color.White,
                        fontSize = 36.sp,
                        fontWeight = FontWeight.SemiBold,
                        textAlign = TextAlign.Center,
                        lineHeight = 50.sp
                    )
                    
                    Spacer(modifier = Modifier.height(30.dp))
                    
                    // 内容
                    val annotatedString = buildAnnotatedString {
                        append(stringResource(R.string.welcome_message))
                        append(" ")
                        
                        pushStringAnnotation(tag = "TERMS", annotation = "terms")
                        withStyle(style = SpanStyle(
                            color = Color(0xFFD4C19C),
                            textDecoration = TextDecoration.Underline
                        )) {
                            append(stringResource(R.string.terms_conditions_quoted))
                        }
                        pop()
                        
                        append(" ")
                        append(stringResource(R.string.terms_and))
                        append(" ")
                        
                        pushStringAnnotation(tag = "PRIVACY", annotation = "privacy")
                        withStyle(style = SpanStyle(
                            color = Color(0xFFD4C19C),
                            textDecoration = TextDecoration.Underline
                        )) {
                            append(stringResource(R.string.privacy_statement_quoted))
                        }
                        pop()
                        
                        append(stringResource(R.string.when_you_click))
                    }
                    
                    // 使用 ClickableText 代替 Text + clickable
                    ClickableText(
                        text = annotatedString,
                        style = androidx.compose.ui.text.TextStyle(
                            color = Color.White,
                            fontSize = 30.sp,
                            fontWeight = FontWeight.SemiBold,
                            lineHeight = 45.sp,
                            textAlign = TextAlign.Center
                        ),
                        modifier = Modifier.padding(horizontal = 40.dp),
                        onClick = { offset ->
                            annotatedString.getStringAnnotations(
                                tag = "TERMS",
                                start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                // 导航到条款页面
                                LogUtil.d("WelcomeDialog", "点击了条款和条件")
                                onDismiss()
                                onAgree()
                                // 使用 Handler 确保对话框关闭后再导航
                                Handler(Looper.getMainLooper()).postDelayed({
                                    navController.navigate("webview/terms") {
                                        launchSingleTop = true
                                    }
                                }, 300)
                            }

                            annotatedString.getStringAnnotations(
                                tag = "PRIVACY",
                                start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                // 导航到隐私政策页面
                                LogUtil.d("WelcomeDialog", "点击了隐私政策")
                                onDismiss()
                                onAgree()
                                // 使用 Handler 确保对话框关闭后再导航
                                Handler(Looper.getMainLooper()).postDelayed({
                                    navController.navigate("webview/privacy") {
                                        launchSingleTop = true
                                    }
                                }, 300)
                            }
                        }
                    )
                    
                    Spacer(modifier = Modifier.weight(1f))
                    
                    // 按钮
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 40.dp, end = 40.dp, bottom = 45.dp),
                        horizontalArrangement = Arrangement.spacedBy(20.dp)
                    ) {
                        // Disagree 按钮
                        Button(
                            onClick = { onDisagree() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.White.copy(0.22f)
                            ),
                            shape = RoundedCornerShape(40.dp),
                            modifier = Modifier
                                .weight(1f)
                                .height(80.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.disagree),
                                color = Color.White.copy(0.60f),
                                fontSize = 36.sp,
                                fontWeight = FontWeight.SemiBold,
                                lineHeight = 50.sp
                            )
                        }
                        
                        // Agree 按钮
                        Button(
                            onClick = { onAgree() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFFE5C98B)
                            ),
                            shape = RoundedCornerShape(40.dp),
                            modifier = Modifier
                                .weight(1f)
                                .height(80.dp)
                        ) {
                            Text(
                                text = stringResource(R.string.agree),
                                color = Color(0xFF3A2E12),
                                fontSize = 36.sp,
                                fontWeight = FontWeight.SemiBold,
                                lineHeight = 50.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CustomCheckbox(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    val imageRes = if (checked) R.drawable.login_btn_selected else R.drawable.login_btn_unselected

    Image(
        painter = painterResource(id = imageRes),
        contentDescription = if (checked) "Checked" else "Unchecked",
        modifier = modifier
            .padding(top = 7.dp)
            .size(24.dp)
            .clickable { 
                // 直接切换状态，不显示提示
                onCheckedChange(!checked) 
            }
    )
}
