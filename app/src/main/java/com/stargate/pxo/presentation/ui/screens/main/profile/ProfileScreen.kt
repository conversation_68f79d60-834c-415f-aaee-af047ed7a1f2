package com.stargate.pxo.presentation.ui.screens.main.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.stargate.pxo.R
import com.stargate.pxo.presentation.ui.view.ImageLoader

/**
 * 个人资料页面
 */
@Composable
fun ProfileScreen(
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF2D1B3D),
                        Color(0xFF1A0F2E)
                    )
                )
            )
    ) {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 用户信息卡片
            item {
                UserInfoCard(
                    uiState = uiState,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // VIP卡片
            item {
                VipCard(
                    uiState = uiState,
                    onVipClick = viewModel::onVipClick,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // 活动横幅
            item {
                PromotionBanner(
                    onClick = viewModel::onPromotionBannerClick,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            // 菜单项列表
            items(viewModel.menuItems) { menuItem ->
                MenuItemCard(
                    menuItem = menuItem,
                    onClick = { viewModel.onMenuItemClick(menuItem) },
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // 加载状态
        if (uiState.isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center),
                color = Color(0xFFE5C98B)
            )
        }
    }
}

/**
 * 用户信息卡片
 */
@Composable
private fun UserInfoCard(
    uiState: ProfileUiState,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF3D2A4A).copy(alpha = 0.8f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 用户头像
            Box(
                modifier = Modifier.size(60.dp)
            ) {
                if (uiState.userAvatar.isNotEmpty()) {
                    ImageLoader.LoadImage(
                        imageUrl = uiState.userAvatar,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )
                } else {
                                         // 默认头像
                     Image(
                         painter = painterResource(id = R.drawable.profile_pic_icon),
                         contentDescription = "User Avatar",
                         modifier = Modifier
                             .fillMaxSize()
                             .clip(CircleShape),
                         contentScale = ContentScale.Crop
                     )
                }

                // 等级标签
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .background(
                            Color(0xFFE5C98B),
                            RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 6.dp, vertical = 2.dp)
                ) {
                    Text(
                        text = uiState.userLevel,
                        color = Color.Black,
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))

            // 用户信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = uiState.userName,
                    color = Color.White,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            // 钻石数量
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .background(
                        Color(0xFF4A3452),
                        RoundedCornerShape(12.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 8.dp)
            ) {
                Text(
                    text = uiState.diamondCount,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "My Diamonds",
                    color = Color.White.copy(alpha = 0.7f),
                    fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * VIP卡片
 */
@Composable
private fun VipCard(
    uiState: ProfileUiState,
    onVipClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onVipClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF5E6A3).copy(alpha = 0.9f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
                         // VIP图标
             Icon(
                 painter = painterResource(id = R.drawable.logo_gold),
                 contentDescription = "VIP",
                 modifier = Modifier.size(32.dp),
                 tint = Color(0xFF8B4513)
             )

            Spacer(modifier = Modifier.width(12.dp))

            // VIP文本
            Text(
                text = "VIP",
                color = Color(0xFF8B4513),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.weight(1f))

            // 价格和箭头
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = uiState.vipPrice,
                    color = Color(0xFF8B4513),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.width(8.dp))
                                 Icon(
                     painter = painterResource(id = R.drawable.profile_right_icon),
                     contentDescription = "Arrow",
                     modifier = Modifier.size(16.dp),
                     tint = Color(0xFF8B4513)
                 )
            }
        }
    }
}

/**
 * 活动横幅
 */
@Composable
private fun PromotionBanner(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(
                    Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFF8B4513),
                            Color(0xFFDAA520)
                        )
                    ),
                    RoundedCornerShape(16.dp)
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                                 // 左侧图标
                 Image(
                     painter = painterResource(id = R.drawable.logo_gold),
                     contentDescription = "Coins",
                     modifier = Modifier.size(60.dp)
                 )

                Spacer(modifier = Modifier.width(16.dp))

                // 文本内容
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "Grab Your Coins Now!",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = "Luxvi Limited-Time Giveaway",
                        color = Color.White.copy(alpha = 0.9f),
                        fontSize = 14.sp
                    )
                }
            }
        }
    }
}

/**
 * 菜单项卡片
 */
@Composable
private fun MenuItemCard(
    menuItem: ProfileMenuItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF3D2A4A).copy(alpha = 0.6f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 菜单图标
            Icon(
                painter = painterResource(id = menuItem.icon),
                contentDescription = menuItem.title,
                modifier = Modifier.size(24.dp),
                tint = Color.White
            )

            Spacer(modifier = Modifier.width(16.dp))

            // 菜单标题
            Text(
                text = menuItem.title,
                color = Color.White,
                fontSize = 16.sp,
                modifier = Modifier.weight(1f)
            )

                         // 箭头
             if (menuItem.hasArrow) {
                 Icon(
                     painter = painterResource(id = R.drawable.profile_right_icon),
                     contentDescription = "Arrow",
                     modifier = Modifier.size(16.dp),
                     tint = Color.White.copy(alpha = 0.7f)
                 )
             }
        }
    }
} 