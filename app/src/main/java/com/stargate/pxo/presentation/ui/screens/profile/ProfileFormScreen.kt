package com.stargate.pxo.presentation.ui.screens.profile

import android.annotation.SuppressLint
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.stargate.pxo.R
import com.stargate.pxo.common.util.ToastUtil
import com.stargate.pxo.presentation.ui.theme.OutlinedTextFieldHintText
import com.stargate.pxo.presentation.ui.theme.OutlinedTextFieldText
import com.stargate.pxo.presentation.ui.theme.noRippleClickable
import com.stargate.pxo.presentation.ui.view.DialogManager
import com.stargate.pxo.presentation.ui.view.DialogManagerComponent
import com.stargate.pxo.presentation.ui.view.GlobalLoadingView
import com.stargate.pxo.presentation.ui.view.ImageLoader
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*


@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun ProfileFormScreen(
    navController: NavController,
    viewModel: ProfileViewModel = hiltViewModel()
) {
    val gradient = Brush.horizontalGradient(
        listOf(Color(0xFFD6B979), Color(0xFFE6CA8C))
    )

    val state by viewModel.uiState.collectAsState()
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val focusManager = LocalFocusManager.current
    
    var name by remember { mutableStateOf(state.userInfo?.nickname ?: "") }
    var ageText by remember { mutableStateOf(state.userInfo?.age?.toString() ?: "") }
    var genderValue by remember { mutableStateOf(state.userInfo?.gender?.toString() ?: "0") }
    var inviteCode by remember { mutableStateOf(state.invitationCode) }
    
    // 头像相关状态
    var avatarUri by remember { mutableStateOf<Uri?>(null) }

    // 添加一个刷新键，用于强制重新加载头像
    var avatarRefreshKey by remember { mutableStateOf(0) }
    
    // 监听日期选择器选中的日期
    val selectedDate by DialogManager.selectedDate.collectAsState()
    
    // 格式化日期显示
    val dateFormat = remember { SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()) }
    
    // 在页面首次加载时重置提交成功状态
    LaunchedEffect(Unit) {
        viewModel.resetSubmitSuccess()
    }
    
    // 当日期选择器选中日期变化时，更新年龄文本为日期格式
    LaunchedEffect(selectedDate) {
        ageText = dateFormat.format(selectedDate.time)
    }

    // 监听用户信息变化
    LaunchedEffect(state.userInfo) {
        state.userInfo?.let { userInfo ->
            name = userInfo.nickname
            // 如果用户信息中有生日字段，则使用生日，否则使用年龄
            if (!userInfo.birthday.isNullOrEmpty()) {
                ageText = userInfo.birthday
            } else {
            ageText = userInfo.age.toString()
            }
            genderValue = userInfo.gender.toString()
            // 当用户信息更新时，增加刷新键以强制重新加载头像
            avatarRefreshKey++
        }
    }

    // 监听头像上传状态
    LaunchedEffect(state.avatarUploadState) {
        when (state.avatarUploadState) {
            is AvatarUploadState.Success -> {
                // 上传成功，显示成功提示
                ToastUtil.showSuccess(context.getString(R.string.profile_avatar_upload_success))
                // 增加刷新键以强制重新加载头像
                avatarRefreshKey++
                // 重置上传状态
                viewModel.resetAvatarUploadState()
            }
            is AvatarUploadState.Error -> {
                // 上传失败，显示错误信息
                val errorMessage = (state.avatarUploadState as AvatarUploadState.Error).message
                ToastUtil.showError(context.getString(R.string.profile_avatar_upload_failed, errorMessage))
                // 重置上传状态
                viewModel.resetAvatarUploadState()
            }
            else -> {
                // 其他状态不处理
            }
        }
    }
    
    // 监听提交成功状态，导航到主页
    LaunchedEffect(state.submitSuccess) {
        if (state.submitSuccess) {
            // 导航到主页
            navController.navigate("main") {
                popUpTo("profile_form") { inclusive = true }
            }
        }
    }
    
    // 监听错误状态，显示错误提示
    LaunchedEffect(state.error) {
        state.error?.let { error ->
            ToastUtil.showError(error)
        }
    }

    // 使用Box作为根容器，添加背景图片
    Box(modifier = Modifier.fillMaxSize().background(Color.Transparent)) {
        // 背景图片
        ImageLoader.LoadImage(
            imageUrl = R.mipmap.bg_puxxi,
            contentDescription = stringResource(R.string.profile_background),
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.fillMaxSize()
        )
        
        // 内容区域
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 56.dp)
                .pointerInput(Unit) {
                    detectTapGestures(onTap = {
                        focusManager.clearFocus()
                    })
                },
        ) {
            Spacer(modifier = Modifier.height(120.dp))

            Text(
                text = stringResource(R.string.profile_form_title),
                fontSize = 48.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFFE5C98B),
                lineHeight = 67.sp,
            )

            Spacer(modifier = Modifier.height(40.dp))

            // 头像部分，使用Box包裹并居中
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.Center
            ) {
                // 用户头像
                val avatarUrl = if (state.userInfo?.avatarUrl?.isNotEmpty() == true) {
                    // 优先使用服务器返回的头像URL
                    state.userInfo?.avatarUrl
                } else {
                    // 如果服务器URL为空，则使用本地选择的头像
                    avatarUri?.toString()
                } ?: ""
                
                Box(
                    modifier = Modifier.wrapContentSize(),
                    contentAlignment = Alignment.Center
                ) {

                    // 使用刷新键强制重新加载头像
                    key(avatarUrl, avatarRefreshKey) {
                    ImageLoader.LoadImage(
                        imageUrl = avatarUrl,
                        contentDescription = stringResource(R.string.profile_avatar_description),
                        modifier = Modifier
                            .size(168.dp)
                            .clip(CircleShape)
                    )
                    }
                    
                    // 相机图标
                    Box(
                        modifier = Modifier
                            .size(60.dp)
                            .offset(y = 25.dp)
                            .noRippleClickable(onClick = {
                                DialogManager.showImagePicker(
                                    onImageSelected = { uri ->
                                        avatarUri = uri
                                        viewModel.updateAvatar(uri)
                                    }
                                )
                            })
                            .align(Alignment.BottomCenter)
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.profile_pic_icon),
                            contentDescription = stringResource(R.string.profile_change_avatar),
                            modifier = Modifier.size(60.dp),
                            tint = Color.Unspecified
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(44.dp))

            TitleText(title = stringResource(R.string.profile_name_label))

            Spacer(modifier = Modifier.height(16.dp))

            // Name input
            CustomTextField(
                value = name,
                onValueChange = { name = it },
                placeholder = stringResource(R.string.profile_name_hint),
                modifier = Modifier.fillMaxWidth().height(106.dp)
            )


            Spacer(modifier = Modifier.height(40.dp))


            TitleText(title = stringResource(R.string.profile_birthday))

            Spacer(modifier = Modifier.height(16.dp))

            // 生日输入
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(106.dp)
                    .background(Color.White.copy(alpha = 0.08f), RoundedCornerShape(10.dp))
                    .border(
                        width = 1.dp,
                        color = Color(0xFF3F204C),
                        shape = RoundedCornerShape(10.dp)
                    )
                    .clickable {
                        // 使用DialogManager显示日期选择器，传入用户的生日信息
                        DialogManager.showDatePickerWithBirthday(state.userInfo?.birthday)
                    }
                    .padding(horizontal = 16.dp, vertical = 12.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxSize(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.CenterStart
                    ) {
                        if (ageText.isEmpty()) {
                            Text(
                                text = stringResource(R.string.profile_age_hint),
                                style = OutlinedTextFieldHintText,
                                modifier = Modifier.fillMaxWidth()
                            )
                        } else {
                            Text(
                                text = ageText,
                                style = OutlinedTextFieldText.copy(color = Color.White),
                                modifier = Modifier.fillMaxWidth()
                            )
                        }
                    }
                    
                    Icon(
                        painter = painterResource(id = R.drawable.profile_right_icon),
                        contentDescription = null,
                    )
                }
            }

            Spacer(modifier = Modifier.height(40.dp))

            // Gender Selector
            TitleText(title = stringResource(R.string.profile_gender_label))

            Spacer(modifier = Modifier.height(15.dp))

            Row(
                horizontalArrangement = Arrangement.spacedBy(49.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                GenderOption(
                    label = stringResource(R.string.profile_gender_boy),
                    selected = genderValue == "1",
                    iconResId = R.drawable.profile_male_icon,
                    modifier = Modifier
                        .weight(1f)
                        .clickable { genderValue = "1" },
                    textColor = Color(0xFF4CA9F7)
                )
                
                GenderOption(
                    label = stringResource(R.string.profile_gender_girl),
                    selected = genderValue == "2",
                    iconResId = R.drawable.profile_female_icon,
                    modifier = Modifier
                        .weight(1f)
                        .clickable { genderValue = "2" },
                    textColor = Color(0xFFDE3A3E)
                )
            }

            Spacer(modifier = Modifier.height(40.dp))

            // Gender Selector
            TitleText(title = stringResource(R.string.profile_invitation_label))

            Spacer(modifier = Modifier.height(15.dp))


            // Invitation code
            CustomTextField(
                value = inviteCode,
                onValueChange = { inviteCode = it },
                placeholder = stringResource(R.string.profile_invitation_hit),
                modifier = Modifier.fillMaxWidth().height(106.dp)
            )

            Spacer(modifier = Modifier.height(53.dp))

            // Confirm Button
            Button(
                onClick = { 
                    try {
                        // 更新个人资料
                        viewModel.submitProfile(
                            name = name,
                            age = ageText,
                            gender = genderValue,
                            invitationCode = inviteCode
                        )
                        // 不再直接导航到主页，而是等待提交成功后自动导航
                    } catch (e: Exception) {
                        // 错误处理可以通过ViewModel的状态来显示
                        if (e.message?.isEmpty() != true){
                            ToastUtil.showError(e.message!!)
                        }
                    }
                },
                enabled = !state.isLoading && name.isNotBlank() && ageText.isNotBlank(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.Transparent,
                    disabledContainerColor = Color(0xFF3F204C)
                ),
                contentPadding = PaddingValues(),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(98.dp)
                    .clip(RoundedCornerShape(49.dp))
                    .background(brush = gradient)
            ) {
                Text(
                    stringResource(R.string.profile_confirm_button),
                    color = Color(0xFF3A2E12),
                    fontSize = 42.sp,
                    lineHeight = 59.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
        
        // 添加对话框管理组件
        DialogManagerComponent()
        
    }
}

@Composable
fun TitleText(title: String){
    Text(
        text = title,
        fontSize = 32.sp,
        fontWeight = FontWeight.SemiBold,
        color = Color.White,
        lineHeight = 45.sp,
    )
}

@Composable
fun GenderOption(
    label: String, 
    selected: Boolean, 
    iconResId: Int,
    modifier: Modifier = Modifier,
    textColor: Color
) {
    val borderWidth = if (selected) 2.dp else 0.dp
    val borderColor = if (selected) Color(0xFF766854) else Color.Transparent
    val backgroundBrush = if (selected) {
        Brush.horizontalGradient(
            colors = listOf(Color(0xFF390B4C), Color(0xFF551071))
        )
    } else {
        Brush.horizontalGradient(
            colors = listOf(Color(0xFF3F204C), Color(0xFF3F204C))
        )
    }

    Box(
        modifier = modifier
            .height(108.dp)
            .border(borderWidth, borderColor, RoundedCornerShape(10.dp))
            .background(backgroundBrush, RoundedCornerShape(10.dp)),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                painter = painterResource(id = iconResId),
                contentDescription = null,
                tint = Color.Unspecified
            )
            Spacer(modifier = Modifier.width(20.dp))
            Text(text = label, color = textColor, fontWeight = FontWeight.SemiBold, fontSize = 32.sp, lineHeight = 45.sp)
        }
    }
}

@Composable
fun CustomTextField(
    value: String,
    onValueChange: (String) -> Unit,
    placeholder: String,
    modifier: Modifier = Modifier,
    readOnly: Boolean = false,
    trailingIcon: @Composable (() -> Unit)? = null
) {
    val focusedBorderColor = Color(0xFF766854)
    val unfocusedBorderColor = Color(0xFF3F204C)
    val backgroundColor = Color.White.copy(alpha = 0.08f)
    
    var isFocused by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    
    Box(
        modifier = modifier
            .background(backgroundColor, RoundedCornerShape(10.dp))
            .border(
                width = 1.dp,
                color = if (isFocused) focusedBorderColor else unfocusedBorderColor,
                shape = RoundedCornerShape(10.dp)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.CenterStart
            ) {
                BasicTextField(
                    value = value,
                    onValueChange = onValueChange,
                    textStyle = OutlinedTextFieldText.copy(
                        color = Color.White,
                        textDecoration = TextDecoration.None
                    ),
                    cursorBrush = SolidColor(Color.White),
                    readOnly = readOnly,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .focusRequester(focusRequester)
                        .onFocusChanged { focusState ->
                            isFocused = focusState.isFocused
                        },
                    decorationBox = { innerTextField ->
                        innerTextField()
                    }
                )
                
                // Placeholder
                if (value.isEmpty()) {
                    Text(
                        text = placeholder,
                        style = OutlinedTextFieldHintText,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            // Trailing icon
            trailingIcon?.invoke()
        }
    }
}
