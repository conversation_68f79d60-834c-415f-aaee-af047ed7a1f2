package com.stargate.pxo.presentation.ui.view

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.zIndex
import com.stargate.pxo.R
import com.stargate.pxo.common.util.ImagePickerResult
import com.stargate.pxo.common.util.ImagePickerUtil
import com.stargate.pxo.common.util.PermissionResult
import com.stargate.pxo.common.util.RequestCameraPermission
import com.stargate.pxo.common.util.RequestGalleryPermission
import com.stargate.pxo.common.util.ToastUtil
import com.stargate.pxo.common.util.rememberCameraLauncher
import com.stargate.pxo.common.util.rememberGalleryLauncher
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.*
import android.net.Uri
import android.os.Build
import android.util.Log
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.ui.unit.Dp
import com.stargate.pxo.common.util.PermissionUtil
import kotlin.math.abs

/**
 * 对话框管理类
 * 用于管理和显示各种对话框
 */
object DialogManager {
    
    // 图片选择对话框状态
    private val _showImagePickerDialog = MutableStateFlow(false)
    val showImagePickerDialog: StateFlow<Boolean> = _showImagePickerDialog.asStateFlow()
    
    // 图片选择器回调
    val _onTakePhoto = MutableStateFlow<(() -> Unit)?>(null)
    val _onChooseFromGallery = MutableStateFlow<(() -> Unit)?>(null)
    val _onImageSelected = MutableStateFlow<((Uri) -> Unit)?>(null)
    
    // 生日选择对话框状态
    private val _showDatePickerDialog = MutableStateFlow(false)
    val showDatePickerDialog: StateFlow<Boolean> = _showDatePickerDialog.asStateFlow()
    
    // 选中的日期
    private val _selectedDate = MutableStateFlow(Calendar.getInstance())
    val selectedDate: StateFlow<Calendar> = _selectedDate.asStateFlow()
    
    // 显示图片选择对话框
    fun showImagePicker(
        onTakePhoto: () -> Unit = {},
        onChooseFromGallery: () -> Unit = {},
        onImageSelected: (Uri) -> Unit = {}
    ) {
        _onTakePhoto.value = onTakePhoto
        _onChooseFromGallery.value = onChooseFromGallery
        _onImageSelected.value = onImageSelected
        _showImagePickerDialog.value = true
    }
    
    // 隐藏图片选择对话框
    fun hideImagePicker() {
        _showImagePickerDialog.value = false
    }
    
    // 显示日期选择对话框
    fun showDatePicker(initialDate: Calendar = Calendar.getInstance()) {
        _selectedDate.value = initialDate
        _showDatePickerDialog.value = true
    }
    
    // 显示日期选择对话框，使用字符串格式的生日
    fun showDatePickerWithBirthday(birthday: String?) {
        if (birthday.isNullOrEmpty()) {
            showDatePicker()
            return
        }
        
        try {
            // 尝试解析 "yyyy-MM-dd" 格式的生日字符串
            val parts = birthday.split("-")
            if (parts.size == 3) {
                val year = parts[0].toInt()
                val month = parts[1].toInt() - 1 // 月份从0开始
                val day = parts[2].toInt()
                
                val calendar = Calendar.getInstance()
                calendar.set(year, month, day)
                showDatePicker(calendar)
                return
            }
        } catch (e: Exception) {
            // 解析失败，使用当前日期
        }
        
        showDatePicker()
    }
    
    // 隐藏日期选择对话框
    fun hideDatePicker() {
        _showDatePickerDialog.value = false
    }
    
    // 设置选中的日期
    fun setSelectedDate(year: Int, month: Int, day: Int) {
        val calendar = Calendar.getInstance()
        calendar.set(year, month, day)
        _selectedDate.value = calendar
    }
    
    // 其他对话框状态可以在这里添加
}

/**
 * 图片选择对话框
 */
@Composable
fun ImagePickerDialog(
    onDismiss: () -> Unit,
    onTakePhoto: () -> Unit,
    onChooseFromGallery: () -> Unit
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        val configuration = LocalConfiguration.current
        val screenWidth = configuration.screenWidthDp.dp
        
        Column(
            modifier = Modifier
                .width(screenWidth - 25.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(Color(0xFF3F204C))
        ) {
            // 拍照选项
            DialogOption(
                text = stringResource(R.string.image_picker_take_photo),
                onClick = {
                    onTakePhoto()
                    onDismiss()
                }
            )
            
            // 分割线
            DialogDivider()
            
            // 从相册中选择
            DialogOption(
                text = stringResource(R.string.image_picker_choose_gallery),
                onClick = {
                    onChooseFromGallery()
                    onDismiss()
                }
            )
            
            // 分割线
            DialogDivider()
            
            // 取消
            DialogOption(
                text = stringResource(R.string.image_picker_cancel),
                onClick = onDismiss
            )
        }
    }
}

/**
 * 生日选择对话框
 */
@Composable
fun DatePickerDialog(
    onDismiss: () -> Unit,
    onDateSelected: (Date) -> Unit,
    initialDate: Date = Date()
) {
    val calendar = Calendar.getInstance()
    val currentCalendar = Calendar.getInstance() // 当前日期，用于计算年份范围
    
    // 设置日历为初始日期
    calendar.time = initialDate
    
    val initialYear = calendar.get(Calendar.YEAR)
    val initialMonth = calendar.get(Calendar.MONTH) + 1 // 月份从0开始，显示时+1
    val initialDay = calendar.get(Calendar.DAY_OF_MONTH)
    
    // 使用临时变量存储选择的值，只有在点击Consent按钮后才更新
    var tempSelectedYear by remember { mutableStateOf(initialYear) }
    var tempSelectedMonth by remember { mutableStateOf(initialMonth) }
    var tempSelectedDay by remember { mutableStateOf(initialDay) }
    
    // 根据选择的年月，计算该月的天数
    val daysInMonth = remember(tempSelectedYear, tempSelectedMonth) {
        calendar.set(Calendar.YEAR, tempSelectedYear)
        calendar.set(Calendar.MONTH, tempSelectedMonth - 1) // Calendar.MONTH从0开始
        calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
    }
    
    // 创建年、月、日的列表
    // 注意：年份范围是从当前年份往前150年，而不是从初始年份
    val currentYear = currentCalendar.get(Calendar.YEAR)
    val years = (currentYear downTo currentYear - 150).map { it.toString() }
    val months = (1..12).map { String.format("%02d", it) }
    val days = (1..daysInMonth).map { String.format("%02d", it) }
    
    // 计算初始年份在years列表中的索引
    val initialYearIndex = years.indexOf(initialYear.toString()).coerceAtLeast(0)
    // 月份索引是从1开始的，所以需要减1
    val initialMonthIndex = initialMonth - 1
    // 日期索引也是从1开始的，所以需要减1
    val initialDayIndex = (initialDay - 1).coerceIn(0, days.size - 1)
    
    val stringResource = stringResource(R.string.profile_age_restriction)
    
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Box(
                modifier = Modifier
                    .fillMaxSize()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) { onDismiss() }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .align(Alignment.BottomCenter)
                    .clickable(enabled = false) { /* 防止点击传递到背景 */ }
                    .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                    .background(Color(0xFF3F204C))
            ) {
                // 顶部标题和按钮
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .background(Color(0xFF4A2A5A))
                ) {
                    Text(
                        text = stringResource(R.string.profile_cancel),
                        color = Color.White,
                        fontSize = 16.sp,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(start = 16.dp)
                            .clickable { onDismiss() }
                    )
                    
                    Text(
                        text = stringResource(R.string.profile_consent),
                        color = Color(0xFFFFD700), // 金色
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(end = 16.dp)
                            .clickable {
                                // 检查年龄是否大于18岁
                                val birthCalendar = Calendar.getInstance()
                                birthCalendar.set(tempSelectedYear, tempSelectedMonth - 1, tempSelectedDay)
                                
                                val today = Calendar.getInstance()
                                var age = today.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)
                                
                                // 如果今年的生日还没到，年龄减1
                                if (today.get(Calendar.MONTH) < birthCalendar.get(Calendar.MONTH) ||
                                    (today.get(Calendar.MONTH) == birthCalendar.get(Calendar.MONTH) &&
                                     today.get(Calendar.DAY_OF_MONTH) < birthCalendar.get(Calendar.DAY_OF_MONTH))) {
                                    age--
                                }
                                
                                if (age >= 18) {
                                    val date = Calendar.getInstance().apply {
                                        set(Calendar.YEAR, tempSelectedYear)
                                        set(Calendar.MONTH, tempSelectedMonth - 1)
                                        set(Calendar.DAY_OF_MONTH, tempSelectedDay)
                                    }.time
                                    onDateSelected(date)
                                    onDismiss() // 选择完成后关闭对话框
                                } else {
                                    ToastUtil.showWarning(stringResource)
                                }
                            }
                    )
                }
                
                // 日期选择器标签
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp, bottom = 8.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    Text(
                        text = stringResource(R.string.profile_year),
                        color = Color.White,
                        fontSize = 14.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = stringResource(R.string.profile_month),
                        color = Color.White,
                        fontSize = 14.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = stringResource(R.string.profile_day),
                        color = Color.White,
                        fontSize = 14.sp,
                        modifier = Modifier.weight(1f),
                        textAlign = TextAlign.Center
                    )
                }
                
                // 日期选择器
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                        .padding(bottom = 24.dp)
                ) {
                    DatePickerWheel(
                        items = years,
                        initialSelectedIndex = initialYearIndex,
                        onSelectionChanged = { index -> tempSelectedYear = years[index].toInt() },
                        modifier = Modifier.weight(1f)
                    )
                    
                    DatePickerWheel(
                        items = months,
                        initialSelectedIndex = initialMonthIndex,
                        onSelectionChanged = { index -> tempSelectedMonth = months[index].toInt() },
                        modifier = Modifier.weight(1f)
                    )
                    
                    DatePickerWheel(
                        items = days,
                        initialSelectedIndex = initialDayIndex,
                        onSelectionChanged = { index -> tempSelectedDay = days[index].toInt() },
                        modifier = Modifier.weight(1f)
                    )
                }
            }
        }
    }
}

/**
 * iOS 风格的日期选择器滚轮
 */
@Composable
fun DatePickerWheel(
    items: List<String>,
    initialSelectedIndex: Int,
    onSelectionChanged: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    if (items.isEmpty()) return
    
    val itemHeight = 40.dp
    val visibleItems = 5
    val halfVisibleItems = visibleItems / 2
    
    // 为了能够滚动到列表的开头和结尾，我们在实际列表前后添加空白项
    val paddedItems = List(halfVisibleItems) { "" } + items + List(halfVisibleItems) { "" }
    
    // 调整初始索引以适应填充后的列表
    val paddedInitialIndex = initialSelectedIndex + halfVisibleItems
    
    // 使用状态跟踪当前选中的项
    var selectedIndex by remember { mutableStateOf(paddedInitialIndex) }
    
    // 创建列表状态，初始滚动位置为选中项的位置减去半个可见项数
    val listState = rememberLazyListState(
        initialFirstVisibleItemIndex = maxOf(0, paddedInitialIndex - halfVisibleItems)
    )
    
    val coroutineScope = rememberCoroutineScope()
    val density = LocalDensity.current
    
    // 滚动停止后自动吸附到中间
    LaunchedEffect(listState.isScrollInProgress) {
        if (!listState.isScrollInProgress) {
            val visibleItemsInfo = listState.layoutInfo.visibleItemsInfo
            if (visibleItemsInfo.isEmpty()) return@LaunchedEffect
            
            // 找到距离中心最近的项
            val center = listState.layoutInfo.viewportEndOffset / 2
            var minDistanceToCenter: Float = Float.MAX_VALUE
            var targetIndex = 0
            
            for (item in visibleItemsInfo) {
                val itemCenter = item.offset + item.size / 2
                val distanceToCenter = abs(itemCenter - center)
                
                if (distanceToCenter < minDistanceToCenter) {
                    minDistanceToCenter = distanceToCenter.toFloat()
                    targetIndex = item.index
                }
            }
            
            // 更新选中项
            if (targetIndex in halfVisibleItems until paddedItems.size - halfVisibleItems) {
                val actualIndex = targetIndex - halfVisibleItems
                onSelectionChanged(actualIndex)
                selectedIndex = targetIndex
            }
            
            // 滚动到目标位置
            coroutineScope.launch {
                listState.animateScrollToItem(
                    index = targetIndex,
                    scrollOffset = -(listState.layoutInfo.viewportEndOffset / 2 - with(density) { itemHeight.toPx() }.toInt() / 2)
                )
            }
        }
    }
    
    Box(
        modifier = modifier
            .height(itemHeight * visibleItems)
    ) {
        // 中间固定的高亮背景
        Box(
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxWidth()
                .height(itemHeight)
                .background(Color(0xFF4A2A5A))
        )
        
        // 滚轮项
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize()
        ) {
            itemsIndexed(paddedItems) { index, item ->
                val isSelected = index == selectedIndex
                val isPadding = index < halfVisibleItems || index >= paddedItems.size - halfVisibleItems
                
                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(itemHeight)
                ) {
                    if (!isPadding) {
                        val distance = abs(index - selectedIndex)
                        val alpha = 1f - (distance * 0.2f).coerceAtMost(0.6f)
                        
                    Text(
                        text = item,
                            fontSize = if (isSelected) 20.sp else 16.sp,
                            color = if (isSelected) Color.White else Color.White.copy(alpha = alpha),
                            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
                        textAlign = TextAlign.Center
                    )
                }
            }
            }
        }
        
        // 顶部和底部渐变效果
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(itemHeight * 2)
                .align(Alignment.TopCenter)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF3F204C),
                            Color(0xFF3F204C).copy(alpha = 0.0f)
                        )
                    )
                )
        )
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(itemHeight * 2)
                .align(Alignment.BottomCenter)
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF3F204C).copy(alpha = 0.0f),
                            Color(0xFF3F204C)
                        )
                    )
                )
        )
    }
}

/**
 * 对话框选项
 */
@Composable
private fun DialogOption(
    text: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(60.dp)
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
    }
}

/**
 * 对话框分割线
 */
@Composable
private fun DialogDivider() {
    Divider(
        modifier = Modifier.fillMaxWidth(),
        color = Color.White.copy(alpha = 0.15f),
        thickness = 1.dp
    )
}

/**
 * 对话框管理组件
 * 用于在Compose界面中显示对话框
 */
@Composable
fun DialogManagerComponent() {
    val showImagePicker by DialogManager.showImagePickerDialog.collectAsState()
    val showDatePicker by DialogManager.showDatePickerDialog.collectAsState()
    val selectedDate by DialogManager.selectedDate.collectAsState()
    
    // 获取相机和相册回调
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    var requestCameraPermission by remember { mutableStateOf(false) }
    var requestGalleryPermission by remember { mutableStateOf(false) }
    
    // 获取图片选择回调
    val onTakePhoto by DialogManager._onTakePhoto.collectAsState()
    val onChooseFromGallery by DialogManager._onChooseFromGallery.collectAsState()
    val onImageSelected by DialogManager._onImageSelected.collectAsState()
    
    // 相机和相册启动器
    val cameraLauncher = rememberCameraLauncher { result ->
        when (result) {
            is ImagePickerResult.Success -> {
                // 处理相机拍照结果
                onImageSelected?.invoke(result.uri)
            }
            is ImagePickerResult.Error -> {
                ToastUtil.showError(result.message)
            }
            is ImagePickerResult.Canceled -> {
                // 用户取消操作
            }
        }
    }
    
    val galleryLauncher = rememberGalleryLauncher { result ->
        when (result) {
            is ImagePickerResult.Success -> {
                // 处理相册选择结果
                onImageSelected?.invoke(result.uri)
            }
            is ImagePickerResult.Error -> {
                ToastUtil.showError(result.message)
            }
            is ImagePickerResult.Canceled -> {
                // 用户取消操作
            }
        }
    }
    
    // 处理相机权限请求
    if (requestCameraPermission) {
        RequestCameraPermission { result ->
            requestCameraPermission = false
            when (result) {
                PermissionResult.Granted -> {
                    // 权限已授予，启动相机
                    scope.launch {
                        try {
                            val photoFile = ImagePickerUtil.createImageFile(context)
                            val photoUri = ImagePickerUtil.getUriForFile(context, photoFile)
                            cameraLauncher(photoUri)
                        } catch (e: Exception) {
                            ToastUtil.showError("Failed to prepare camera: ${e.message}")
                        }
                    }
                }
                PermissionResult.Denied -> {
                    // 权限被拒绝，但用户未选择"不再询问"
                    ToastUtil.showError("Camera permission is required to take photos")
                }
                PermissionResult.PermanentlyDenied -> {
                    // 权限被永久拒绝，引导用户到设置页面手动授权
                    ToastUtil.showError("Camera permission is permanently denied. Please enable it in app settings.")
                    PermissionUtil.openAppSettings(context)
                }
            }
        }
    }
    
    // 处理相册权限请求
    if (requestGalleryPermission) {
        RequestGalleryPermission { result ->
            requestGalleryPermission = false
            when (result) {
                PermissionResult.Granted -> {
                    // 权限已授予，打开相册
                    galleryLauncher.launch("image/*")
                }
                PermissionResult.Denied -> {
                    // 权限被拒绝，但用户未选择"不再询问"
                    ToastUtil.showError("Storage permission is required to access photos")
                }
                PermissionResult.PermanentlyDenied -> {
                    // 权限被永久拒绝，引导用户到设置页面手动授权
                    ToastUtil.showError("Storage permission is permanently denied. Please enable it in app settings.")
                    PermissionUtil.openAppSettings(context)
                }
            }
        }
    }
    
    if (showImagePicker) {
        ImagePickerDialog(
            onDismiss = { DialogManager.hideImagePicker() },
            onTakePhoto = { 
                DialogManager.hideImagePicker()
                // 只请求相机权限，不调用自定义回调
                requestCameraPermission = true
            },
            onChooseFromGallery = { 
                DialogManager.hideImagePicker()
                // 检查是否需要权限
                val galleryPermissions = PermissionUtil.getGalleryPermissions()
                Log.d("DialogManager", "Gallery permissions needed: $galleryPermissions")
                Log.d("DialogManager", "Android version: ${Build.VERSION.SDK_INT}")
                
                if (galleryPermissions.isEmpty()) {
                    // 不需要权限，直接打开相册
                    Log.d("DialogManager", "Opening gallery directly")
                    galleryLauncher.launch("image/*")
                } else {
                    // 需要权限，先请求权限
                    Log.d("DialogManager", "Requesting gallery permissions: $galleryPermissions")
                    requestGalleryPermission = true
                }
            }
        )
    }
    
    if (showDatePicker) {
        DatePickerDialog(
            onDismiss = { DialogManager.hideDatePicker() },
            onDateSelected = { date ->
                val calendar = Calendar.getInstance()
                calendar.time = date
                DialogManager.setSelectedDate(
                    calendar.get(Calendar.YEAR),
                    calendar.get(Calendar.MONTH),
                    calendar.get(Calendar.DAY_OF_MONTH)
                )
                // 不需要在这里调用 DialogManager.hideDatePicker()，因为在 DatePickerDialog 中已经调用了 onDismiss()
            },
            initialDate = selectedDate.time
        )
    }
    
    // 其他对话框可以在这里添加
} 