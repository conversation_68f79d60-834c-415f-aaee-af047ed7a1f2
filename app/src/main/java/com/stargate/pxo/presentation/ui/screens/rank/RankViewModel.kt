package com.stargate.pxo.presentation.ui.screens.rank

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 排行榜页面的ViewModel
 */
@HiltViewModel
class RankViewModel @Inject constructor(
    // TODO: Inject repository when available
) : ViewModel() {

    private val _uiState = MutableStateFlow(RankUiState())
    val uiState: StateFlow<RankUiState> = _uiState.asStateFlow()

    init {
        // Load initial data
        loadRankData(RankTabType.CHARM)
    }

    /**
     * 选择tab
     */
    fun selectTab(tabType: RankTabType) {
        _uiState.value = _uiState.value.copy(selectedTab = tabType)
        loadRankData(tabType)
    }

    /**
     * 选择时间周期
     */
    fun selectPeriod(period: RankPeriod) {
        _uiState.value = _uiState.value.copy(selectedPeriod = period)
        // Reload data for current tab with new period
        loadRankData(_uiState.value.selectedTab)
    }

    /**
     * 刷新排行榜数据
     */
    fun refreshRankData(tabType: RankTabType) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isRefreshing = true)
            
            try {
                // Clear existing data
                clearTabData(tabType)
                
                // Load fresh data
                loadRankData(tabType)
            } catch (e: Exception) {
                // Handle error
            } finally {
                _uiState.value = _uiState.value.copy(isRefreshing = false)
            }
        }
    }

    /**
     * 加载更多排行榜数据
     */
    fun loadMoreRankData(tabType: RankTabType) {
        if (_uiState.value.isLoading) return
        
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                val newItems = generateMockRankItems(tabType, getNextPage(tabType))
                addItemsToTab(tabType, newItems)
            } catch (e: Exception) {
                // Handle error
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    /**
     * 加载排行榜数据
     */
    private fun loadRankData(tabType: RankTabType) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            try {
                // TODO: Replace with actual API call
                val mockItems = generateMockRankItems(tabType, 1)
                setTabData(tabType, mockItems)
            } catch (e: Exception) {
                // Handle error
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    private fun clearTabData(tabType: RankTabType) {
        when (tabType) {
            RankTabType.CHARM -> _uiState.value = _uiState.value.copy(charmRankItems = emptyList())
            RankTabType.RICK -> _uiState.value = _uiState.value.copy(rickRankItems = emptyList())
            RankTabType.COUPLE -> _uiState.value = _uiState.value.copy(coupleRankItems = emptyList())
        }
    }

    private fun setTabData(tabType: RankTabType, items: List<RankItem>) {
        when (tabType) {
            RankTabType.CHARM -> _uiState.value = _uiState.value.copy(charmRankItems = items)
            RankTabType.RICK -> _uiState.value = _uiState.value.copy(rickRankItems = items)
            RankTabType.COUPLE -> _uiState.value = _uiState.value.copy(coupleRankItems = items)
        }
    }

    private fun addItemsToTab(tabType: RankTabType, newItems: List<RankItem>) {
        when (tabType) {
            RankTabType.CHARM -> {
                val currentItems = _uiState.value.charmRankItems
                _uiState.value = _uiState.value.copy(charmRankItems = currentItems + newItems)
            }
            RankTabType.RICK -> {
                val currentItems = _uiState.value.rickRankItems
                _uiState.value = _uiState.value.copy(rickRankItems = currentItems + newItems)
            }
            RankTabType.COUPLE -> {
                val currentItems = _uiState.value.coupleRankItems
                _uiState.value = _uiState.value.copy(coupleRankItems = currentItems + newItems)
            }
        }
    }

    private fun getNextPage(tabType: RankTabType): Int {
        return when (tabType) {
            RankTabType.CHARM -> (_uiState.value.charmRankItems.size / 20) + 1
            RankTabType.RICK -> (_uiState.value.rickRankItems.size / 20) + 1
            RankTabType.COUPLE -> (_uiState.value.coupleRankItems.size / 20) + 1
        }
    }

    /**
     * 生成模拟数据 - TODO: 替换为实际API调用
     */
    private fun generateMockRankItems(tabType: RankTabType, page: Int): List<RankItem> {
        val startIndex = (page - 1) * 20
        return (1..20).map { index ->
            val rank = startIndex + index
            RankItem(
                id = "${tabType.name.lowercase()}_$rank",
                rank = rank,
                userName = when (tabType) {
                    RankTabType.CHARM -> "CharmUser$rank"
                    RankTabType.RICK -> "RickUser$rank"
                    RankTabType.COUPLE -> "Couple$rank"
                },
                score = (1000 - rank * 10).toLong(),
                additionalInfo = when (tabType) {
                    RankTabType.CHARM -> "💎 ${1000 - rank * 5}"
                    RankTabType.RICK -> "🎯 ${800 - rank * 3}"
                    RankTabType.COUPLE -> "💕 ${1200 - rank * 8}"
                },
                avatarUrl = "",
                tabType = tabType
            )
        }
    }
}

/**
 * 排行榜UI状态
 */
data class RankUiState(
    val selectedTab: RankTabType = RankTabType.CHARM,
    val selectedPeriod: RankPeriod = RankPeriod.DAILY,
    val charmRankItems: List<RankItem> = emptyList(),
    val rickRankItems: List<RankItem> = emptyList(),
    val coupleRankItems: List<RankItem> = emptyList(),
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val error: String? = null
)

/**
 * 排行榜tab类型
 */
enum class RankTabType {
    CHARM,
    RICK,
    COUPLE
}

/**
 * 排行榜时间周期
 */
enum class RankPeriod {
    DAILY,
    WEEKLY
}

/**
 * 排行榜项目数据模型
 */
data class RankItem(
    val id: String,
    val rank: Int,
    val userName: String,
    val score: Long,
    val additionalInfo: String,
    val avatarUrl: String,
    val tabType: RankTabType
) 