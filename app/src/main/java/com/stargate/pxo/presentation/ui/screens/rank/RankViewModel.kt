package com.stargate.pxo.presentation.ui.screens.rank

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stargate.pxo.common.base.BaseViewModel
import com.stargate.pxo.common.base.UiState
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.repository.RankRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 排行榜页面的ViewModel
 */
@HiltViewModel
class RankViewModel @Inject constructor(
    private val rankRepository: RankRepository
) : BaseViewModel<RankUiState>() {

    override fun createInitialState(): RankUiState = RankUiState()

    init {
        // Load initial data
        loadRankData(RankTabType.CHARM)
    }

    /**
     * 选择tab
     */
    fun selectTab(tabType: RankTabType) {
        updateState { copy(selectedTab = tabType) }
        loadRankData(tabType)
    }

    /**
     * 选择时间周期
     */
    fun selectPeriod(period: RankPeriod) {
        updateState { copy(selectedPeriod = period) }
        // Reload data for current tab with new period
        loadRankData(uiState.value.selectedTab)
    }

    /**
     * 刷新排行榜数据
     */
    fun refreshRankData(tabType: RankTabType) {
        viewModelScope.launch {
            updateState { copy(isRefreshing = true) }
            
            try {
                // Clear existing data
                clearTabData(tabType)
                
                // Load fresh data
                loadRankData(tabType)
            } catch (e: Exception) {
                LogUtil.e("RankViewModel", "刷新数据异常", e)
            } finally {
                updateState { copy(isRefreshing = false) }
            }
        }
    }



    /**
     * 加载排行榜数据
     */
    private fun loadRankData(tabType: RankTabType) {
        viewModelScope.launch {
            updateState { copy(isLoading = true, error = null) }
            
            try {
                val requestData = buildRequestData(tabType, uiState.value.selectedPeriod)
                
                val flow = when (tabType) {
                    RankTabType.CHARM -> rankRepository.getRankCharm(requestData)
                    RankTabType.RICK -> rankRepository.getRankRich(requestData)
                    RankTabType.COUPLE -> rankRepository.getRankCouple(requestData)
                }
                
                flow.collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            updateState { copy(isLoading = true) }
                        }
                        is Resource.Success -> {
                            val rankItems = convertToRankItems(resource.data, tabType)
                            setTabData(tabType, rankItems)
                            updateState { copy(isLoading = false, error = null) }
                        }
                        is Resource.Error -> {
                            LogUtil.e("RankViewModel", "加载排行榜数据失败: ${resource.message}")
                            updateState { copy(
                                isLoading = false,
                                error = resource.message ?: "加载失败"
                            ) }
                            // 加载失败时使用模拟数据作为后备
                            val mockItems = generateMockRankItems(tabType)
                            setTabData(tabType, mockItems)
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e("RankViewModel", "加载排行榜数据异常", e)
                updateState { copy(
                    isLoading = false,
                    error = "加载异常: ${e.message}"
                ) }
                // 异常时使用模拟数据作为后备
                val mockItems = generateMockRankItems(tabType)
                setTabData(tabType, mockItems)
            }
        }
    }

    private fun clearTabData(tabType: RankTabType) {
        when (tabType) {
            RankTabType.CHARM -> updateState { copy(charmRankItems = emptyList()) }
            RankTabType.RICK -> updateState { copy(rickRankItems = emptyList()) }
            RankTabType.COUPLE -> updateState { copy(coupleRankItems = emptyList()) }
        }
    }

    private fun setTabData(tabType: RankTabType, items: List<RankItem>) {
        when (tabType) {
            RankTabType.CHARM -> updateState { copy(charmRankItems = items) }
            RankTabType.RICK -> updateState { copy(rickRankItems = items) }
            RankTabType.COUPLE -> updateState { copy(coupleRankItems = items) }
        }
    }



    /**
     * 构建请求参数
     */
    private fun buildRequestData(tabType: RankTabType, period: RankPeriod): Map<String, Any> {
        return mapOf(
            "count" to 2,
            "rankType" to when (period) {
                RankPeriod.DAILY -> "daily"
                RankPeriod.WEEKLY -> "weekly"
            },
            "type" to when (tabType) {
                RankTabType.CHARM -> "charm"
                RankTabType.RICK -> "rich"
                RankTabType.COUPLE -> "couple"
            }
        )
    }
    
    /**
     * 将API返回的数据转换为RankItem列表
     */
    private fun convertToRankItems(apiData: List<Any>, tabType: RankTabType): List<RankItem> {
        return try {
            // TODO: 根据实际API返回的数据结构进行解析
            // 这里先使用模拟数据的逻辑
            generateMockRankItems(tabType)
        } catch (e: Exception) {
            LogUtil.e("RankViewModel", "转换排行榜数据失败", e)
            generateMockRankItems(tabType)
        }
    }

    /**
     * 生成模拟数据 - 作为API数据解析失败时的后备方案
     */
    private fun generateMockRankItems(tabType: RankTabType): List<RankItem> {
        return (1..50).map { rank ->
            RankItem(
                id = "${tabType.name.lowercase()}_$rank",
                rank = rank,
                userName = when (tabType) {
                    RankTabType.CHARM -> "CharmUser$rank"
                    RankTabType.RICK -> "RickUser$rank"
                    RankTabType.COUPLE -> "Couple$rank"
                },
                score = (1000 - rank * 10).toLong(),
                additionalInfo = when (tabType) {
                    RankTabType.CHARM -> "💎 ${1000 - rank * 5}"
                    RankTabType.RICK -> "🎯 ${800 - rank * 3}"
                    RankTabType.COUPLE -> "💕 ${1200 - rank * 8}"
                },
                avatarUrl = "",
                tabType = tabType
            )
        }
    }
}

/**
 * 排行榜UI状态
 */
data class RankUiState(
    val selectedTab: RankTabType = RankTabType.CHARM,
    val selectedPeriod: RankPeriod = RankPeriod.DAILY,
    val charmRankItems: List<RankItem> = emptyList(),
    val rickRankItems: List<RankItem> = emptyList(),
    val coupleRankItems: List<RankItem> = emptyList(),
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false,
    val error: String? = null
) : UiState

/**
 * 排行榜tab类型
 */
enum class RankTabType {
    CHARM,
    RICK,
    COUPLE
}

/**
 * 排行榜时间周期
 */
enum class RankPeriod {
    DAILY,
    WEEKLY
}

/**
 * 排行榜项目数据模型
 */
data class RankItem(
    val id: String,
    val rank: Int,
    val userName: String,
    val score: Long,
    val additionalInfo: String,
    val avatarUrl: String,
    val tabType: RankTabType
) 