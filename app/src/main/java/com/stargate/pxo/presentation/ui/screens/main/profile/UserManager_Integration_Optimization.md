# ProfileViewModel UserManager 集成优化

## 🎯 优化内容

根据你的建议，将 ProfileViewModel 中的用户信息获取方式从调用 `getCurrentUser()` 方法改为直接监听 `UserManager.currentUser` Flow，实现响应式的用户信息更新。

## 🔧 修改对比

### 修改前 ❌ (一次性获取)

```kotlin
/**
 * 加载用户资料
 */
private fun loadUserProfile() {
    viewModelScope.launch {
        try {
            updateState { 
                copy(isLoading = true, error = null)
            }
            
            // 一次性获取用户信息
            val currentUser = userManager.getCurrentUser()
            
            updateState {
                copy(
                    userName = currentUser?.userName ?: "Charlotte",
                    userAvatar = currentUser?.avatar ?: "",
                    // ... 其他字段
                    isLoading = false
                )
            }
        } catch (e: Exception) {
            // 错误处理
        }
    }
}
```

### 修改后 ✅ (响应式监听)

```kotlin
/**
 * 监听用户信息变化
 */
private fun observeUserChanges() {
    viewModelScope.launch {
        userManager.currentUser.collectLatest { userInfo ->
            try {
                updateState {
                    copy(
                        userName = userInfo?.userName ?: "Charlotte",
                        userAvatar = userInfo?.avatar ?: "",
                        userLevel = "LV1",
                        diamondCount = "39000",
                        isVipUser = userInfo?.isVip ?: false,
                        isLoading = false,
                        error = null
                    )
                }
                
                LogUtil.d(TAG, "用户资料更新成功: ${userInfo?.userName}")
            } catch (e: Exception) {
                LogUtil.e(TAG, "更新用户资料失败", e)
                updateState {
                    copy(
                        isLoading = false,
                        error = "更新用户资料失败: ${e.message}"
                    )
                }
            }
        }
    }
}
```

## 📋 优化优势

### 1. **响应式更新** ✅

#### 修改前的问题：
```kotlin
// 只在初始化时获取一次用户信息
// 如果用户信息在其他地方更新，ProfileScreen 不会自动更新
init {
    loadUserProfile() // 只执行一次
}
```

#### 修改后的优势：
```kotlin
// 持续监听用户信息变化
// 用户信息在任何地方更新，ProfileScreen 都会自动同步
init {
    observeUserChanges() // 持续监听
}
```

### 2. **数据一致性** ✅

#### 场景示例：
```
1. 用户在设置页面修改了头像
    ↓
2. UserManager.currentUser 发出新的用户信息
    ↓
3. ProfileViewModel 自动接收到更新
    ↓
4. ProfileScreen 自动显示新头像
```

### 3. **减少重复代码** ✅

#### 不需要手动刷新：
```kotlin
// ❌ 修改前需要手动调用
fun refreshUserProfile() {
    loadUserProfile() // 需要手动刷新
}

// ✅ 修改后自动更新
// 不需要手动刷新方法，自动响应变化
```

### 4. **更好的用户体验** ✅

#### 实时同步：
- ✅ **头像更新** - 修改头像后立即在个人资料页显示
- ✅ **用户名更新** - 修改用户名后立即同步
- ✅ **VIP状态更新** - VIP状态变化立即反映
- ✅ **登录状态同步** - 登录/退出后立即更新

## 🔄 与其他 ViewModel 的一致性

### HomeViewModel 的类似模式：

```kotlin
// HomeViewModel 中也使用了类似的响应式监听
init {
    // 监听用户信息变化
    viewModelScope.launch {
        UserManager.currentUser.collectLatest { userInfo ->
            // 用户信息变化时可以在这里处理
        }
    }
    
    // 监听主播状态变化
    observeBroadcasterStatusUpdates()
}
```

### ProfileViewModel 现在的模式：

```kotlin
// ProfileViewModel 现在也使用响应式监听
init {
    observeUserChanges() // 监听用户信息变化
}
```

## 📊 数据流对比

### 修改前的数据流：

```
ProfileViewModel 初始化
    ↓
调用 userManager.getCurrentUser()
    ↓
获取当前用户信息（一次性）
    ↓
更新 UI 状态
    ↓
用户信息在其他地方更新 → ProfileScreen 不知道 ❌
```

### 修改后的数据流：

```
ProfileViewModel 初始化
    ↓
监听 userManager.currentUser Flow
    ↓
用户信息变化时自动触发
    ↓
自动更新 UI 状态
    ↓
ProfileScreen 始终显示最新信息 ✅
```

## 🎯 使用场景

### 1. **用户登录/退出**

```kotlin
// 用户登录
UserManager.login(userInfo)
    ↓
UserManager.currentUser 发出新用户信息
    ↓
ProfileViewModel 自动更新显示登录用户信息
```

### 2. **用户信息编辑**

```kotlin
// 在设置页面修改用户名
UserManager.updateUserName("新用户名")
    ↓
UserManager.currentUser 发出更新后的用户信息
    ↓
ProfileViewModel 自动显示新用户名
```

### 3. **VIP状态变化**

```kotlin
// 用户购买VIP
UserManager.updateVipStatus(true)
    ↓
UserManager.currentUser 发出更新后的用户信息
    ↓
ProfileViewModel 自动显示VIP标识
```

## 🔍 调试和监控

### 日志输出：

```
D/ProfileViewModel: 用户资料更新成功: Charlotte
D/ProfileViewModel: 用户资料更新成功: NewUserName
D/ProfileViewModel: 用户资料更新成功: null (用户退出)
```

### 监控用户信息变化：

```kotlin
// 可以添加更详细的日志
userManager.currentUser.collectLatest { userInfo ->
    LogUtil.d(TAG, "收到用户信息更新:")
    LogUtil.d(TAG, "  用户名: ${userInfo?.userName}")
    LogUtil.d(TAG, "  头像: ${userInfo?.avatar}")
    LogUtil.d(TAG, "  VIP状态: ${userInfo?.isVip}")
    
    // 更新UI状态
    updateState { ... }
}
```

## ✅ 总结

**ProfileViewModel 已优化为响应式用户信息监听！**

### 主要改进：
1. ✅ **响应式更新** - 使用 `collectLatest` 监听用户信息变化
2. ✅ **数据一致性** - 用户信息变化时自动同步UI
3. ✅ **架构统一** - 与其他 ViewModel 保持一致的模式
4. ✅ **用户体验** - 实时显示最新的用户信息

### 技术优势：
- 🔄 **自动同步** - 不需要手动刷新
- 📱 **实时更新** - 用户信息变化立即反映
- 🏗️ **架构优雅** - 符合响应式编程原则
- 🛡️ **数据安全** - 始终显示最新、准确的用户信息

### 用户体验：
- ⚡ **即时反馈** - 修改信息后立即看到变化
- 🔄 **状态同步** - 多个页面的用户信息保持一致
- 📱 **流畅体验** - 无需手动刷新页面

现在 ProfileViewModel 使用了更优雅和高效的响应式用户信息监听机制！
