package com.stargate.pxo.common.util

import android.content.Context
import android.os.Build
import com.stargate.pxo.common.util.log.LogUtil

/**
 * 设备兼容性工具类
 * 专门处理不同厂商和Android版本的Toast兼容性问题
 */
object DeviceCompatibilityUtil {
    
    // 已知的问题机型和系统
    private val PROBLEMATIC_MANUFACTURERS = setOf(
        "XIAOMI", "MIUI", "HUAWEI", "HON<PERSON>", "OPPO", "VIVO", "ONEPLUS", "SAMSUNG"
    )
    
    private val PROBLEMATIC_MODELS = setOf(
        "MI", "REDMI", "HUAWEI", "HONOR", "OPPO", "VIVO", "ONEPLUS", "SAMSUNG"
    )
    
    /**
     * 检查是否为问题厂商
     */
    fun isProblematicManufacturer(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        val model = Build.MODEL.uppercase()
        
        return PROBLEMATIC_MANUFACTURERS.any { 
            manufacturer.contains(it) || brand.contains(it) 
        } || PROBLEMATIC_MODELS.any { 
            model.contains(it) 
        }
    }
    
    /**
     * 检查是否支持自定义Toast
     */
    fun supportsCustomToast(): Boolean {
        return when {
            // Android 11+ 限制了Toast自定义
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> false
            // 某些厂商在较低版本也限制了Toast
            isProblematicManufacturer() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> false
            else -> true
        }
    }
    
    /**
     * 检查是否支持Toast位置设置
     */
    fun supportsToastGravity(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> false
            isXiaomiDevice() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.P -> false
            isHuaweiDevice() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q -> false
            else -> true
        }
    }
    
    /**
     * 获取适合当前设备的Toast配置
     */
    fun getCompatibleToastConfig(): ToastConfig {
        return when {
            // Android 11+ 使用系统默认样式
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                LogUtil.d("DeviceCompatibility", "Using system toast for Android 11+")
                ToastConfig.simpleConfig()
            }
            
            // 小米设备优化
            isXiaomiDevice() -> {
                LogUtil.d("DeviceCompatibility", "Using Xiaomi optimized config")
                ToastConfig.materialConfig().copy(
                    useCustomStyle = Build.VERSION.SDK_INT < Build.VERSION_CODES.P,
                    enableQueue = false // 小米系统可能会干扰Toast队列
                )
            }
            
            // 华为设备优化
            isHuaweiDevice() -> {
                LogUtil.d("DeviceCompatibility", "Using Huawei optimized config")
                ToastConfig.materialConfig().copy(
                    useCustomStyle = Build.VERSION.SDK_INT < Build.VERSION_CODES.Q,
                    cornerRadius = 8 // 华为系统偏好较小圆角
                )
            }
            
            // OPPO/OnePlus设备优化
            isOppoOrOnePlusDevice() -> {
                LogUtil.d("DeviceCompatibility", "Using OPPO/OnePlus optimized config")
                ToastConfig.colorfulConfig().copy(
                    enableQueue = true,
                    maxQueueSize = 3 // ColorOS可能限制Toast数量
                )
            }
            
            // Vivo设备优化
            isVivoDevice() -> {
                LogUtil.d("DeviceCompatibility", "Using Vivo optimized config")
                ToastConfig.roundedConfig().copy(
                    enableQueue = false,
                    defaultYOffset = 150 // Vivo底部导航栏较高
                )
            }
            
            // 三星设备优化
            isSamsungDevice() -> {
                LogUtil.d("DeviceCompatibility", "Using Samsung optimized config")
                ToastConfig.materialConfig().copy(
                    defaultTextSize = 15f, // 三星One UI字体较大
                    paddingVertical = 16
                )
            }
            
            // 其他设备使用默认配置
            else -> {
                LogUtil.d("DeviceCompatibility", "Using default config for ${Build.MANUFACTURER}")
                ToastConfig.defaultConfig()
            }
        }
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): String {
        return buildString {
            append("Manufacturer: ${Build.MANUFACTURER}\n")
            append("Brand: ${Build.BRAND}\n")
            append("Model: ${Build.MODEL}\n")
            append("Android Version: ${Build.VERSION.RELEASE}\n")
            append("API Level: ${Build.VERSION.SDK_INT}\n")
            append("Supports Custom Toast: ${supportsCustomToast()}\n")
            append("Supports Toast Gravity: ${supportsToastGravity()}")
        }
    }
    
    /**
     * 检查是否为小米设备
     */
    private fun isXiaomiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        return manufacturer.contains("XIAOMI") || 
               brand.contains("XIAOMI") ||
               brand.contains("MIUI") ||
               hasSystemProperty("ro.miui.ui.version.name")
    }
    
    /**
     * 检查是否为华为设备
     */
    private fun isHuaweiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        return manufacturer.contains("HUAWEI") || 
               manufacturer.contains("HONOR") ||
               brand.contains("HUAWEI") ||
               brand.contains("HONOR") ||
               hasSystemProperty("ro.build.hw_emui_api_level")
    }
    
    /**
     * 检查是否为OPPO或OnePlus设备
     */
    private fun isOppoOrOnePlusDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        return manufacturer.contains("OPPO") || 
               manufacturer.contains("ONEPLUS") ||
               brand.contains("OPPO") ||
               brand.contains("ONEPLUS") ||
               hasSystemProperty("ro.build.version.opporom")
    }
    
    /**
     * 检查是否为Vivo设备
     */
    private fun isVivoDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        return manufacturer.contains("VIVO") || 
               brand.contains("VIVO") ||
               hasSystemProperty("ro.vivo.os.version")
    }
    
    /**
     * 检查是否为三星设备
     */
    private fun isSamsungDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER.uppercase()
        val brand = Build.BRAND.uppercase()
        return manufacturer.contains("SAMSUNG") || 
               brand.contains("SAMSUNG")
    }
    
    /**
     * 检查系统属性
     */
    private fun hasSystemProperty(property: String): Boolean {
        return try {
            val systemProperties = Class.forName("android.os.SystemProperties")
            val get = systemProperties.getMethod("get", String::class.java)
            val value = get.invoke(null, property) as String?
            !value.isNullOrEmpty()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 应用兼容性修复
     */
    fun applyCompatibilityFixes(context: Context) {
        try {
            when {
                isXiaomiDevice() -> applyXiaomiFixes()
                isHuaweiDevice() -> applyHuaweiFixes()
                isOppoOrOnePlusDevice() -> applyOppoFixes()
                isVivoDevice() -> applyVivoFixes()
                isSamsungDevice() -> applySamsungFixes()
            }
        } catch (e: Exception) {
            LogUtil.w("DeviceCompatibility", "Failed to apply compatibility fixes$e")
        }
    }
    
    private fun applyXiaomiFixes() {
        LogUtil.d("DeviceCompatibility", "Applied Xiaomi compatibility fixes")
        // 小米设备特定修复
    }
    
    private fun applyHuaweiFixes() {
        LogUtil.d("DeviceCompatibility", "Applied Huawei compatibility fixes")
        // 华为设备特定修复
    }
    
    private fun applyOppoFixes() {
        LogUtil.d("DeviceCompatibility", "Applied OPPO compatibility fixes")
        // OPPO设备特定修复
    }
    
    private fun applyVivoFixes() {
        LogUtil.d("DeviceCompatibility", "Applied Vivo compatibility fixes")
        // Vivo设备特定修复
    }
    
    private fun applySamsungFixes() {
        LogUtil.d("DeviceCompatibility", "Applied Samsung compatibility fixes")
        // 三星设备特定修复
    }
}