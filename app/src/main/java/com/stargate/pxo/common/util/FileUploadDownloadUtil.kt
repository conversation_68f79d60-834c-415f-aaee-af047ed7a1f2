package com.stargate.pxo.common.util

import android.content.Context
import android.net.Uri
import android.os.Environment
import android.webkit.MimeTypeMap
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.model.OssPolicyResult
import com.stargate.pxo.data.network.model.XFileUploadResult
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import okio.Buffer
import okio.BufferedSink
import okio.buffer
import okio.sink
import okio.use
import java.io.File
import java.io.IOException
import java.io.RandomAccessFile
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 文件上传下载工具类
 * 支持断点续传和断点下载
 */
@Singleton
class FileUploadDownloadUtil @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()
    
    /**
     * 文件上传进度状态
     */
    sealed class UploadState {
        data class Progress(val progress: Int, val uploaded: Long, val total: Long) : UploadState()
        data class Success(val result: XFileUploadResult) : UploadState()
        data class Error(val message: String) : UploadState()
    }
    
    /**
     * 文件下载进度状态
     */
    sealed class DownloadState {
        data class Progress(val progress: Int, val downloaded: Long, val total: Long) : DownloadState()
        data class Success(val file: File) : DownloadState()
        data class Error(val message: String) : DownloadState()
    }
    
    /**
     * 上传文件
     * @param context 上下文
     * @param uri 文件Uri
     * @param ossPolicyResult OSS策略结果
     * @param headers 请求头
     * @param fieldName 表单字段名
     * @return 上传进度Flow
     */
    suspend fun uploadFile(
        uri: Uri,
        ossPolicyResult: OssPolicyResult,
        headers: Map<String, String> = emptyMap(),
        fieldName: String = "file"
    ): Flow<UploadState> = callbackFlow {
        var call: Call? = null

        try {
            // 获取文件信息
            val file = getFileFromUri(context, uri) ?: throw IOException("Cannot get file from URI")
            val fileName = getFileNameFromUri(context, uri) ?: file.name
            val mimeType = getMimeType(file) ?: "application/octet-stream"

            // 创建进度监听的请求体
            val fileReqBody = createProgressRequestBody(file, mimeType) { progress, uploaded, total ->
                trySend(UploadState.Progress(progress, uploaded, total))
            }

            val filePart = MultipartBody.Part.createFormData(
                fieldName,
                fileName,
                fileReqBody
            )

            // 创建请求体，使用OSS策略参数
            val requestBody: RequestBody = MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("ossaccessKeyId", ossPolicyResult.accessKeyId)
                .addFormDataPart("policy", ossPolicyResult.policy)
                .addFormDataPart("signature", ossPolicyResult.signature)
                .addFormDataPart("callback", ossPolicyResult.callback)
                .addFormDataPart("key", getOssKey(
                    dir = ossPolicyResult.dir,
                    fileName = fileName
                ))
                .addPart(filePart)
                .build()

            // 创建请求
            val request = Request.Builder()
                .url(ossPolicyResult.host)
                .apply {
                    // 添加请求头
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                    }
                }
                .post(requestBody)
                .build()

            // 执行请求
            call = client.newCall(request)
            
            val response = withContext(Dispatchers.IO) {
                call.execute()
            }
            
            try {
                if (!response.isSuccessful) {
                    throw IOException("Unexpected response: ${response.code}")
                }
                
                // 解析响应
                val responseBody = response.body?.string() ?: throw IOException("Empty response")
                LogUtil.d("FileUploadDownloadUtil", "上传响应: $responseBody")
                val result = parseUploadResponse(responseBody)
                
                // 发送成功状态
                trySend(UploadState.Success(result))
            } finally {
                response.close()
            }
        } catch (e: Exception) {
            trySend(UploadState.Error(e.message ?: "Unknown error"))
        }

        // 当Flow被取消时执行清理操作
        awaitClose {
            // 取消网络请求
            call?.cancel()
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 生成OSS对象Key
     */
    private fun getOssKey(dir: String, fileName: String): String {
        val suffixIndex = fileName.lastIndexOf(".")
        val fileType = if (suffixIndex != -1 && suffixIndex > 0) fileName.substring(suffixIndex) else ""
        return "$dir${System.currentTimeMillis()}$fileType"
    }
    
    /**
     * 下载文件
     * @param url 下载地址
     * @param destFile 目标文件
     * @param headers 请求头
     * @return 下载进度Flow
     */
    suspend fun downloadFile(
        url: String,
        destFile: File,
        headers: Map<String, String> = emptyMap()
    ): Flow<DownloadState> = callbackFlow {
        var call: Call? = null

        try {
            // 检查是否存在断点下载记录
            val downloadedBytes = if (destFile.exists()) destFile.length() else 0L

            // 创建请求
            val request = Request.Builder()
                .url(url)
                .apply {
                    // 添加请求头
                    headers.forEach { (key, value) ->
                        addHeader(key, value)
                    }

                    // 如果有断点下载，添加Range头
                    if (downloadedBytes > 0) {
                        addHeader("Range", "bytes=$downloadedBytes-")
                    }
                }
                .get()
                .build()

            // 执行请求
            call = client.newCall(request)
            
            val response = withContext(Dispatchers.IO) {
                call.execute()
            }
            
            try {
                if (!response.isSuccessful) {
                    throw IOException("Unexpected response: ${response.code}")
                }
                
                // 获取文件总大小
                val contentLength = response.header("Content-Length")?.toLongOrNull() ?: -1L
                val totalBytes = if (contentLength > 0) downloadedBytes + contentLength else -1L
                
                // 创建文件输出流
                val randomAccessFile = RandomAccessFile(destFile, "rw")
                if (downloadedBytes > 0) {
                    randomAccessFile.seek(downloadedBytes)
                }
                
                // 读取响应并写入文件
                response.body?.let { body ->
                    val buffer = ByteArray(8192)
                    var downloaded = downloadedBytes
                    var read: Int
                    
                    body.byteStream().use { inputStream ->
                        while (inputStream.read(buffer).also { bytesRead -> read = bytesRead } != -1) {
                            randomAccessFile.write(buffer, 0, read)
                            downloaded += read
                            
                            // 计算进度
                            val progress = if (totalBytes > 0) {
                                ((downloaded * 100) / totalBytes).toInt()
                            } else {
                                -1
                            }
                            
                            // 发送进度状态
                            trySend(DownloadState.Progress(progress, downloaded, totalBytes))
                        }
                    }
                    
                    randomAccessFile.close()
                    
                    // 发送成功状态
                    trySend(DownloadState.Success(destFile))
                } ?: throw IOException("Empty response body")
            } finally {
                response.close()
            }
        } catch (e: Exception) {
            trySend(DownloadState.Error(e.message ?: "Unknown error"))
        }
        
        // 当Flow被取消时执行清理操作
        awaitClose {
            // 取消网络请求
            call?.cancel()
        }
    }.flowOn(Dispatchers.IO)
    
    /**
     * 创建带进度监听的请求体
     */
    private fun createProgressRequestBody(
        file: File,
        contentType: String,
        onProgress: (Int, Long, Long) -> Unit
    ): RequestBody {
        return object : RequestBody() {
            override fun contentType() = contentType.toMediaTypeOrNull()
            
            override fun contentLength() = file.length()
            
            override fun writeTo(sink: BufferedSink) {
                val fileLength = file.length()
                val buffer = ByteArray(8192)
                val randomAccessFile = RandomAccessFile(file, "r")
                
                try {
                    var uploaded = 0L
                    var read: Int
                    
                    while (randomAccessFile.read(buffer).also { read = it } != -1) {
                        sink.write(buffer, 0, read)
                        uploaded += read
                        
                        // 计算进度
                        val progress = ((uploaded * 100) / fileLength).toInt()
                        onProgress(progress, uploaded, fileLength)
                    }
                } finally {
                    randomAccessFile.close()
                }
            }
        }
    }
    
    /**
     * 从Uri获取文件
     */
    private suspend fun getFileFromUri(context: Context, uri: Uri): File? = withContext(Dispatchers.IO) {
        // 如果是file://协议，直接返回文件
        if (uri.scheme == "file") {
            return@withContext File(uri.path ?: return@withContext null)
        }
        
        // 如果是content://协议，复制到临时文件
        if (uri.scheme == "content") {
            val inputStream = context.contentResolver.openInputStream(uri) ?: return@withContext null
            val fileName = getFileNameFromUri(context, uri) ?: "temp_${System.currentTimeMillis()}"
            val tempFile = File(context.cacheDir, fileName)
            
            tempFile.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            
            return@withContext tempFile
        }
        
        return@withContext null
    }
    
    /**
     * 从Uri获取文件名
     */
    private fun getFileNameFromUri(context: Context, uri: Uri): String? {
        // 如果是file://协议，直接返回文件名
        if (uri.scheme == "file") {
            return uri.lastPathSegment
        }
        
        // 如果是content://协议，从ContentResolver获取文件名
        if (uri.scheme == "content") {
            val cursor = context.contentResolver.query(uri, null, null, null, null)
            cursor?.use {
                if (it.moveToFirst()) {
                    val displayNameIndex = it.getColumnIndex("_display_name")
                    if (displayNameIndex != -1) {
                        return it.getString(displayNameIndex)
                    }
                }
            }
        }
        
        return null
    }
    
    /**
     * 获取文件MIME类型
     */
    private fun getMimeType(file: File): String? {
        val extension = file.extension
        return MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
    }
    
    /**
     * 解析上传响应
     */
    private fun parseUploadResponse(response: String): XFileUploadResult {
        // 根据实际API响应格式解析
        // 这里假设响应是JSON格式，包含相应字段
        return try {
            // 简单解析，实际项目中应使用JSON解析库如Gson或Moshi
            val filenameRegex = "\"filename\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val sizeRegex = "\"size\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val mimeTypeRegex = "\"mimeType\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val widthRegex = "\"width\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            val heightRegex = "\"height\"\\s*:\\s*\"([^\"]+)\"".toRegex()
            
            val filename = filenameRegex.find(response)?.groupValues?.get(1) ?: ""
            val size = sizeRegex.find(response)?.groupValues?.get(1) ?: ""
            val mimeType = mimeTypeRegex.find(response)?.groupValues?.get(1) ?: ""
            val width = widthRegex.find(response)?.groupValues?.get(1) ?: ""
            val height = heightRegex.find(response)?.groupValues?.get(1) ?: ""
            
            XFileUploadResult(
                filename = filename,
                size = size,
                mimeType = mimeType,
                width = width,
                height = height
            )
        } catch (e: Exception) {
            // 返回默认值或抛出异常
            XFileUploadResult(
                filename = "",
                size = "",
                mimeType = "",
                width = "",
                height = ""
            )
        }
    }
    
    /**
     * 上传信息
     */
    private data class UploadInfo(
        val fileId: String,
        val uploadedBytes: Long
    )
    
    /**
     * 获取上传信息
     */
    private fun getUploadInfo(file: File): UploadInfo? {
        val infoFile = File(file.parentFile, "${file.name}.upload")
        if (!infoFile.exists()) return null
        
        return try {
            val content = infoFile.readText()
            val parts = content.split(":")
            if (parts.size == 2) {
                UploadInfo(parts[0], parts[1].toLong())
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 保存上传信息
     */
    private fun saveUploadInfo(file: File, uploadedBytes: Long) {
        val infoFile = File(file.parentFile, "${file.name}.upload")
        try {
            infoFile.writeText("${file.name}:$uploadedBytes")
        } catch (e: Exception) {
            // 忽略错误
        }
    }
    
    /**
     * 清除上传信息
     */
    private fun clearUploadInfo(file: File) {
        val infoFile = File(file.parentFile, "${file.name}.upload")
        if (infoFile.exists()) {
            infoFile.delete()
        }
    }
    
    /**
     * 获取下载目录
     */
    fun getDownloadDirectory(context: Context): File {
        return context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS)
            ?: context.filesDir
    }
}