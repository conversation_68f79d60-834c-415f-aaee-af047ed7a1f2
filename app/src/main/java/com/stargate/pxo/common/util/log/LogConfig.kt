package com.stargate.pxo.common.util.log

import com.stargate.pxo.BuildConfig

data class LogConfig(
    // 基础配置
    val isDebug: Boolean = BuildConfig.DEBUG,
    val minLogLevel: LogLevel = if (BuildConfig.DEBUG) LogLevel.VERBOSE else LogLevel.INFO,
    val globalTag: String = "Puxxi",
    val showThreadInfo: Boolean = true,
    val showMethodInfo: Boolean = true,
    val showStackOffset: Int = 0,
    
    // 格式化配置
    val enableBorder: Boolean = true,
    val enableJsonFormat: Boolean = true,
    val maxLineLength: Int = 110,
    val methodOffset: Int = 5,
    
    // 文件日志配置
    val enableFileLog: Boolean = BuildConfig.DEBUG,
    val logDir: String = "logs",
    val maxFileSize: Long = 10 * 1024 * 1024, // 10MB
    val maxFileCount: Int = 5,
    val enableLogUpload: Boolean = false,
    
    // 过滤配置
    val enableFilter: Boolean = false,
    val filterTags: Set<String> = emptySet(),
    val excludeTags: Set<String> = emptySet(),
    
    // 性能配置
    val enableAsync: Boolean = true,
    val enableCrashLog: Boolean = true
) {
    companion object {
        fun defaultConfig() = LogConfig()
        
        fun debugConfig() = LogConfig(
            isDebug = true,
            minLogLevel = LogLevel.VERBOSE,
            enableBorder = true,
            enableJsonFormat = true,
            enableFileLog = true
        )
        
        fun releaseConfig() = LogConfig(
            isDebug = false,
            minLogLevel = LogLevel.ERROR,
            enableBorder = false,
            enableJsonFormat = false,
            enableFileLog = true,
            enableAsync = true
        )
    }
}