package com.stargate.pxo.common.util

import android.graphics.Color
import com.stargate.pxo.common.util.log.LogUtil

/**
 * ToastUtil使用示例
 * 
 * 展示了ToastUtil的各种功能和使用方法
 * 特别针对国外主流机型进行了优化
 */
object ToastUtilExample {
    
    fun demonstrateBasicUsage() {
        LogUtil.d("ToastExample", "开始演示Toast基础用法")
        
        // =================== 基础使用 ===================
        
        // 简单短Toast
        ToastUtil.showShort("这是一个短Toast")
        
        // 简单长Toast
        ToastUtil.showLong("这是一个长Toast，显示时间更久")
        
        // 使用字符串资源
        // ToastUtil.showShort(R.string.app_name)
        
        // =================== 样式化Toast ===================
        
        // 成功提示
        ToastUtil.showSuccess("操作成功完成！")
        
        // 错误提示
        ToastUtil.showError("操作失败，请重试")
        
        // 警告提示
        ToastUtil.showWarning("请注意：数据即将过期")
        
        // 信息提示
        ToastUtil.showInfo("新版本可用，建议更新")
        
        // =================== 自定义Toast ===================
        
        // 完全自定义的Toast
        ToastUtil.showCustomToast(
            message = "这是自定义样式的Toast",
            backgroundColor = Color.parseColor("#FF5722"),
            textColor = Color.WHITE,
            textSize = 16f,
            // icon = R.drawable.ic_custom_icon
        )
        
        // 带图标的Toast
        ToastUtil.showWithIcon(
            message = "下载完成",
            iconRes = android.R.drawable.stat_sys_download_done
        )
    }
    
    fun demonstrateAdvancedFeatures() {
        LogUtil.d("ToastExample", "开始演示Toast高级功能")
        
        // =================== 队列管理 ===================
        
        // 连续显示多个Toast（会排队显示）
        repeat(5) { index ->
            ToastUtil.showShort("排队Toast #${index + 1}")
        }
        
        // 检查队列状态
        LogUtil.d("ToastExample", "当前队列大小: ${ToastUtil.getQueueSize()}")
        LogUtil.d("ToastExample", "是否有Toast在显示: ${ToastUtil.isShowing()}")
        
        // =================== 取消操作 ===================
        
        // 显示一些Toast然后取消
        ToastUtil.showLong("这个Toast会被取消")
        ToastUtil.showLong("这个也会被取消")
        
        // 延迟后取消所有Toast
        // 注意：这里需要在实际Activity或Fragment中使用适当的协程作用域
        // lifecycleScope.launch {
        //     delay(1000)
        //     ToastUtil.cancelAllToasts()
        //     LogUtil.d("ToastExample", "所有Toast已被取消")
        // }
    }
    
    fun demonstrateDeviceCompatibility() {
        LogUtil.d("ToastExample", "开始演示设备兼容性功能")
        
        // 显示设备信息
        val deviceInfo = DeviceCompatibilityUtil.getDeviceInfo()
        LogUtil.d("ToastExample", "设备信息:\n$deviceInfo")
        
        // 根据设备类型显示不同样式的Toast
        when {
            DeviceCompatibilityUtil.isProblematicManufacturer() -> {
                ToastUtil.showWarning("检测到特殊设备，使用兼容模式")
            }
            DeviceCompatibilityUtil.supportsCustomToast() -> {
                ToastUtil.showSuccess("设备支持自定义Toast样式")
            }
            else -> {
                ToastUtil.showInfo("使用系统默认Toast样式")
            }
        }
        
        // 测试不同配置
        testDifferentConfigs()
    }
    
    fun demonstrateConfigurationOptions() {
        LogUtil.d("ToastExample", "开始演示配置选项")
        
        // =================== 不同预设配置 ===================
        
        // Material Design风格
        ToastUtil.updateConfig(ToastConfig.materialConfig())
        ToastUtil.showInfo("这是Material Design风格")
        
        // 圆角风格
        ToastUtil.updateConfig(ToastConfig.roundedConfig())
        ToastUtil.showSuccess("这是圆角风格")
        
        // 彩色主题
        ToastUtil.updateConfig(ToastConfig.colorfulConfig())
        ToastUtil.showWarning("这是彩色主题")
        
        // 高对比度（适合视力不佳用户）
        ToastUtil.updateConfig(ToastConfig.highContrastConfig())
        ToastUtil.showError("这是高对比度模式")
        
        // 大字体（适合老年用户）
        ToastUtil.updateConfig(ToastConfig.largeFontConfig())
        ToastUtil.showInfo("这是大字体模式")
        
        // 重置为默认配置
        ToastUtil.resetConfig()
        ToastUtil.showShort("已重置为默认配置")
    }
    
    fun demonstrateRealWorldScenarios() {
        LogUtil.d("ToastExample", "开始演示实际使用场景")
        
        // =================== 网络请求场景 ===================
        
        // 开始请求
        ToastUtil.showInfo("正在加载数据...")
        
        // 模拟网络请求
        // lifecycleScope.launch {
        //     try {
        //         delay(2000) // 模拟网络延迟
        //         
        //         // 成功
        //         ToastUtil.showSuccess("数据加载成功")
        //         
        //     } catch (e: Exception) {
        //         // 失败
        //         ToastUtil.showError("网络连接失败: ${e.message}")
        //     }
        // }
        
        // =================== 用户操作场景 ===================
        
        // 保存数据
        ToastUtil.showInfo("正在保存...")
        // 延迟后显示结果
        // Handler(Looper.getMainLooper()).postDelayed({
        //     ToastUtil.showSuccess("保存成功")
        // }, 1500)
        
        // 删除确认
        ToastUtil.showWarning("请确认是否删除此项目")
        
        // 权限请求
        ToastUtil.showInfo("请授予相机权限以继续")
        
        // =================== 表单验证场景 ===================
        
        // 验证失败
        ToastUtil.showError("请填写必填字段")
        ToastUtil.showWarning("密码长度至少8位")
        ToastUtil.showError("邮箱格式不正确")
        
        // 验证成功
        ToastUtil.showSuccess("表单验证通过")
    }
    
    private fun testDifferentConfigs() {
        val configs = listOf(
            "默认配置" to ToastConfig.defaultConfig(),
            "简约配置" to ToastConfig.simpleConfig(),
            "Material配置" to ToastConfig.materialConfig(),
            "圆角配置" to ToastConfig.roundedConfig(),
            "彩色配置" to ToastConfig.colorfulConfig()
        )
        
        configs.forEachIndexed { index, (name, config) ->
            // 延迟显示以避免Toast重叠
            // Handler(Looper.getMainLooper()).postDelayed({
            //     ToastUtil.updateConfig(config)
            //     ToastUtil.showInfo("测试: $name")
            // }, index * 3000L)
        }
    }
    
    fun demonstrateAccessibilityFeatures() {
        LogUtil.d("ToastExample", "开始演示无障碍功能")
        
        // 高对比度模式
        ToastUtil.updateConfig(ToastConfig.highContrastConfig())
        ToastUtil.showInfo("高对比度模式已启用")
        
        // 大字体模式
        ToastUtil.updateConfig(ToastConfig.largeFontConfig())
        ToastUtil.showInfo("大字体模式已启用，适合视力不佳的用户")
        
        // 简化模式（减少视觉干扰）
        ToastUtil.updateConfig(ToastConfig.simpleConfig())
        ToastUtil.showInfo("简化模式已启用，减少视觉元素")
    }
    
    fun demonstratePerformanceTest() {
        LogUtil.d("ToastExample", "开始性能测试")
        
        val startTime = System.currentTimeMillis()
        
        // 快速显示多个Toast测试
        repeat(20) { index ->
            ToastUtil.showShort("性能测试 #$index")
        }
        
        val endTime = System.currentTimeMillis()
        LogUtil.i("ToastExample", "显示20个Toast耗时: ${endTime - startTime}ms")
        
        // 测试队列管理
        LogUtil.d("ToastExample", "队列中Toast数量: ${ToastUtil.getQueueSize()}")
        
        // 清理测试
        // Handler(Looper.getMainLooper()).postDelayed({
        //     ToastUtil.cancelAllToasts()
        //     LogUtil.d("ToastExample", "性能测试完成，已清理所有Toast")
        // }, 5000)
    }
    
    /**
     * 应用退出时的清理示例
     */
    fun onAppExit() {
        LogUtil.d("ToastExample", "应用退出，清理Toast资源")
        
        // 取消所有Toast
        ToastUtil.cancelAllToasts()
        
        // 销毁ToastUtil
        ToastUtil.destroy()
        
        LogUtil.d("ToastExample", "Toast资源清理完成")
    }
}