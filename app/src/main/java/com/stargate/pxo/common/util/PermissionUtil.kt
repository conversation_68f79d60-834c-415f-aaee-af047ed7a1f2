package com.stargate.pxo.common.util

import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.platform.LocalContext
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.google.accompanist.permissions.shouldShowRationale


/**
 * 权限管理工具类
 * 用于处理应用所需的各种权限
 */
object PermissionUtil {

    /**
     * 相机权限
     * 根据不同的Android版本返回不同的权限列表
     */
    fun getCameraPermissions(): List<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+)
            listOf(
                Manifest.permission.CAMERA
            )
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ (API 29+)
            listOf(
                Manifest.permission.CAMERA
            )
        } else {
            // Android 9 及以下
            listOf(
                Manifest.permission.CAMERA,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }
    }

    /**
     * 相册权限
     * 根据不同的Android版本返回不同的权限列表
     * 注意：使用 ACTION_GET_CONTENT 在大多数情况下不需要权限
     */
    fun getGalleryPermissions(): List<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ (API 29+) - 使用ACTION_GET_CONTENT不需要权限
            emptyList()
        } else {
            // Android 9 及以下 (API 28-) - 需要读取外部存储权限
            listOf(
                Manifest.permission.READ_EXTERNAL_STORAGE
            )
        }
    }

    /**
     * 打开应用设置页面
     */
    fun openAppSettings(context: Context) {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", context.packageName, null)
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }
}

/**
 * 权限请求结果
 */
sealed class PermissionResult {
    object Granted : PermissionResult()
    object Denied : PermissionResult()
    object PermanentlyDenied : PermissionResult()
}

/**
 * 多权限请求组件
 * 使用 accompanist-permissions 库处理权限请求
 */
@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun RequestMultiplePermissions(
    permissions: List<String>,
    rationaleTitle: String = "权限请求",
    rationaleText: String = "此功能需要相关权限才能使用",
    permanentlyDeniedTitle: String = "权限被拒绝",
    permanentlyDeniedText: String = "您已永久拒绝权限，请在设置中手动开启",
    onPermissionResult: (PermissionResult) -> Unit
) {
    val context = LocalContext.current
    
    // 如果权限列表为空，直接返回成功
    if (permissions.isEmpty()) {
        LaunchedEffect(Unit) {
            onPermissionResult(PermissionResult.Granted)
        }
        return
    }
    
    // 使用 rememberedSaveable 来防止重组时重复执行
    var hasProcessedResult by rememberSaveable { mutableStateOf(false) }
    
    // 使用 accompanist-permissions 创建权限状态
    val permissionsState = rememberMultiplePermissionsState(permissions = permissions)
    
    // 权限对话框状态
    var showRationale by remember { mutableStateOf(false) }
    var showPermanentlyDenied by remember { mutableStateOf(false) }
    
    // 初始检查权限状态
    LaunchedEffect(Unit) {
        if (!hasProcessedResult) {
            when {
                // 所有权限都已授予
                permissionsState.allPermissionsGranted -> {
                    hasProcessedResult = true
                    onPermissionResult(PermissionResult.Granted)
                }
                // 有权限需要显示理由
                permissionsState.shouldShowRationale -> {
                    showRationale = true
                }
                // 有权限被永久拒绝
                else -> {
                    // 首次请求权限
                    permissionsState.launchMultiplePermissionRequest()
                }
            }
        }
    }
    
    // 监听权限状态变化
    LaunchedEffect(permissionsState.allPermissionsGranted, permissionsState.shouldShowRationale) {
        if (!hasProcessedResult) {
            when {
                permissionsState.allPermissionsGranted -> {
                    hasProcessedResult = true
                    onPermissionResult(PermissionResult.Granted)
                }
                permissionsState.shouldShowRationale -> {
                    showRationale = true
                }
                permissionsState.revokedPermissions.isNotEmpty() && !permissionsState.shouldShowRationale -> {
                    // 检查是否有被永久拒绝的权限
                    val hasPermanentlyDenied = permissionsState.revokedPermissions.any { 
                        !it.status.shouldShowRationale 
                    }
                    if (hasPermanentlyDenied) {
                        hasProcessedResult = true
                        showPermanentlyDenied = true
                        onPermissionResult(PermissionResult.PermanentlyDenied)
                    } else {
                        hasProcessedResult = true
                        onPermissionResult(PermissionResult.Denied)
                    }
                }
            }
        }
    }
    
    // 显示权限说明对话框
    if (showRationale) {
        AlertDialog(
            onDismissRequest = { 
                showRationale = false 
                onPermissionResult(PermissionResult.Denied)
            },
            title = { Text(rationaleTitle) },
            text = { Text(rationaleText) },
            confirmButton = {
                Button(onClick = {
                    showRationale = false
                    hasProcessedResult = false // 重置状态，允许重新处理
                    permissionsState.launchMultiplePermissionRequest()
                }) {
                    Text("授予权限")
                }
            },
            dismissButton = {
                Button(onClick = { 
                    showRationale = false 
                    hasProcessedResult = true
                    onPermissionResult(PermissionResult.Denied)
                }) {
                    Text("取消")
                }
            }
        )
    }
    
    // 显示永久拒绝权限对话框
    if (showPermanentlyDenied) {
        AlertDialog(
            onDismissRequest = { 
                showPermanentlyDenied = false 
                onPermissionResult(PermissionResult.PermanentlyDenied)
            },
            title = { Text(permanentlyDeniedTitle) },
            text = { Text(permanentlyDeniedText) },
            confirmButton = {
                Button(onClick = {
                    showPermanentlyDenied = false
                    PermissionUtil.openAppSettings(context)
                }) {
                    Text("去设置")
                }
            },
            dismissButton = {
                Button(onClick = { 
                    showPermanentlyDenied = false 
                    onPermissionResult(PermissionResult.PermanentlyDenied)
                }) {
                    Text("取消")
                }
            }
        )
    }
}

/**
 * 相机权限请求组件
 */
@Composable
fun RequestCameraPermission(
    onResult: (PermissionResult) -> Unit
) {
    RequestMultiplePermissions(
        permissions = PermissionUtil.getCameraPermissions(),
        rationaleTitle = "相机权限",
        rationaleText = "需要相机权限才能拍照",
        permanentlyDeniedTitle = "相机权限被拒绝",
        permanentlyDeniedText = "您已拒绝相机权限，请在设置中手动开启",
        onPermissionResult = onResult
    )
}

/**
 * 相册权限请求组件
 */
@Composable
fun RequestGalleryPermission(
    onResult: (PermissionResult) -> Unit
) {
    RequestMultiplePermissions(
        permissions = PermissionUtil.getGalleryPermissions(),
        rationaleTitle = "相册权限",
        rationaleText = "需要相册权限才能选择图片",
        permanentlyDeniedTitle = "相册权限被拒绝",
        permanentlyDeniedText = "您已拒绝相册权限，请在设置中手动开启",
        onPermissionResult = onResult
    )
} 