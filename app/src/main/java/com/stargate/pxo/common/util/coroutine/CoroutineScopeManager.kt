package com.stargate.pxo.common.util.coroutine

import com.stargate.pxo.common.util.log.LogUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 协程作用域管理器
 * 专门管理不同模块和功能的协程作用域
 */
class CoroutineScopeManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: CoroutineScopeManager? = null
        
        fun getInstance(): CoroutineScopeManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CoroutineScopeManager().also { INSTANCE = it }
            }
        }
        
        // 预定义的作用域名称
        const val SCOPE_NETWORK = "network"
        const val SCOPE_DATABASE = "database"
        const val SCOPE_UI = "ui"
        const val SCOPE_BACKGROUND = "background"
        const val SCOPE_FILE_IO = "file_io"
        const val SCOPE_IMAGE_PROCESSING = "image_processing"
        const val SCOPE_UPLOAD = "upload"
        const val SCOPE_DOWNLOAD = "download"
        const val SCOPE_SYNC = "sync"
        const val SCOPE_ANALYTICS = "analytics"
    }
    
    private val scopeRegistry = ConcurrentHashMap<String, ManagedScope>()
    private val coroutineManager = CoroutineManager.getInstance()
    
    /**
     * 托管的作用域信息
     */
    private data class ManagedScope(
        val scope: CoroutineScope,
        val createdAt: Long = System.currentTimeMillis(),
        var lastUsed: Long = System.currentTimeMillis(),
        val jobs: MutableSet<Job> = mutableSetOf()
    )
    
    /**
     * 获取或创建指定名称的作用域
     */
    fun getScope(name: String): CoroutineScope {
        val managedScope = scopeRegistry.getOrPut(name) {
            val scope = coroutineManager.getScope(name)
            ManagedScope(scope = scope)
        }
        
        managedScope.lastUsed = System.currentTimeMillis()
        return managedScope.scope
    }
    
    /**
     * 在指定作用域中启动协程
     */
    fun launchInScope(
        scopeName: String,
        jobName: String? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        val scope = getScope(scopeName)
        val job = scope.launch {
            block()
        }
        
        // 记录Job
        scopeRegistry[scopeName]?.jobs?.add(job)
        job.invokeOnCompletion { 
            scopeRegistry[scopeName]?.jobs?.remove(job) 
        }
        
        return job
    }
    
    /**
     * 取消指定作用域
     */
    fun cancelScope(name: String) {
        scopeRegistry[name]?.let { managedScope ->
            managedScope.scope.cancel("Scope $name cancelled")
            scopeRegistry.remove(name)
            LogUtil.d("ScopeManager", "Cancelled scope: $name")
        }
    }
    
    /**
     * 取消多个作用域
     */
    fun cancelScopes(vararg names: String) {
        names.forEach { cancelScope(it) }
    }
    
    /**
     * 检查作用域是否活跃
     */
    fun isScopeActive(name: String): Boolean {
        return scopeRegistry[name]?.scope?.isActive ?: false
    }
    
    /**
     * 获取作用域中活跃的Job数量
     */
    fun getActiveJobsCount(name: String): Int {
        return scopeRegistry[name]?.jobs?.count { it.isActive } ?: 0
    }
    
    /**
     * 获取所有活跃的作用域
     */
    fun getActiveScopes(): List<String> {
        return scopeRegistry.filter { it.value.scope.isActive }.keys.toList()
    }
    
    /**
     * 清理长时间未使用的作用域
     */
    fun cleanupUnusedScopes(unusedThresholdMs: Long = 300000) { // 5分钟
        val currentTime = System.currentTimeMillis()
        val scopesToRemove = mutableListOf<String>()
        
        scopeRegistry.forEach { (name, managedScope) ->
            if (currentTime - managedScope.lastUsed > unusedThresholdMs && 
                managedScope.jobs.none { it.isActive }) {
                scopesToRemove.add(name)
            }
        }
        
        scopesToRemove.forEach { scopeName ->
            cancelScope(scopeName)
            LogUtil.d("ScopeManager", "Cleaned up unused scope: $scopeName")
        }
    }
    
    /**
     * 获取作用域统计信息
     */
    fun getScopeStats(): Map<String, ScopeStats> {
        return scopeRegistry.mapValues { (_, managedScope) ->
            ScopeStats(
                isActive = managedScope.scope.isActive,
                createdAt = managedScope.createdAt,
                lastUsed = managedScope.lastUsed,
                activeJobs = managedScope.jobs.count { it.isActive },
                totalJobs = managedScope.jobs.size
            )
        }
    }
    
    /**
     * 销毁所有作用域
     */
    fun destroyAll() {
        scopeRegistry.forEach { (name, managedScope) ->
            managedScope.scope.cancel("ScopeManager destroyed")
            LogUtil.d("ScopeManager", "Destroyed scope: $name")
        }
        scopeRegistry.clear()
    }
}

/**
 * 作用域统计信息
 */
data class ScopeStats(
    val isActive: Boolean,
    val createdAt: Long,
    val lastUsed: Long,
    val activeJobs: Int,
    val totalJobs: Int
) {
    val ageMs: Long get() = System.currentTimeMillis() - createdAt
    val idleMs: Long get() = System.currentTimeMillis() - lastUsed
}