package com.stargate.pxo.common.util

import android.content.Context
import android.net.Uri
import android.os.Environment
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.FileProvider
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图片选择工具类
 * 用于处理相机和相册图片选择
 */
object ImagePickerUtil {

    /**
     * 创建临时图片文件
     */
    suspend fun createImageFile(context: Context): File = withContext(Dispatchers.IO) {
        // 创建图片文件名
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val imageFileName = "JPEG_${timeStamp}_"
        
        // 获取外部存储目录
        val storageDir = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        
        // 创建临时文件
        return@withContext File.createTempFile(
            imageFileName,
            ".jpg",
            storageDir
        )
    }

    /**
     * 获取文件的URI
     */
    fun getUriForFile(context: Context, file: File): Uri {
        return FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )
    }
}

/**
 * 图片选择结果
 */
sealed class ImagePickerResult {
    data class Success(val uri: Uri) : ImagePickerResult()
    data class Error(val message: String) : ImagePickerResult()
    object Canceled : ImagePickerResult()
}

/**
 * 相机拍照组件
 */
@Composable
fun rememberCameraLauncher(onResult: (ImagePickerResult) -> Unit): (Uri) -> Unit {
    // 使用 rememberSaveable 来保持状态，避免重组时丢失
    var currentPhotoUri by rememberSaveable { mutableStateOf<String?>(null) }
    var isLaunching by remember { mutableStateOf(false) }
    
    val cameraLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.TakePicture()
    ) { success ->
        // 确保只处理一次结果
        if (!isLaunching) return@rememberLauncherForActivityResult
        
        isLaunching = false
        val photoUriString = currentPhotoUri
        currentPhotoUri = null
        
        if (success && photoUriString != null) {
            try {
                val uri = Uri.parse(photoUriString)
                onResult(ImagePickerResult.Success(uri))
            } catch (e: Exception) {
                onResult(ImagePickerResult.Error("Failed to parse image URI: ${e.message}"))
            }
        } else if (!success) {
            onResult(ImagePickerResult.Canceled)
        } else {
            onResult(ImagePickerResult.Error("Failed to get image"))
        }
    }
    
    return remember {
        { uri ->
            // 防止重复启动
            if (!isLaunching) {
                currentPhotoUri = uri.toString()
                isLaunching = true
                cameraLauncher.launch(uri)
            }
        }
    }
}

/**
 * 相册选择组件
 */
@Composable
fun rememberGalleryLauncher(onResult: (ImagePickerResult) -> Unit) = rememberLauncherForActivityResult(
    contract = ActivityResultContracts.GetContent()
) { uri ->
    android.util.Log.d("ImagePickerUtil", "Gallery result URI: $uri")
    uri?.let {
        android.util.Log.d("ImagePickerUtil", "Gallery selection successful: $it")
        onResult(ImagePickerResult.Success(it))
    } ?: run {
        android.util.Log.d("ImagePickerUtil", "Gallery selection canceled")
        onResult(ImagePickerResult.Canceled)
    }
} 