package com.stargate.pxo.common.util

import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Settings
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class DeviceInfoUtil @Inject constructor(
    @ApplicationContext private val context: Context
) {
    /** 获取系统语言（如 zh-CN） */
    fun getSystemLanguage(): String = Locale.getDefault().toString()

    /** 获取App包名 */
    fun getPackageName(): String = context.packageName

    /** 获取App版本名 */
    fun getVersionName(): String {
        return try {
            val pm = context.packageManager
            val info = pm.getPackageInfo(context.packageName, 0)
            info.versionName ?: ""
        } catch (e: Exception) {
            ""
        }
    }

    /** 获取App版本号（versionCode，兼容新旧API） */
    fun getVersionCode(): Long {
        return try {
            val pm = context.packageManager
            val info = pm.getPackageInfo(context.packageName, 0)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) info.longVersionCode else info.versionCode.toLong()
        } catch (e: Exception) {
            0L
        }
    }

    /** 获取设备型号 */
    fun getModel(): String = Build.MODEL ?: ""

    /** 获取设备品牌 */
    fun getBrand(): String = Build.BRAND ?: ""

    /** 获取系统版本号 */
    fun getSystemVersion(): String = Build.VERSION.RELEASE ?: ""

    /** 获取设备唯一ID（ANDROID_ID） */
    fun getUniqueDeviceId(): String {
        return try {
            Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID) ?: ""
        } catch (e: Exception) {
            ""
        }
    }
}