package com.stargate.pxo.common.util.coroutine

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 统一协程DSL
 * 提供更简洁、更流畅的协程操作API
 */
object PuxxiCoroutine {
    
    private val manager = CoroutineManager.getInstance()
    private val scopeManager = CoroutineScopeManager.getInstance()
    private val monitor = CoroutineExceptionMonitor.getInstance()
    
    /**
     * 使用UI作用域执行协程
     * 适用于UI相关操作，自动在主线程运行
     */
    fun ui(
        owner: LifecycleOwner? = null,
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        state: Lifecycle.State = Lifecycle.State.STARTED,
        action: suspend CoroutineScope.() -> Unit
    ): Job {
        return when (owner) {
            null -> manager.launchMain(scopeName = "ui") { action() }
            else -> owner.lifecycleScope.launch(dispatcher) {
                owner.lifecycle.repeatOnLifecycle(state) { action() }
            }
        }
    }
    
    /**
     * 使用IO作用域执行协程
     * 适用于IO操作如网络请求、文件读写
     */
    fun io(
        tag: String? = null,
        onError: ((Throwable) -> Unit)? = null,
        action: suspend CoroutineScope.() -> Unit
    ): Job {
        return manager.launchIO(
            scopeName = "io",
            jobName = tag,
            exceptionHandler = onError,
            block = action
        )
    }
    
    /**
     * 使用计算作用域执行协程
     * 适用于CPU密集型任务
     */
    fun compute(
        tag: String? = null,
        onError: ((Throwable) -> Unit)? = null,
        action: suspend CoroutineScope.() -> Unit
    ): Job {
        return manager.launchCPU(
            scopeName = "compute",
            jobName = tag,
            exceptionHandler = onError,
            block = action
        )
    }
    
    /**
     * 使用后台作用域执行协程
     * 适用于长时间运行的任务
     */
    fun background(
        tag: String? = null,
        onError: ((Throwable) -> Unit)? = null,
        action: suspend CoroutineScope.() -> Unit
    ): Job {
        return manager.launchHeavy(
            scopeName = "background",
            jobName = tag,
            exceptionHandler = onError,
            block = action
        )
    }
    

    /**
     * 针对ViewModel的扩展
     */
    fun ViewModel.launch(
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        onError: ((Throwable) -> Unit)? = null,
        action: suspend CoroutineScope.() -> Unit
    ): Job {
        val handler = onError?.let { 
            monitor.createMonitoredExceptionHandler(context = this::class.simpleName ?: "ViewModel", it) 
        }
        
        return viewModelScope.launch(dispatcher + (handler ?: monitor.createMonitoredExceptionHandler())) {
            action()
        }
    }
    
    /**
     * Flow扩展 - 安全收集
     */
    fun <T> Flow<T>.collect(
        scope: CoroutineScope,
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        onStart: () -> Unit = {},
        onComplete: () -> Unit = {},
        onError: (Throwable) -> Unit = {},
        collector: (T) -> Unit
    ): Job {
        return scope.launch(dispatcher) {
            this@collect
                .flowOn(Dispatchers.IO)
                .onStart { onStart() }
                .catch { 
                    onError(it)
                }
                .onCompletion { onComplete() }
                .collect { collector(it) }
        }
    }
    
    /**
     * Flow扩展 - 收集最新值
     */
    fun <T> Flow<T>.collectLatest(
        scope: CoroutineScope,
        dispatcher: CoroutineDispatcher = Dispatchers.Main,
        onStart: () -> Unit = {},
        onComplete: () -> Unit = {},
        onError: (Throwable) -> Unit = {},
        collector: (T) -> Unit
    ): Job {
        return scope.launch(dispatcher) {
            this@collectLatest
                .flowOn(Dispatchers.IO)
                .onStart { onStart() }
                .catch { 
                    onError(it)
                }
                .onCompletion { onComplete() }
                .collectLatest { collector(it) }
        }
    }

    /**
     * 任务类型
     */
    enum class TaskType {
        UI, IO, COMPUTE, BACKGROUND
    }
    
    /**
     * 延迟执行方法
     */
    suspend fun delay(timeMillis: Long) {
        kotlinx.coroutines.delay(timeMillis)
    }
    
    /**
     * 在主线程执行
     */
    suspend fun <T> withMain(block: suspend CoroutineScope.() -> T): T {
        return withContext(Dispatchers.Main) { block() }
    }
    
    /**
     * 在IO线程执行
     */
    suspend fun <T> withIO(block: suspend CoroutineScope.() -> T): T {
        return withContext(Dispatchers.IO) { block() }
    }
    
    /**
     * 在计算线程执行
     */
    suspend fun <T> withCompute(block: suspend CoroutineScope.() -> T): T {
        return withContext(manager.cpuDispatcher) { block() }
    }
    
    /**
     * 在后台线程执行
     */
    suspend fun <T> withBackground(block: suspend CoroutineScope.() -> T): T {
        return withContext(manager.heavyDispatcher) { block() }
    }
} 