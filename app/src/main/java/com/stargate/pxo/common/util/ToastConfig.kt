package com.stargate.pxo.common.util

import android.graphics.Color
import android.graphics.Typeface
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes

/**
 * Toast配置类
 * 支持各种自定义样式和行为配置
 */
data class ToastConfig(
    // 基础配置
    val useCustomStyle: Boolean = true,
    val enableQueue: Boolean = true,
    val maxQueueSize: Int = 5,
    
    // 样式配置
    @ColorInt val defaultBackgroundColor: Int = Color.parseColor("#CC000000"),
    @ColorInt val defaultTextColor: Int = Color.WHITE,
    val defaultTextSize: Float = 14f,
    val cornerRadius: Int = 12,
    val paddingHorizontal: Int = 24,
    val paddingVertical: Int = 12,
    val maxLines: Int = 3,
    val typeface: Typeface? = null,
    
    // 位置配置
    val defaultYOffset: Int = 100,
    
    // 图标配置
    val iconMargin: Int = 12,
    @DrawableRes val successIcon: Int? = android.R.drawable.ic_dialog_info,
    @DrawableRes val errorIcon: Int? = android.R.drawable.ic_dialog_alert,
    @DrawableRes val warningIcon: Int? = android.R.drawable.stat_sys_warning,
    @DrawableRes val infoIcon: Int? = android.R.drawable.ic_dialog_info,
    
    // 颜色主题配置
    @ColorInt val successColor: Int = Color.parseColor("#4CAF50"),
    @ColorInt val errorColor: Int = Color.parseColor("#F44336"),
    @ColorInt val warningColor: Int = Color.parseColor("#FF9800"),
    @ColorInt val infoColor: Int = Color.parseColor("#2196F3"),
) {
    companion object {
        /**
         * 默认配置
         */
        fun defaultConfig() = ToastConfig()
        
        /**
         * 简约风格配置
         */
        fun simpleConfig() = ToastConfig(
            useCustomStyle = false,
            enableQueue = false
        )
        
        /**
         * Material Design风格配置
         */
        fun materialConfig() = ToastConfig(
            defaultBackgroundColor = Color.parseColor("#323232"),
            defaultTextColor = Color.WHITE,
            cornerRadius = 4,
            paddingHorizontal = 24,
            paddingVertical = 14,
            defaultTextSize = 14f
        )
        
        /**
         * 圆角风格配置
         */
        fun roundedConfig() = ToastConfig(
            defaultBackgroundColor = Color.parseColor("#333333"),
            cornerRadius = 25,
            paddingHorizontal = 28,
            paddingVertical = 16
        )
        
        /**
         * 彩色主题配置
         */
        fun colorfulConfig() = ToastConfig(
            successColor = Color.parseColor("#00C851"),
            errorColor = Color.parseColor("#FF4444"),
            warningColor = Color.parseColor("#FFBB33"),
            infoColor = Color.parseColor("#33B5E5"),
            defaultBackgroundColor = Color.parseColor("#2BBBAD"),
            cornerRadius = 8
        )
        
        /**
         * 无图标配置
         */
        fun noIconConfig() = ToastConfig(
            successIcon = null,
            errorIcon = null,
            warningIcon = null,
            infoIcon = null
        )
        
        /**
         * 高对比度配置（适合视力不佳用户）
         */
        fun highContrastConfig() = ToastConfig(
            defaultBackgroundColor = Color.BLACK,
            defaultTextColor = Color.WHITE,
            defaultTextSize = 16f,
            paddingHorizontal = 32,
            paddingVertical = 18,
            cornerRadius = 0
        )
        
        /**
         * 大字体配置（适合老年用户）
         */
        fun largeFontConfig() = ToastConfig(
            defaultTextSize = 18f,
            paddingHorizontal = 32,
            paddingVertical = 20,
            cornerRadius = 12,
            maxLines = 5
        )
    }
}