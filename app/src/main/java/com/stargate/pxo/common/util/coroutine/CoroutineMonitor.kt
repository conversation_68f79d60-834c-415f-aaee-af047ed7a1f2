package com.stargate.pxo.common.util.coroutine

import com.stargate.pxo.common.util.log.LogUtil
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 协程异常监控器
 * 用于监控和处理协程中的异常
 */
class CoroutineExceptionMonitor private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: CoroutineExceptionMonitor? = null
        
        fun getInstance(): CoroutineExceptionMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CoroutineExceptionMonitor().also { INSTANCE = it }
            }
        }
    }
    
    // 异常统计
    private val exceptionCounts = ConcurrentHashMap<String, AtomicLong>()
    private val lastExceptions = ConcurrentHashMap<String, ExceptionRecord>()
    private val exceptionHandlers = ConcurrentHashMap<String, (Throwable) -> Unit>()
    
    // 全局异常监听器
    private val globalExceptionListeners = mutableSetOf<(ExceptionInfo) -> Unit>()
    
    /**
     * 异常记录
     */
    private data class ExceptionRecord(
        val exception: Throwable,
        val timestamp: Long,
        val context: String,
        val threadName: String
    )
    
    /**
     * 异常信息
     */
    data class ExceptionInfo(
        val exceptionType: String,
        val message: String?,
        val context: String,
        val timestamp: Long,
        val threadName: String,
        val stackTrace: String
    )
    
    /**
     * 创建带监控的异常处理器
     */
    fun createMonitoredExceptionHandler(
        context: String = "Unknown",
        customHandler: ((Throwable) -> Unit)? = null
    ): CoroutineExceptionHandler {
        return CoroutineExceptionHandler { coroutineContext, throwable ->
            recordException(throwable, context)
            customHandler?.invoke(throwable)
            
            // 通知全局监听器
            val exceptionInfo = ExceptionInfo(
                exceptionType = throwable.javaClass.simpleName,
                message = throwable.message,
                context = context,
                timestamp = System.currentTimeMillis(),
                threadName = Thread.currentThread().name,
                stackTrace = throwable.stackTraceToString()
            )
            
            notifyGlobalListeners(exceptionInfo)
        }
    }
    
    /**
     * 记录异常
     */
    private fun recordException(throwable: Throwable, context: String) {
        val exceptionType = throwable.javaClass.simpleName
        
        // 更新异常计数
        exceptionCounts.getOrPut(exceptionType) { AtomicLong(0) }.incrementAndGet()
        
        // 记录最新异常
        lastExceptions[exceptionType] = ExceptionRecord(
            exception = throwable,
            timestamp = System.currentTimeMillis(),
            context = context,
            threadName = Thread.currentThread().name
        )
        
        // 执行自定义处理器
        exceptionHandlers[exceptionType]?.invoke(throwable)
        
        LogUtil.e("ExceptionMonitor", "Recorded exception: $exceptionType in context: $context", throwable)
    }
    
    /**
     * 注册特定异常类型的处理器
     */
    fun registerExceptionHandler(exceptionType: String, handler: (Throwable) -> Unit) {
        exceptionHandlers[exceptionType] = handler
        LogUtil.d("ExceptionMonitor", "Registered handler for: $exceptionType")
    }
    
    /**
     * 移除异常处理器
     */
    fun unregisterExceptionHandler(exceptionType: String) {
        exceptionHandlers.remove(exceptionType)
        LogUtil.d("ExceptionMonitor", "Unregistered handler for: $exceptionType")
    }
    
    /**
     * 添加全局异常监听器
     */
    fun addGlobalExceptionListener(listener: (ExceptionInfo) -> Unit) {
        globalExceptionListeners.add(listener)
    }
    
    /**
     * 移除全局异常监听器
     */
    fun removeGlobalExceptionListener(listener: (ExceptionInfo) -> Unit) {
        globalExceptionListeners.remove(listener)
    }
    
    /**
     * 通知全局监听器
     */
    private fun notifyGlobalListeners(exceptionInfo: ExceptionInfo) {
        globalExceptionListeners.forEach { listener ->
            try {
                listener(exceptionInfo)
            } catch (e: Exception) {
                LogUtil.e("ExceptionMonitor", "Error in global exception listener", e)
            }
        }
    }
    
    /**
     * 获取异常统计
     */
    fun getExceptionStats(): Map<String, Long> {
        return exceptionCounts.mapValues { it.value.get() }
    }
    
    /**
     * 获取最近的异常
     */
    fun getRecentExceptions(): Map<String, ExceptionInfo> {
        return lastExceptions.mapValues { (_, record) ->
            ExceptionInfo(
                exceptionType = record.exception.javaClass.simpleName,
                message = record.exception.message,
                context = record.context,
                timestamp = record.timestamp,
                threadName = record.threadName,
                stackTrace = record.exception.stackTraceToString()
            )
        }
    }
    
    /**
     * 清理异常记录
     */
    fun clearExceptionRecords() {
        exceptionCounts.clear()
        lastExceptions.clear()
        LogUtil.d("ExceptionMonitor", "Cleared all exception records")
    }
    
    /**
     * 检查是否有频繁异常
     */
    fun checkForFrequentExceptions(threshold: Long = 10): List<String> {
        return exceptionCounts.filter { it.value.get() >= threshold }.keys.toList()
    }
}

/**
 * 协程性能监控器
 */
class CoroutinePerformanceMonitor private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: CoroutinePerformanceMonitor? = null
        
        fun getInstance(): CoroutinePerformanceMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CoroutinePerformanceMonitor().also { INSTANCE = it }
            }
        }
    }
    
    private val performanceRecords = ConcurrentHashMap<String, PerformanceRecord>()
    private val slowOperationThreshold = 5000L // 5秒
    
    /**
     * 性能记录
     */
    private data class PerformanceRecord(
        var totalExecutions: Long = 0,
        var totalDuration: Long = 0,
        var maxDuration: Long = 0,
        var minDuration: Long = Long.MAX_VALUE,
        var lastExecution: Long = 0,
        val slowExecutions: MutableList<Long> = mutableListOf()
    ) {
        val averageDuration: Long get() = if (totalExecutions > 0) totalDuration / totalExecutions else 0
    }
    
    /**
     * 记录协程执行时间
     */
    suspend fun <T> measureCoroutine(
        operationName: String,
        scope: CoroutineScope,
        block: suspend () -> T
    ): T {
        val startTime = System.currentTimeMillis()
        
        return try {
            val result = block()
            val duration = System.currentTimeMillis() - startTime
            recordPerformance(operationName, duration)
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            recordPerformance(operationName, duration, failed = true)
            throw e
        }
    }
    
    /**
     * 记录性能数据
     */
    private fun recordPerformance(operationName: String, duration: Long, failed: Boolean = false) {
        val record = performanceRecords.getOrPut(operationName) { PerformanceRecord() }
        
        synchronized(record) {
            record.totalExecutions++
            record.totalDuration += duration
            record.lastExecution = System.currentTimeMillis()
            
            if (duration > record.maxDuration) {
                record.maxDuration = duration
            }
            if (duration < record.minDuration) {
                record.minDuration = duration
            }
            
            // 记录慢操作
            if (duration > slowOperationThreshold) {
                record.slowExecutions.add(duration)
                LogUtil.w("PerformanceMonitor", "Slow operation detected: $operationName took ${duration}ms")
                
                // 只保留最近的10个慢操作记录
                if (record.slowExecutions.size > 10) {
                    record.slowExecutions.removeAt(0)
                }
            }
        }
        
        if (failed) {
            LogUtil.w("PerformanceMonitor", "Failed operation: $operationName took ${duration}ms")
        }
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): Map<String, PerformanceStats> {
        return performanceRecords.mapValues { (_, record) ->
            PerformanceStats(
                operationName = "",
                totalExecutions = record.totalExecutions,
                averageDuration = record.averageDuration,
                maxDuration = record.maxDuration,
                minDuration = if (record.minDuration == Long.MAX_VALUE) 0 else record.minDuration,
                slowExecutions = record.slowExecutions.size,
                lastExecution = record.lastExecution
            )
        }
    }
    
    /**
     * 获取慢操作列表
     */
    fun getSlowOperations(): List<String> {
        return performanceRecords.filter { it.value.slowExecutions.isNotEmpty() }.keys.toList()
    }
    
    /**
     * 清理性能记录
     */
    fun clearPerformanceRecords() {
        performanceRecords.clear()
        LogUtil.d("PerformanceMonitor", "Cleared all performance records")
    }
    
    /**
     * 设置慢操作阈值
     */
    fun setSlowOperationThreshold(thresholdMs: Long) {
        LogUtil.d("PerformanceMonitor", "Set slow operation threshold to ${thresholdMs}ms")
    }
}

/**
 * 性能统计信息
 */
data class PerformanceStats(
    val operationName: String,
    val totalExecutions: Long,
    val averageDuration: Long,
    val maxDuration: Long,
    val minDuration: Long,
    val slowExecutions: Int,
    val lastExecution: Long
)