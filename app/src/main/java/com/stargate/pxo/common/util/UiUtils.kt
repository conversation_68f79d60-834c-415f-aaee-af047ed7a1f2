package com.stargate.pxo.common.util

import android.content.Context
import android.widget.Toast
import androidx.compose.material3.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow

object UiUtils {
    
    fun Context.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
        Toast.makeText(this, message, duration).show()
    }
    
    @Composable
    fun ShowSnackbar(
        snackbarHostState: SnackbarHostState,
        message: String?,
        actionLabel: String? = null,
        onAction: (() -> Unit)? = null
    ) {
        val context = LocalContext.current
        LaunchedEffect(message) {
            message?.let {
                if (actionLabel != null && onAction != null) {
                    snackbarHostState.showSnackbar(
                        message = it,
                        actionLabel = actionLabel
                    ).let { result ->
                        if (result.toString() == "ActionPerformed") {
                            onAction()
                        }
                    }
                } else {
                    snackbarHostState.showSnackbar(it)
                }
            }
        }
    }
}

inline fun <T> Flow<T>.safeFlow(): Flow<Result<T>> = flow {
    <EMAIL> { throwable ->
        emit(Result.failure(throwable))
    }.collect { value ->
        emit(Result.success(value))
    }
}