package com.stargate.pxo.common.util

import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.AdjustEvent
import com.adjust.sdk.LogLevel
import com.stargate.pxo.common.util.log.LogUtil

/**
 * Adjust工具类，简化Adjust SDK的使用
 * 提供应用归因跟踪功能
 */
object AdjustUtil {
    private const val TAG = "AdjustUtil"
    private var isInitialized = false
    private var isEnabled = true
    
    /**
     * 初始化Adjust SDK
     * 
     * @param application Application实例
     * @param appToken Adjust应用令牌
     * @param environment 环境设置，生产环境为"production"，沙箱环境为"sandbox"
     * @param isDebug 是否启用调试日志
     */
    fun init(
        application: Application,
        appToken: String,
        environment: String = AdjustConfig.ENVIRONMENT_PRODUCTION,
        isDebug: Boolean = false
    ) {
        if (isInitialized) {
            LogUtil.w(TAG, "Adjust SDK already initialized")
            return
        }
        
        try {
            val config = AdjustConfig(application, appToken, environment)
            
            // 设置日志级别
            if (isDebug) {
                config.setLogLevel(LogLevel.DEBUG)
            } else {
                config.setLogLevel(LogLevel.VERBOSE)
            }
            
            // 设置归因变化监听器
            config.setOnAttributionChangedListener { attribution ->
                attribution?.let {
                    LogUtil.d(TAG, "Attribution changed: network=${it.network}, campaign=${it.campaign}, " +
                            "adgroup=${it.adgroup}, creative=${it.creative}")
                }
            }
            
            // 设置事件成功回调
            config.setOnEventTrackingSucceededListener { eventSuccessData ->
                LogUtil.d(TAG, "Event success: ${eventSuccessData?.eventToken}")
            }
            
            // 设置事件失败回调
            config.setOnEventTrackingFailedListener { eventFailureData ->
                LogUtil.e(TAG, "Event failed: ${eventFailureData?.message}")
            }
            
            // 设置会话回调
            config.setOnSessionTrackingSucceededListener { sessionSuccessData ->
                LogUtil.d(TAG, "Session success: ${sessionSuccessData?.message}")
            }
            
            config.setOnSessionTrackingFailedListener { sessionFailureData ->
                LogUtil.e(TAG, "Session failed: ${sessionFailureData?.message}")
            }
            
            // 初始化SDK
            Adjust.initSdk(config)
            
            // 注册应用生命周期回调
            registerLifecycleCallbacks(application)
            
            isInitialized = true
            LogUtil.i(TAG, "Adjust SDK initialized successfully")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to initialize Adjust SDK: ${e.message}")
        }
    }
    
    /**
     * 注册应用生命周期回调
     */
    private fun registerLifecycleCallbacks(application: Application) {
        application.registerActivityLifecycleCallbacks(object : Application.ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}
            
            override fun onActivityStarted(activity: Activity) {}
            
            override fun onActivityResumed(activity: Activity) {
                if (isEnabled) {
                    Adjust.onResume()
                }
            }
            
            override fun onActivityPaused(activity: Activity) {
                if (isEnabled) {
                    Adjust.onPause()
                }
            }
            
            override fun onActivityStopped(activity: Activity) {}
            
            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
            
            override fun onActivityDestroyed(activity: Activity) {}
        })
    }
    
    /**
     * 跟踪简单事件
     * 
     * @param eventToken 事件令牌
     */
    fun trackEvent(eventToken: String) {
        if (!isInitialized || !isEnabled) return
        
        try {
            val event = AdjustEvent(eventToken)
            Adjust.trackEvent(event)
            LogUtil.d(TAG, "Tracked event: $eventToken")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to track event: ${e.message}")
        }
    }
    
    /**
     * 跟踪带有值的事件
     * 
     * @param eventToken 事件令牌
     * @param value 事件值
     * @param currency 货币代码（如 "USD"）
     */
    fun trackRevenueEvent(eventToken: String, value: Double, currency: String) {
        if (!isInitialized || !isEnabled) return
        
        try {
            val event = AdjustEvent(eventToken)
            event.setRevenue(value, currency)
            Adjust.trackEvent(event)
            LogUtil.d(TAG, "Tracked revenue event: $eventToken, value: $value $currency")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to track revenue event: ${e.message}")
        }
    }
    
    /**
     * 跟踪带有自定义参数的事件
     * 
     * @param eventToken 事件令牌
     * @param params 自定义参数
     */
    fun trackEventWithParams(eventToken: String, params: Map<String, String>) {
        if (!isInitialized || !isEnabled) return
        
        try {
            val event = AdjustEvent(eventToken)
            
            // 添加自定义参数
            params.forEach { (key, value) ->
                event.addCallbackParameter(key, value)
            }
            
            Adjust.trackEvent(event)
            LogUtil.d(TAG, "Tracked event with params: $eventToken, params: $params")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to track event with params: ${e.message}")
        }
    }
    
    /**
     * 添加会话回调参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    fun addSessionParameter(key: String, value: String) {
        if (!isInitialized || !isEnabled) return
        
        try {
            Adjust.addGlobalCallbackParameter(key, value)
            LogUtil.d(TAG, "Added session parameter: $key=$value")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to add session parameter: ${e.message}")
        }
    }
    
    /**
     * 设置用户ID
     * 
     * @param userId 用户ID
     */
    fun setUserId(userId: String) {
        if (!isInitialized || !isEnabled) return
        
        try {
//            Adjust.setReferrer(userId)
            LogUtil.d(TAG, "Set user ID: $userId")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to set user ID: ${e.message}")
        }
    }
    
    /**
     * 启用或禁用跟踪
     * 
     * @param enabled 是否启用
     */
    fun setEnabled(enabled: Boolean) {
        isEnabled = enabled
        if (isInitialized) {
            if (enabled) Adjust.enable() else Adjust.disable()
            LogUtil.d(TAG, "Adjust tracking ${if (enabled) "enabled" else "disabled"}")
        }
    }
    
    /**
     * 获取当前归因数据
     * 
     * @param context Context实例
     * @return 归因数据的字符串表示，如果没有则返回null
     */
    fun getAttribution(context: Context): String? {
        if (!isInitialized || !isEnabled) return null
        
        return try {
            val attribution = Adjust.getAttribution {  }
            attribution?.toString()
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to get attribution: ${e.message}")
            null
        }
    }
    
    /**
     * 获取Adjust设备ID
     * 
     * @param context Context实例
     * @return Adjust设备ID，如果没有则返回null
     */
    fun getAdid(context: Context): String? {
        if (!isInitialized || !isEnabled) return null
        
        return try {
//            Adjust.getAdid()
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to get ADID: ${e.message}")
            null
        } as String?
    }
    
}