package com.stargate.pxo.common.util

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.Bundle
import android.view.Gravity
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.ColorInt
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import com.stargate.pxo.common.util.log.LogUtil
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentLinkedQueue

class ToastUtil private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: ToastUtil? = null
        private var context: WeakReference<Context>? = null
        private var isInitialized = false
        
        // Toast队列管理
        private val toastQueue = ConcurrentLinkedQueue<Toast>()
        private var currentToast: Toast? = null
        
        // 配置参数
        private var config = ToastConfig()
        
        // 应用生命周期回调
        private var lifecycleCallback: Application.ActivityLifecycleCallbacks? = null
        private var activityCount = 0
        
        fun getInstance(): ToastUtil {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ToastUtil().also { INSTANCE = it }
            }
        }
        
        /**
         * 初始化ToastUtil
         */
        fun init(application: Application, toastConfig: ToastConfig? = null) {
            context = WeakReference(application.applicationContext)
            
            // 根据设备兼容性选择最佳配置
            config = toastConfig ?: DeviceCompatibilityUtil.getCompatibleToastConfig()
            
            isInitialized = true
            
            // 应用设备兼容性修复
            DeviceCompatibilityUtil.applyCompatibilityFixes(application)
            
            // 注册应用生命周期监听
            setupLifecycleCallbacks(application)
            
            LogUtil.d("ToastUtil", "ToastUtil initialized successfully")
            LogUtil.d("ToastUtil", "Device info: ${DeviceCompatibilityUtil.getDeviceInfo()}")
        }
        
        /**
         * 设置应用生命周期监听，用于管理Toast的生命周期
         */
        private fun setupLifecycleCallbacks(application: Application) {
            lifecycleCallback = object : Application.ActivityLifecycleCallbacks {
                override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                    activityCount++
                }
                
                override fun onActivityStarted(activity: Activity) {}
                override fun onActivityResumed(activity: Activity) {}
                override fun onActivityPaused(activity: Activity) {}
                override fun onActivityStopped(activity: Activity) {}
                override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
                
                override fun onActivityDestroyed(activity: Activity) {
                    activityCount--
                    // 当所有Activity都被销毁时，取消所有Toast
                    if (activityCount <= 0) {
                        cancelAllToasts()
                    }
                }
            }
            
            lifecycleCallback?.let { callback ->
                application.registerActivityLifecycleCallbacks(callback)
            }
        }
        
        // =================== 基础Toast方法 ===================
        
        /**
         * 显示短时Toast
         */
        @JvmStatic
        fun showShort(message: String) {
            show(message, Toast.LENGTH_SHORT)
        }
        
        @JvmStatic
        fun showShort(@StringRes messageRes: Int) {
            show(getStringFromRes(messageRes), Toast.LENGTH_SHORT)
        }
        
        @JvmStatic
        fun showShort(context: Context, message: String) {
            show(context, message, Toast.LENGTH_SHORT)
        }
        
        /**
         * 显示长时Toast
         */
        @JvmStatic
        fun showLong(message: String) {
            show(message, Toast.LENGTH_LONG)
        }
        
        @JvmStatic
        fun showLong(@StringRes messageRes: Int) {
            show(getStringFromRes(messageRes), Toast.LENGTH_LONG)
        }
        
        @JvmStatic
        fun showLong(context: Context, message: String) {
            show(context, message, Toast.LENGTH_LONG)
        }
        
        // =================== 样式化Toast方法 ===================
        
        /**
         * 显示成功Toast
         */
        @JvmStatic
        fun showSuccess(message: String) {
            showCustomToast(
                message = message,
                backgroundColor = config.successColor,
                textColor = Color.WHITE,
                icon = config.successIcon
            )
        }
        
        /**
         * 显示错误Toast
         */
        @JvmStatic
        fun showError(message: String) {
            showCustomToast(
                message = message,
                backgroundColor = config.errorColor,
                textColor = Color.WHITE,
                icon = config.errorIcon
            )
        }
        
        /**
         * 显示警告Toast
         */
        @JvmStatic
        fun showWarning(message: String) {
            showCustomToast(
                message = message,
                backgroundColor = config.warningColor,
                textColor = Color.WHITE,
                icon = config.warningIcon
            )
        }
        
        /**
         * 显示信息Toast
         */
        @JvmStatic
        fun showInfo(message: String) {
            showCustomToast(
                message = message,
                backgroundColor = config.infoColor,
                textColor = Color.WHITE,
                icon = config.infoIcon
            )
        }
        
        // =================== 自定义Toast方法 ===================
        
        /**
         * 显示自定义样式的Toast
         */
        @JvmStatic
        fun showCustomToast(
            message: String,
            duration: Int = Toast.LENGTH_SHORT,
            @ColorInt backgroundColor: Int = config.defaultBackgroundColor,
            @ColorInt textColor: Int = config.defaultTextColor,
            textSize: Float = config.defaultTextSize,
            @DrawableRes icon: Int? = null,
            gravity: Int = Gravity.BOTTOM, // 使用 Toast 位置，不是 CENTER_VERTICAL
            xOffset: Int = 0,
            yOffset: Int = config.defaultYOffset
        ) {
            if (!isInitialized) {
                LogUtil.w("ToastUtil", "ToastUtil not initialized, using fallback")
                showFallbackToast(message, duration)
                return
            }
            
            runOnUiThread {
                try {
                    val toast = createToast(
                        message = message,
                        duration = duration,
                        backgroundColor = backgroundColor,
                        textColor = textColor,
                        textSize = textSize,
                        icon = icon,
                        gravity = gravity,
                        xOffset = xOffset,
                        yOffset = yOffset
                    )
                    
                    enqueueToast(toast)
                } catch (e: Exception) {
                    LogUtil.e("ToastUtil", "Error showing custom toast", e)
                    showFallbackToast(message, duration)
                }
            }
        }
        
        /**
         * 显示带图标的Toast
         */
        @JvmStatic
        fun showWithIcon(
            message: String,
            @DrawableRes iconRes: Int,
            duration: Int = Toast.LENGTH_SHORT
        ) {
            showCustomToast(
                message = message,
                duration = duration,
                icon = iconRes
            )
        }
        
        // =================== Toast管理方法 ===================
        
        /**
         * 取消当前显示的Toast
         */
        @JvmStatic
        fun cancelCurrent() {
            runOnUiThread {
                currentToast?.cancel()
                currentToast = null
            }
        }
        
        /**
         * 取消所有Toast（包括队列中的）
         */
        @JvmStatic
        fun cancelAllToasts() {
            runOnUiThread {
                try {
                    // 取消当前显示的Toast
                    currentToast?.cancel()
                    currentToast = null
                    
                    // 取消队列中的所有Toast
                    while (toastQueue.isNotEmpty()) {
                        val toast = toastQueue.poll()
                        toast?.cancel()
                    }
                    
                    // 取消Toast定时器Job
                    // PuxxiCoroutine.cancelJob("ToastTimer") // This line is removed as per the new_code
                    
                    LogUtil.d("ToastUtil", "All toasts cancelled")
                } catch (e: Exception) {
                    LogUtil.e("ToastUtil", "Error cancelling toasts", e)
                }
            }
        }
        
        /**
         * 检查是否有Toast正在显示
         */
        @JvmStatic
        fun isShowing(): Boolean {
            return currentToast != null
        }
        
        /**
         * 获取队列中待显示的Toast数量
         */
        @JvmStatic
        fun getQueueSize(): Int {
            return toastQueue.size
        }
        
        // =================== 私有方法 ===================
        
        private fun show(message: String, duration: Int) {
            getContext()?.let { ctx ->
                show(ctx, message, duration)
            } ?: run {
                LogUtil.w("ToastUtil", "Context is null, cannot show toast")
            }
        }
        
        private fun show(context: Context, message: String, duration: Int) {
            if (!isInitialized) {
                showFallbackToast(message, duration)
                return
            }
            
            runOnUiThread {
                try {
                    val toast = if (config.useCustomStyle && canUseCustomToast()) {
                        createToast(message = message, duration = duration)
                    } else {
                        createSystemToast(context, message, duration)
                    }
                    
                    enqueueToast(toast)
                } catch (e: Exception) {
                    LogUtil.e("ToastUtil", "Error showing toast", e)
                    showFallbackToast(message, duration)
                }
            }
        }
        
        /**
         * 检查当前环境是否可以使用自定义Toast
         */
        private fun canUseCustomToast(): Boolean {
            return Build.VERSION.SDK_INT < Build.VERSION_CODES.R && 
                   DeviceCompatibilityUtil.supportsCustomToast()
        }
        
        /**
         * 创建统一的Toast，根据设备能力选择合适的实现
         */
        private fun createToast(
            message: String,
            duration: Int = Toast.LENGTH_SHORT,
            @ColorInt backgroundColor: Int = config.defaultBackgroundColor,
            @ColorInt textColor: Int = config.defaultTextColor,
            textSize: Float = config.defaultTextSize,
            @DrawableRes icon: Int? = null,
            gravity: Int = Gravity.BOTTOM,
            xOffset: Int = 0,
            yOffset: Int = config.defaultYOffset
        ): Toast {
            val context = getContext() ?: throw IllegalStateException("Context is null")
            
            // 检查是否可以使用自定义Toast
            return if (canUseCustomToast()) {
                createCustomToast(
                    context = context,
                    message = message,
                    duration = duration,
                    backgroundColor = backgroundColor,
                    textColor = textColor,
                    textSize = textSize,
                    icon = icon,
                    gravity = gravity,
                    xOffset = xOffset,
                    yOffset = yOffset
                )
            } else {
                // 回退到系统Toast
                createSystemToast(context, message, duration, gravity, xOffset, yOffset)
            }
        }
        
        @SuppressLint("ShowToast")
        private fun createSystemToast(
            context: Context, 
            message: String, 
            duration: Int,
            gravity: Int = Gravity.BOTTOM,
            xOffset: Int = 0,
            yOffset: Int = config.defaultYOffset
        ): Toast {
            val toast = Toast.makeText(context, message, duration)
            
            // 尝试设置位置（如果支持）
            try {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R && 
                    DeviceCompatibilityUtil.supportsToastGravity()) {
                    toast.setGravity(gravity, xOffset, yOffset)
                }
            } catch (e: Exception) {
                LogUtil.w("ToastUtil", "Failed to set toast gravity$e")
            }
            
            return toast
        }
        
        private fun createCustomToast(
            context: Context,
            message: String,
            duration: Int = Toast.LENGTH_SHORT,
            @ColorInt backgroundColor: Int = config.defaultBackgroundColor,
            @ColorInt textColor: Int = config.defaultTextColor,
            textSize: Float = config.defaultTextSize,
            @DrawableRes icon: Int? = null,
            gravity: Int = Gravity.BOTTOM,
            xOffset: Int = 0,
            yOffset: Int = config.defaultYOffset
        ): Toast {
            val toast = Toast(context)
            toast.duration = duration
            
            try {
                // 创建自定义布局
                val layout = LinearLayout(context).apply {
                    orientation = LinearLayout.HORIZONTAL
                    setPadding(
                        config.paddingHorizontal,
                        config.paddingVertical,
                        config.paddingHorizontal,
                        config.paddingVertical
                    )
                    // 这里使用 Gravity.CENTER_VERTICAL 是正确的，因为是设置 LinearLayout 的内容对齐方式
                    this.gravity = Gravity.CENTER_VERTICAL
                    
                    // 设置背景
                    background = createToastBackground(backgroundColor)
                }
                
                // 添加图标（如果有）
                icon?.let { iconRes ->
                    try {
                        val iconView = ImageView(context).apply {
                            setImageResource(iconRes)
                            val iconSize = (textSize * 1.2f).toInt()
                            layoutParams = LinearLayout.LayoutParams(iconSize, iconSize).apply {
                                setMargins(0, 0, config.iconMargin, 0)
                            }
                        }
                        layout.addView(iconView)
                    } catch (e: Exception) {
                        LogUtil.w("ToastUtil", "Failed to add icon to toast$e")
                    }
                }
                
                // 添加文本
                val textView = TextView(context).apply {
                    text = message
                    setTextColor(textColor)
                    this.textSize = textSize
                    // 这里使用 Gravity.CENTER_VERTICAL 是正确的，因为是设置 TextView 的文本对齐方式
                    this.gravity = Gravity.CENTER_VERTICAL
                    maxLines = config.maxLines
                    
                    // 设置字体（如果配置了）
                    config.typeface?.let { typeface ->
                        this.typeface = typeface
                    }
                }
                layout.addView(textView)
                
                // 设置自定义视图
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                    // 仅在 Android 11 以下设置自定义视图
                    toast.view = layout
                    
                    // 设置位置
                    if (DeviceCompatibilityUtil.supportsToastGravity()) {
                        toast.setGravity(gravity, xOffset, yOffset)
                    }
                } else {
                    // Android 11+ 不支持自定义视图，回退到系统Toast
                    return createSystemToast(context, message, duration, gravity, xOffset, yOffset)
                }
            } catch (e: Exception) {
                LogUtil.e("ToastUtil", "Error creating custom toast", e)
                // 出错时回退到系统Toast
                return createSystemToast(context, message, duration, gravity, xOffset, yOffset)
            }
            
            return toast
        }
        
        private fun createToastBackground(@ColorInt backgroundColor: Int): Drawable {
            return GradientDrawable().apply {
                setColor(backgroundColor)
                cornerRadius = config.cornerRadius.toFloat()
                
                // 添加阴影效果（如果支持）
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    setStroke(1, Color.parseColor("#20000000"))
                }
            }
        }
        
        private fun enqueueToast(toast: Toast) {
            if (currentToast == null) {
                // 没有Toast在显示，直接显示
                showToastNow(toast)
            } else {
                // 有Toast在显示，加入队列
                if (config.enableQueue) {
                    // 限制队列大小
                    if (toastQueue.size >= config.maxQueueSize) {
                        toastQueue.poll()?.cancel()
                    }
                    toastQueue.offer(toast)
                } else {
                    // 不使用队列，取消当前Toast并显示新的
                    currentToast?.cancel()
                    showToastNow(toast)
                }
            }
        }
        
        private fun showToastNow(toast: Toast) {
            try {
                currentToast = toast
                toast.show()
                
                // 使用PuxxiCoroutine设置定时器
                val delay = if (toast.duration == Toast.LENGTH_LONG) 3500L else 2000L
                PuxxiCoroutine.ui{
                    PuxxiCoroutine.delay(delay)
                    currentToast = null
                    showNextToast()
                }
                
            } catch (e: Exception) {
                LogUtil.e("ToastUtil", "Error showing toast", e)
                currentToast = null
                showNextToast()
            }
        }

        private fun showNextToast() {
            val nextToast = toastQueue.poll()
            nextToast?.let { toast ->
                showToastNow(toast)
            }
        }
        
        private fun showFallbackToast(message: String, duration: Int) {
            getContext()?.let { context ->
                runOnUiThread {
                    try {
                        Toast.makeText(context, message, duration).show()
                    } catch (e: Exception) {
                        LogUtil.e("ToastUtil", "Failed to show fallback toast", e)
                    }
                }
            }
        }
        
        private fun getContext(): Context? {
            return context?.get()
        }
        
        private fun getStringFromRes(@StringRes stringRes: Int): String {
            return try {
                getContext()?.getString(stringRes) ?: ""
            } catch (e: Exception) {
                LogUtil.w("ToastUtil", "Failed to get string from resource$e")
                ""
            }
        }
        
        private fun runOnUiThread(action: () -> Unit) {
            PuxxiCoroutine.ui { action() }
        }
        
        // =================== 配置更新方法 ===================
        
        /**
         * 更新Toast配置
         */
        @JvmStatic
        fun updateConfig(newConfig: ToastConfig) {
            config = newConfig
            LogUtil.d("ToastUtil", "Toast config updated")
        }
        
        /**
         * 获取当前配置
         */
        @JvmStatic
        fun getConfig(): ToastConfig {
            return config
        }
        
        /**
         * 重置为默认配置
         */
        @JvmStatic
        fun resetConfig() {
            config = ToastConfig()
            LogUtil.d("ToastUtil", "Toast config reset to default")
        }
        
        /**
         * 销毁ToastUtil，释放资源
         */
        @JvmStatic
        fun destroy() {
            cancelAllToasts()
            
            // 取消ToastUtil相关的协程作用域
            // PuxxiCoroutine.cancelScope("ToastUtil") // This line is removed as per the new_code
            
            lifecycleCallback?.let { callback ->
                (getContext() as? Application)?.unregisterActivityLifecycleCallbacks(callback)
            }
            
            context?.clear()
            context = null
            isInitialized = false
            
            LogUtil.d("ToastUtil", "ToastUtil destroyed")
        }
    }
}