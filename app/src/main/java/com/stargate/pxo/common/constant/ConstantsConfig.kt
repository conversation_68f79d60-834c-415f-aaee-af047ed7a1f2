package com.stargate.pxo.common.constant

object ApiConstants {

    const val MAIN = "puxxi.club"
    const val APP_API_DOMAIN = "test-app.$MAIN"
    const val BASE_URL = "http://$APP_API_DOMAIN"

    const val WEB_URL = "http://chat.$MAIN"

    const val PRIVACY_POLICY = "http://chat.$MAIN/privacyPolicy.html"
    const val TERM_CONDITIONS = "http://chat.$MAIN/termConditions.html"


    const val IM_URL = "http://test-im.$MAIN"
    const val LOG_URL = "http://test-log.$MAIN"

    const val AES = "AES/ECB/PKCS5Padding"


    class BroadcasterStatus {
        companion object {
            const val STATUS_OFFLINE = "offline"
            const val STATUS_ONLINE = "online"
            const val STATUS_BUSY = "busy"
            const val STATUS_CALLING = "calling"
            const val STATUS_CALLING_OUT = "calling_out"
            const val STATUS_CALLING_IN = "calling_in"
            const val STATUS_CALLING_MATCH = "calling_match"
            const val STATUS_CALLING_MATCH_OUT = "calling_match_out"
            const val STATUS_CALLING_MATCH_IN = "calling_match_in"
        }
    }

}
