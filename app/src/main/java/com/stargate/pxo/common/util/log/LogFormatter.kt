package com.stargate.pxo.common.util.log

import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class LogFormatter(private val config: LogConfig) {
    
    private val gson by lazy {
        GsonBuilder()
            .setPrettyPrinting()
            .serializeNulls()
            .create()
    }
    
    private val timeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
    
    companion object {
        private const val TOP_LEFT_CORNER = '┌'
        private const val BOTTOM_LEFT_CORNER = '└'
        private const val MIDDLE_CORNER = '├'
        private const val HORIZONTAL_LINE = '─'
        private const val VERTICAL_LINE = "│ "
        private const val DOUBLE_DIVIDER = "────────────────────────────────────────────────────────"
        private const val SINGLE_DIVIDER = "┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄"
        private  val TOP_BORDER = TOP_LEFT_CORNER + DOUBLE_DIVIDER + DOUBLE_DIVIDER
        private  val BOTTOM_BORDER = BOTTOM_LEFT_CORNER + DOUBLE_DIVIDER + DOUBLE_DIVIDER
        private  val MIDDLE_BORDER = MIDDLE_CORNER + SINGLE_DIVIDER + SINGLE_DIVIDER
    }
    
    fun formatLog(
        level: LogLevel,
        tag: String,
        message: String,
        throwable: Throwable? = null
    ): List<String> {
        val logs = mutableListOf<String>()
        
        if (config.enableBorder) {
            logs.add(TOP_BORDER)
        }
        
        // 添加时间和线程信息
        if (config.showThreadInfo) {
            val threadInfo = buildString {
                append("Time: ${timeFormat.format(Date())} ")
                append("Thread: ${Thread.currentThread().name}")
            }
            logs.add(formatBorderLine(threadInfo))
        }
        
        // 添加方法调用信息
        if (config.showMethodInfo) {
            val methodInfo = getMethodInfo()
            if (methodInfo.isNotEmpty()) {
                logs.add(formatBorderLine(methodInfo))
            }
        }
        
        if ((config.showThreadInfo || config.showMethodInfo) && config.enableBorder) {
            logs.add(MIDDLE_BORDER)
        }
        
        // 格式化消息内容
        val formattedMessages = formatMessage(message)
        formattedMessages.forEach { msg ->
            logs.add(formatBorderLine(msg))
        }
        
        // 添加异常信息
        throwable?.let { t ->
            if (config.enableBorder) {
                logs.add(MIDDLE_BORDER)
            }
            val exceptionLines = formatException(t)
            exceptionLines.forEach { line ->
                logs.add(formatBorderLine(line))
            }
        }
        
        if (config.enableBorder) {
            logs.add(BOTTOM_BORDER)
        }
        
        return logs
    }
    
    private fun formatBorderLine(message: String): String {
        return if (config.enableBorder) {
            VERTICAL_LINE + message
        } else {
            message
        }
    }
    
    private fun formatMessage(message: String): List<String> {
        // 尝试格式化JSON
        val jsonFormatted = if (config.enableJsonFormat) {
            tryFormatJson(message)
        } else null
        
        val content = jsonFormatted ?: message
        
        // 分行处理，避免日志截断
        return splitLongMessage(content)
    }
    
    private fun tryFormatJson(message: String): String? {
        return try {
            val trimmed = message.trim()
            when {
                trimmed.startsWith("{") && trimmed.endsWith("}") -> {
                    val jsonElement = JsonParser.parseString(trimmed)
                    gson.toJson(jsonElement)
                }
                trimmed.startsWith("[") && trimmed.endsWith("]") -> {
                    val jsonElement = JsonParser.parseString(trimmed)
                    gson.toJson(jsonElement)
                }
                else -> null
            }
        } catch (e: Exception) {
            null
        }
    }
    
    private fun splitLongMessage(message: String): List<String> {
        val lines = message.split('\n')
        val result = mutableListOf<String>()
        
        lines.forEach { line ->
            if (line.length <= config.maxLineLength) {
                result.add(line)
            } else {
                // 分割长行
                var remaining = line
                while (remaining.length > config.maxLineLength) {
                    val chunk = remaining.substring(0, config.maxLineLength)
                    result.add(chunk)
                    remaining = remaining.substring(config.maxLineLength)
                }
                if (remaining.isNotEmpty()) {
                    result.add(remaining)
                }
            }
        }
        
        return result
    }
    
    private fun getMethodInfo(): String {
        return try {
            val stackTrace = Thread.currentThread().stackTrace
            var index = 0
            
            // 找到调用LogUtil的位置
            for (i in stackTrace.indices) {
                val element = stackTrace[i]
                if (element.className.contains("LogUtil") || 
                    element.className.contains("Logger")) {
                    index = i + config.methodOffset
                    break
                }
            }
            
            if (index < stackTrace.size) {
                val element = stackTrace[index + config.showStackOffset]
                val className = element.className.substringAfterLast('.')
                val methodName = element.methodName
                val lineNumber = element.lineNumber
                "($className.java:$lineNumber) $methodName()"
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    private fun formatException(throwable: Throwable): List<String> {
        val result = mutableListOf<String>()
        
        result.add("Exception: ${throwable.javaClass.simpleName}")
        result.add("Message: ${throwable.message ?: "No message"}")
        
        throwable.stackTrace.take(10).forEach { element ->
            result.add("    at ${element.className}.${element.methodName}(${element.fileName}:${element.lineNumber})")
        }
        
        if (throwable.stackTrace.size > 10) {
            result.add("    ... ${throwable.stackTrace.size - 10} more")
        }
        
        throwable.cause?.let { cause ->
            result.add("")
            result.add("Caused by: ${cause.javaClass.simpleName}: ${cause.message ?: "No message"}")
            cause.stackTrace.take(3).forEach { element ->
                result.add("    at ${element.className}.${element.methodName}(${element.fileName}:${element.lineNumber})")
            }
        }
        
        return result
    }
    
    fun formatForFile(
        level: LogLevel,
        tag: String,
        message: String,
        throwable: Throwable? = null
    ): String {
        return buildString {
            append("${timeFormat.format(Date())} ")
            append("${level.tag}/$tag ")
            append("[${Thread.currentThread().name}] ")
            
            if (config.showMethodInfo) {
                val methodInfo = getMethodInfo()
                if (methodInfo.isNotEmpty()) {
                    append("$methodInfo ")
                }
            }
            
            append(message)
            
            throwable?.let { t ->
                append("\n")
                append("Exception: ${t.javaClass.simpleName}")
                append("\nMessage: ${t.message ?: "No message"}")
                append("\nStackTrace:\n")
                append(t.stackTraceToString())
            }
        }
    }
}