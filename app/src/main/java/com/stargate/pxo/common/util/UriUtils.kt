package com.stargate.pxo.common.util

import android.content.ContentResolver
import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.OpenableColumns
import androidx.core.content.FileProvider
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Uri工具类
 * 使用依赖注入获取Context，无需在方法调用时传入Context参数
 */
@Singleton
class UriUtils @Inject constructor(
    @ApplicationContext private val context: Context
) {

    /**
     * 资源路径转Uri
     * @param resPath 资源路径，格式：res/raw/filename 或 raw/filename
     * @return Uri对象
     */
    fun res2Uri(resPath: String): Uri? {
        val cleanPath = resPath.removePrefix("res/")
        return Uri.parse("android.resource://com.stargate.pxo/$cleanPath")
    }

    /**
     * 文件转Uri
     * @param file 文件对象
     * @return Uri对象，Android N+使用FileProvider
     */
    fun file2Uri(file: File): Uri {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )
        } else {
            Uri.fromFile(file)
        }
    }

    /**
     * Uri转文件
     * @param uri Uri对象
     * @return File对象，如果转换失败则将Uri内容复制到缓存目录
     */
    suspend fun uri2File(uri: Uri): File? = withContext(Dispatchers.IO) {
        when (uri.scheme) {
            "file" -> {
                uri.path?.let { File(it) }
            }
            "content" -> {
                // 尝试获取真实路径
                getRealPathFromUri(uri)?.let { File(it) }
                    ?: copyUriToCache(uri)
            }
            else -> null
        }
    }

    /**
     * Uri转InputStream
     * @param uri Uri对象
     * @return InputStream对象
     */
    fun uri2InputStream(uri: Uri): InputStream? {
        return try {
            context.contentResolver.openInputStream(uri)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 获取Uri的文件名
     * @param uri Uri对象
     * @return 文件名
     */
    fun getFileName(uri: Uri): String? {
        return when (uri.scheme) {
            "file" -> uri.lastPathSegment
            "content" -> getFileNameFromContentUri(uri)
            else -> null
        }
    }

    /**
     * 获取Uri的文件大小
     * @param uri Uri对象
     * @return 文件大小（字节），失败返回-1
     */
    fun getFileSize(uri: Uri): Long {
        return try {
            when (uri.scheme) {
                "file" -> {
                    uri.path?.let { File(it).length() } ?: -1L
                }
                "content" -> {
                    context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                        val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                        if (sizeIndex != -1 && cursor.moveToFirst()) {
                            cursor.getLong(sizeIndex)
                        } else {
                            -1L
                        }
                    } ?: -1L
                }
                else -> -1L
            }
        } catch (e: Exception) {
            -1L
        }
    }

    /**
     * 检查Uri是否存在
     * @param uri Uri对象
     * @return 是否存在
     */
    fun isUriExists(uri: Uri): Boolean {
        return try {
            when (uri.scheme) {
                "file" -> {
                    uri.path?.let { File(it).exists() } ?: false
                }
                "content" -> {
                    context.contentResolver.openInputStream(uri)?.use { true } ?: false
                }
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 获取Uri的MIME类型
     * @param uri Uri对象
     * @return MIME类型
     */
    fun getMimeType(uri: Uri): String? {
        return when (uri.scheme) {
            "content" -> context.contentResolver.getType(uri)
            "file" -> {
                uri.path?.let { path ->
                    val extension = File(path).extension
                    android.webkit.MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
                }
            }
            else -> null
        }
    }

    /**
     * 从content Uri获取真实路径
     */
    private fun getRealPathFromUri(uri: Uri): String? {
        return when {
            // DocumentProvider
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT && DocumentsContract.isDocumentUri(context, uri) -> {
                when {
                    // ExternalStorageProvider
                    isExternalStorageDocument(uri) -> {
                        val docId = DocumentsContract.getDocumentId(uri)
                        val split = docId.split(":")
                        val type = split[0]

                        if ("primary".equals(type, ignoreCase = true)) {
                            "${Environment.getExternalStorageDirectory()}/${split[1]}"
                        } else {
                            null
                        }
                    }
                    // DownloadsProvider
                    isDownloadsDocument(uri) -> {
                        val id = DocumentsContract.getDocumentId(uri)
                        if (id.startsWith("raw:")) {
                            id.replaceFirst("raw:", "")
                        } else {
                            getDataColumn(Uri.parse("content://downloads/public_downloads"), "_id=?", arrayOf(id))
                        }
                    }
                    // MediaProvider
                    isMediaDocument(uri) -> {
                        val docId = DocumentsContract.getDocumentId(uri)
                        val split = docId.split(":")
                        val type = split[0]

                        val contentUri = when (type) {
                            "image" -> MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                            "video" -> MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                            "audio" -> MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                            else -> null
                        }

                        contentUri?.let {
                            getDataColumn(it, "_id=?", arrayOf(split[1]))
                        }
                    }
                    else -> null
                }
            }
            // MediaStore (and general)
            "content".equals(uri.scheme, ignoreCase = true) -> {
                if (isGooglePhotosUri(uri)) {
                    uri.lastPathSegment
                } else {
                    getDataColumn(uri, null, null)
                }
            }
            // File
            "file".equals(uri.scheme, ignoreCase = true) -> {
                uri.path
            }
            else -> null
        }
    }

    /**
     * 获取数据列
     */
    private fun getDataColumn(uri: Uri, selection: String?, selectionArgs: Array<String>?): String? {
        val column = "_data"
        val projection = arrayOf(column)

        return try {
            context.contentResolver.query(uri, projection, selection, selectionArgs, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val index = cursor.getColumnIndexOrThrow(column)
                    cursor.getString(index)
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从content Uri获取文件名
     */
    private fun getFileNameFromContentUri(uri: Uri): String? {
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                val nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (nameIndex != -1 && cursor.moveToFirst()) {
                    cursor.getString(nameIndex)
                } else {
                    null
                }
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 复制Uri内容到缓存目录
     */
    private fun copyUriToCache(uri: Uri): File? {
        return try {
            val fileName = getFileName(uri) ?: "temp_${System.currentTimeMillis()}"
            val cacheFile = File(context.cacheDir, fileName)

            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                FileOutputStream(cacheFile).use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            cacheFile
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 检查是否为外部存储文档
     */
    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    /**
     * 检查是否为下载文档
     */
    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    /**
     * 检查是否为媒体文档
     */
    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }

    /**
     * 检查是否为Google Photos Uri
     */
    private fun isGooglePhotosUri(uri: Uri): Boolean {
        return "com.google.android.apps.photos.content" == uri.authority
    }
}