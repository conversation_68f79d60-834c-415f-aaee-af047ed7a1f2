# BroadcasterStatusManager 使用指南

## 🎯 功能概述

BroadcasterStatusManager 是一个主播状态管理类，提供以下功能：

1. **根据主播ID批量获取状态** - 调用 USER_GETUSERLISTONLINESTATUSPOSTV3 接口
2. **状态缓存管理** - 使用 ConcurrentHashMap 进行线程安全的缓存
3. **Flow 状态监听** - 提供 StateFlow 供其他 Screen 监听状态变化
4. **自动状态更新** - 接口返回后自动更新缓存并通知监听者

## 🏗️ 架构设计

### 核心组件

```kotlin
@Singleton
class BroadcasterStatusManager @Inject constructor(
    private val broadcasterInfoRepository: BroadcasterInfoRepository
) {
    // 状态缓存
    private val statusCache = ConcurrentHashMap<String, BroadcasterStatus>()
    
    // 状态变化 Flow
    private val _statusUpdates = MutableStateFlow<Map<String, BroadcasterStatus>>(emptyMap())
    val statusUpdates: StateFlow<Map<String, BroadcasterStatus>> = _statusUpdates.asStateFlow()
}
```

### 数据流

```
HomeScreen/其他Screen
    ↓ 请求状态
BroadcasterStatusManager
    ↓ 调用接口
BroadcasterInfoRepository
    ↓ 网络请求
USER_GETUSERLISTONLINESTATUSPOSTV3 API
    ↓ 返回数据
BroadcasterStatusManager (更新缓存)
    ↓ 发送 Flow 事件
HomeScreen/其他Screen (更新UI)
```

## 📋 主要方法

### 1. **状态查询方法**

```kotlin
// 获取单个主播状态
fun getStatus(userId: String): BroadcasterStatus?

// 批量获取主播状态
fun getStatusBatch(userIds: List<String>): Map<String, BroadcasterStatus>

// 检查主播是否在线
fun isOnline(userId: String): Boolean

// 获取所有缓存状态
fun getAllCachedStatuses(): Map<String, BroadcasterStatus>
```

### 2. **状态请求方法**

```kotlin
// 请求单个主播状态
fun requestStatus(userId: String)

// 批量请求主播状态（推荐）
fun requestStatusBatch(userIds: List<String>)
```

### 3. **状态管理方法**

```kotlin
// 手动更新状态
fun updateStatus(userId: String, status: String)

// 清空缓存
fun clearCache()

// 获取缓存统计
fun getCacheStats(): CacheStats
```

## 🔧 在 HomeViewModel 中的集成

### 1. **依赖注入**

```kotlin
@HiltViewModel
class HomeViewModel @Inject constructor(
    private val broadcasterInfoRepository: BroadcasterInfoRepository,
    private val strategyConfigManager: StrategyConfigManager,
    private val broadcasterStatusManager: BroadcasterStatusManager // 注入状态管理器
) : BaseViewModel<HomeUiState>()
```

### 2. **状态监听**

```kotlin
init {
    // 监听主播状态变化
    observeBroadcasterStatusUpdates()
}

private fun observeBroadcasterStatusUpdates() {
    viewModelScope.launch {
        broadcasterStatusManager.statusUpdates.collectLatest { statusUpdates ->
            if (statusUpdates.isNotEmpty()) {
                updateBroadcasterStatuses(statusUpdates)
            }
        }
    }
}
```

### 3. **状态更新**

```kotlin
private fun updateBroadcasterStatuses(statusUpdates: Map<String, BroadcasterStatusManager.BroadcasterStatus>) {
    val currentBroadcasters = currentState.broadcasters
    if (currentBroadcasters.isEmpty()) return
    
    val updatedBroadcasters = currentBroadcasters.map { broadcaster ->
        val statusUpdate = statusUpdates[broadcaster.id]
        if (statusUpdate != null) {
            // 更新主播状态
            broadcaster.copy(status = statusUpdate.status)
        } else {
            broadcaster
        }
    }
    
    // 更新UI状态
    updateState { 
        copy(broadcasters = updatedBroadcasters)
    }
}
```

### 4. **请求状态**

```kotlin
private fun requestBroadcasterStatuses() {
    val broadcasterIds = currentState.broadcasters.map { it.id }
    if (broadcasterIds.isNotEmpty()) {
        broadcasterStatusManager.requestStatusBatch(broadcasterIds)
    }
}

// 在加载主播数据后调用
fun loadBroadcasterWall(page: Int = 1, isLoadMore: Boolean = false) {
    // ... 加载主播数据
    
    // 加载成功后请求状态
    requestBroadcasterStatuses()
}
```

## 🎯 在其他 Screen 中的使用

### ProfileScreen 示例

```kotlin
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val broadcasterStatusManager: BroadcasterStatusManager
) : ViewModel() {
    
    init {
        // 监听状态变化
        viewModelScope.launch {
            broadcasterStatusManager.statusUpdates.collectLatest { statusUpdates ->
                // 更新个人资料页面的状态显示
                updateProfileStatus(statusUpdates)
            }
        }
    }
    
    fun loadUserProfile(userId: String) {
        // 加载用户资料
        loadProfile(userId)
        
        // 请求用户状态
        broadcasterStatusManager.requestStatus(userId)
    }
}
```

### MessageScreen 示例

```kotlin
@HiltViewModel
class MessageViewModel @Inject constructor(
    private val broadcasterStatusManager: BroadcasterStatusManager
) : ViewModel() {
    
    fun loadConversations() {
        // 加载对话列表
        loadConversationList()
        
        // 请求所有联系人的状态
        val contactIds = conversations.map { it.userId }
        broadcasterStatusManager.requestStatusBatch(contactIds)
    }
    
    init {
        // 监听状态变化，更新对话列表中的在线状态
        viewModelScope.launch {
            broadcasterStatusManager.statusUpdates.collectLatest { statusUpdates ->
                updateConversationStatuses(statusUpdates)
            }
        }
    }
}
```

## 📊 性能特性

### 1. **缓存机制**
- 使用 `ConcurrentHashMap` 保证线程安全
- 最大缓存 1000 个状态
- 30分钟自动过期清理

### 2. **批量处理**
- 单次最多处理 50 个用户ID
- 避免重复请求（请求中的ID会被标记）
- 自动分批处理大量请求

### 3. **错误处理**
- 网络错误时保持缓存状态
- 详细的日志记录
- 优雅的异常处理

## 🔍 调试和监控

### 日志标签
搜索 `BroadcasterStatusManager` 可以看到：

```
D/BroadcasterStatusManager: Requesting status for 10 broadcasters: [id1, id2, ...]
D/BroadcasterStatusManager: Successfully updated status for 10 broadcasters
D/BroadcasterStatusManager: Updated status for broadcaster user123: online
```

### 缓存统计
```kotlin
val stats = broadcasterStatusManager.getCacheStats()
Log.d("Cache", "Total: ${stats.totalCount}, Requesting: ${stats.requestingCount}")
```

## ✅ 使用最佳实践

### 1. **批量请求优于单个请求**
```kotlin
// ✅ 推荐
broadcasterStatusManager.requestStatusBatch(listOf("user1", "user2", "user3"))

// ❌ 不推荐
broadcasterStatusManager.requestStatus("user1")
broadcasterStatusManager.requestStatus("user2")
broadcasterStatusManager.requestStatus("user3")
```

### 2. **在合适的时机请求状态**
```kotlin
// ✅ 在数据加载完成后请求
fun loadBroadcasters() {
    // 先加载主播数据
    loadBroadcasterData()
    
    // 数据加载成功后请求状态
    requestBroadcasterStatuses()
}
```

### 3. **避免频繁请求**
```kotlin
// ✅ 检查缓存，避免重复请求
fun requestStatusIfNeeded(userId: String) {
    val cachedStatus = broadcasterStatusManager.getStatus(userId)
    val isExpired = cachedStatus?.let { 
        System.currentTimeMillis() - it.lastUpdateTime > 5 * 60 * 1000L // 5分钟过期
    } ?: true
    
    if (isExpired) {
        broadcasterStatusManager.requestStatus(userId)
    }
}
```

## 🚀 总结

BroadcasterStatusManager 提供了完整的主播状态管理解决方案：

### 核心功能：
1. ✅ **批量状态查询** - 高效的API调用
2. ✅ **智能缓存管理** - 减少网络请求
3. ✅ **Flow 状态监听** - 响应式UI更新
4. ✅ **线程安全设计** - 支持并发访问

### 使用场景：
- 🏠 **主播墙** - 显示主播在线状态
- 💬 **消息页面** - 显示联系人状态
- 👤 **个人资料** - 显示用户状态
- 📞 **通话功能** - 检查可通话状态

现在你可以在任何需要主播状态的地方使用这个管理器了！
