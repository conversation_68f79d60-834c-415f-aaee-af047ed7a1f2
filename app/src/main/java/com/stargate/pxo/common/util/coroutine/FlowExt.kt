package com.stargate.pxo.common.util.coroutine

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.FlowCollector
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * Flow扩展函数
 * 提供更简洁的Flow处理方式
 */

/**
 * 安全收集Flow
 * 一个简单的方法来处理Flow的收集、开始、完成和错误处理
 */
fun <T> Flow<T>.safeCollect(
    scope: CoroutineScope,
    dispatcher: CoroutineDispatcher = Dispatchers.Main,
    onStart: (() -> Unit)? = null,
    onComplete: (() -> Unit)? = null,
    onError: ((Throwable) -> Unit)? = null,
    onSuccess: (T) -> Unit
): Job {
    return this
        .flowOn(Dispatchers.IO)
        .onStart { onStart?.invoke() }
        .onEach { onSuccess(it) }
        .onCompletion { onComplete?.invoke() }
        .catch { error -> onError?.invoke(error) ?: throw error }
        .launchIn(scope)
}

/**
 * 收集Flow，绑定到生命周期
 * 当LifecycleOwner进入指定状态时收集Flow，离开时自动取消
 */
fun <T> Flow<T>.collectIn(
    lifecycleOwner: LifecycleOwner,
    state: Lifecycle.State = Lifecycle.State.STARTED,
    onStart: (() -> Unit)? = null,
    onComplete: (() -> Unit)? = null,
    onError: ((Throwable) -> Unit)? = null,
    collector: (T) -> Unit
): Job {
    return lifecycleOwner.lifecycleScope.launch {
        lifecycleOwner.repeatOnLifecycle(state) {
            this@collectIn
                .onStart { onStart?.invoke() }
                .onEach { collector(it) }
                .onCompletion { onComplete?.invoke() }
                .catch { error -> onError?.invoke(error) ?: throw error }
                .collect(FlowCollector { collector(it) })
        }
    }
}

/**
 * UI状态包装类
 * 用于表示UI状态的密封类
 */
sealed class UiState<out T> {
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val error: Throwable) : UiState<Nothing>()
}

/**
 * 加载状态包装类
 * 用于表示数据加载状态的密封类
 */
sealed class LoadState<out T> {
    object Idle : LoadState<Nothing>()
    object Loading : LoadState<Nothing>()
    data class Success<T>(val data: T) : LoadState<T>()
    data class Error(val error: Throwable) : LoadState<Nothing>()
}

/**
 * 将Flow转换为UI状态Flow
 * 自动添加Loading、Success和Error状态
 */
fun <T> Flow<T>.asUiState(): Flow<UiState<T>> = flow {
    emit(UiState.Loading)
    try {
        collect { emit(UiState.Success(it)) }
    } catch (e: Throwable) {
        emit(UiState.Error(e))
    }
}

/**
 * 将Flow转换为加载状态Flow
 * 提供更详细的加载状态表示
 */
fun <T> Flow<T>.asLoadState(): Flow<LoadState<T>> = flow {
    emit(LoadState.Idle)
    emit(LoadState.Loading)
    try {
        collect { emit(LoadState.Success(it)) }
    } catch (e: Throwable) {
        emit(LoadState.Error(e))
    }
}

/**
 * 尝试执行并返回Flow
 * 用于将可能抛出异常的操作转换为Flow
 */
fun <T> tryAsFlow(block: suspend () -> T): Flow<T> = flow {
    emit(block())
}

/**
 * 带重试的Flow
 * 自动重试指定次数
 */
fun <T> Flow<T>.retry(
    times: Int,
    delayMillis: Long = 1000,
    predicate: (Throwable) -> Boolean = { true }
): Flow<T> = flow {
    var attempt = 0
    var lastException: Throwable? = null
    
    while (attempt <= times) {
        try {
            collect { emit(it) }
            return@flow
        } catch (e: Throwable) {
            lastException = e
            if (!predicate(e) || attempt == times) break
            attempt++
            kotlinx.coroutines.delay(delayMillis * attempt)
        }
    }
    
    throw lastException ?: IllegalStateException("Unknown error in retry flow")
}