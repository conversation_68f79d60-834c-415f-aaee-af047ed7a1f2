package com.stargate.pxo.common.util.log

import android.util.Log
import com.google.gson.GsonBuilder
import com.google.gson.JsonParser
import com.stargate.pxo.BuildConfig

/**
 * 日志工具类
 * 提供统一的日志记录方法
 */
object LogUtil {
    // 是否启用日志
    private var isEnabled = BuildConfig.DEBUG
    
    // 日志级别
    private const val VERBOSE = 1
    private const val DEBUG = 2
    private const val INFO = 3
    private const val WARN = 4
    private const val ERROR = 5
    
    // 当前日志级别
    private var level = if (BuildConfig.DEBUG) VERBOSE else INFO
    
    // 默认TAG
    private const val DEFAULT_TAG = "PuxxiLog"
    
    // 最大日志长度
    private const val MAX_LOG_LENGTH = 4000
    
    /**
     * 设置是否启用日志
     */
    fun setEnable(enable: Boolean) {
        isEnabled = enable
    }
    
    /**
     * 设置日志级别
     */
    fun setLevel(logLevel: Int) {
        level = logLevel
    }
    
    /**
     * 打印Verbose日志
     */
    fun v(msg: String) {
        v(DEFAULT_TAG, msg)
    }
    
    /**
     * 打印Verbose日志
     */
    fun v(tag: String, msg: String) {
        if (isEnabled && level <= VERBOSE) {
            printLog(tag, msg, Log.VERBOSE)
        }
    }
    
    /**
     * 打印Debug日志
     */
    fun d(msg: String) {
        d(DEFAULT_TAG, msg)
    }
    
    /**
     * 打印Debug日志
     */
    fun d(tag: String, msg: String) {
        if (isEnabled && level <= DEBUG) {
            printLog(tag, msg, Log.DEBUG)
        }
    }
    
    /**
     * 打印Info日志
     */
    fun i(msg: String) {
        i(DEFAULT_TAG, msg)
    }
    
    /**
     * 打印Info日志
     */
    fun i(tag: String, msg: String) {
        if (isEnabled && level <= INFO) {
            printLog(tag, msg, Log.INFO)
        }
    }
    
    /**
     * 打印Warn日志
     */
    fun w(msg: String) {
        w(DEFAULT_TAG, msg)
    }
    
    /**
     * 打印Warn日志
     */
    fun w(tag: String, msg: String) {
        if (isEnabled && level <= WARN) {
            printLog(tag, msg, Log.WARN)
        }
    }
    
    /**
     * 打印Error日志
     */
    fun e(msg: String) {
        e(DEFAULT_TAG, msg)
    }
    
    /**
     * 打印Error日志
     */
    fun e(tag: String, msg: String) {
        if (isEnabled && level <= ERROR) {
            printLog(tag, msg, Log.ERROR)
        }
    }
    
    /**
     * 打印Error日志
     */
    fun e(tag: String, msg: String, tr: Throwable) {
        if (isEnabled && level <= ERROR) {
            Log.e(tag, msg, tr)
        }
    }
    
    /**
     * 格式化打印JSON数据
     */
    fun json(json: String) {
        json(DEFAULT_TAG, json)
    }
    
    /**
     * 格式化打印JSON数据
     */
    fun json(tag: String, json: String) {
        if (!isEnabled || level > DEBUG) {
            return
        }
        
        var formattedJson = json
        
        try {
            if (json.trim().startsWith("{") || json.trim().startsWith("[")) {
                val jsonElement = JsonParser.parseString(json)
                formattedJson = GsonBuilder().setPrettyPrinting().create().toJson(jsonElement)
            }
            d(tag, "╔═════════════════════════ JSON ═════════════════════════")
            // 按行分割
            val lines = formattedJson.split("\n")
            for (line in lines) {
                d(tag, "║ $line")
            }
            d(tag, "╚══════════════════════════════════════════════════════════")
        } catch (e: Exception) {
            e(tag, "Invalid JSON: $json")
        }
    }
    
    /**
     * 分段打印日志，避免超过Android日志长度限制
     */
    private fun printLog(tag: String, msg: String, priority: Int) {
        if (msg.length <= MAX_LOG_LENGTH) {
            printByPriority(tag, msg, priority)
            return
        }
        
        // 分段打印日志
        var i = 0
        while (i < msg.length) {
            val end = (i + MAX_LOG_LENGTH).coerceAtMost(msg.length)
            val part = msg.substring(i, end)
            printByPriority(tag, part, priority)
            i = end
        }
    }
    
    /**
     * 根据优先级打印日志
     */
    private fun printByPriority(tag: String, msg: String, priority: Int) {
        when (priority) {
            Log.VERBOSE -> Log.v(tag, msg)
            Log.DEBUG -> Log.d(tag, msg)
            Log.INFO -> Log.i(tag, msg)
            Log.WARN -> Log.w(tag, msg)
            Log.ERROR -> Log.e(tag, msg)
            else -> Log.d(tag, msg)
        }
    }
}