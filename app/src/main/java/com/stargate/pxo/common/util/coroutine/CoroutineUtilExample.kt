package com.stargate.pxo.common.util.coroutine

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stargate.pxo.common.util.ToastUtil
import com.stargate.pxo.common.util.log.LogUtil
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.launch

/**
 * 协程工具类使用示例
 * 演示项目中各种协程使用场景
 */
object CoroutineUtilExample {
    
    private val coroutineManager = CoroutineManager.getInstance()
    private val scopeManager = CoroutineScopeManager.getInstance()
    private val exceptionMonitor = CoroutineExceptionMonitor.getInstance()
    private val performanceMonitor = CoroutinePerformanceMonitor.getInstance()
    
    fun demonstrateBasicUsage() {
        LogUtil.d("CoroutineExample", "开始演示基础协程用法")
        
        // =================== 基础协程启动 ===================
        
        // IO线程处理网络请求
        coroutineManager.launchIO(jobName = "network_request") {
            val result = simulateNetworkRequest()
            LogUtil.d("CoroutineExample", "网络请求结果: $result")
        }
        
        // 主线程更新UI
        coroutineManager.launchMain(jobName = "update_ui") {
            LogUtil.d("CoroutineExample", "在主线程更新UI")
            // 更新UI操作
        }
        
        // CPU密集型计算
        coroutineManager.launchCPU(jobName = "heavy_calculation") {
            val result = performHeavyCalculation()
            LogUtil.d("CoroutineExample", "计算结果: $result")
        }
        
        // 重型任务处理
        coroutineManager.launchHeavy(jobName = "file_processing") {
            processLargeFile()
            LogUtil.d("CoroutineExample", "文件处理完成")
        }
    }
    
    fun demonstrateScopeManagement() {
        LogUtil.d("CoroutineExample", "开始演示作用域管理")
        
        // =================== 作用域管理 ===================
        
        // 网络相关操作
        scopeManager.launchInScope(
            scopeName = CoroutineScopeManager.SCOPE_NETWORK,
            jobName = "api_call"
        ) {
            val data = fetchUserData()
            LogUtil.d("CoroutineExample", "获取用户数据: $data")
        }
        
        // 数据库操作
        scopeManager.launchInScope(
            scopeName = CoroutineScopeManager.SCOPE_DATABASE,
            jobName = "save_user"
        ) {
            saveUserToDatabase("user123")
            LogUtil.d("CoroutineExample", "用户数据已保存")
        }
        
        // 文件IO操作
        scopeManager.launchInScope(
            scopeName = CoroutineScopeManager.SCOPE_FILE_IO,
            jobName = "save_file"
        ) {
            saveDataToFile("important_data.txt")
            LogUtil.d("CoroutineExample", "文件保存完成")
        }
        
        // 检查作用域状态
        val activeScopes = scopeManager.getActiveScopes()
        LogUtil.d("CoroutineExample", "活跃作用域: $activeScopes")
    }
    
    fun demonstrateExceptionHandling() {
        LogUtil.d("CoroutineExample", "开始演示异常处理")
        
        // =================== 异常处理 ===================
        
        // 注册特定异常处理器
        exceptionMonitor.registerExceptionHandler("NetworkException") { throwable ->
            LogUtil.w("CoroutineExample", "处理网络异常: ${throwable.message}")
            ToastUtil.Companion.showError("网络连接失败，请检查网络设置")
        }
        
        // 使用监控的异常处理器
        val exceptionHandler = exceptionMonitor.createMonitoredExceptionHandler(
            context = "UserDataFetch",
            customHandler = { throwable ->
                LogUtil.e("CoroutineExample", "用户数据获取失败", throwable)
            }
        )
        
        coroutineManager.launchIO(
            jobName = "risky_operation",
            exceptionHandler = { throwable ->
                LogUtil.e("CoroutineExample", "风险操作失败", throwable)
            }
        ) {
            // 模拟可能抛出异常的操作
            if (Math.random() > 0.5) {
                throw RuntimeException("模拟异常")
            }
            LogUtil.d("CoroutineExample", "风险操作成功完成")
        }
    }
    
    fun demonstratePerformanceMonitoring() {
        LogUtil.d("CoroutineExample", "开始演示性能监控")
        
        // =================== 性能监控 ===================
        
        coroutineManager.launchIO {
            // 监控数据库查询性能
            performanceMonitor.measureCoroutine("database_query", this) {
                simulateDatabaseQuery()
            }
            
            // 监控图片处理性能
            performanceMonitor.measureCoroutine("image_processing", this) {
                simulateImageProcessing()
            }
            
            // 获取性能统计
            val stats = performanceMonitor.getPerformanceStats()
            LogUtil.d("CoroutineExample", "性能统计: $stats")
            
            // 检查慢操作
            val slowOps = performanceMonitor.getSlowOperations()
            if (slowOps.isNotEmpty()) {
                LogUtil.w("CoroutineExample", "检测到慢操作: $slowOps")
            }
        }
    }
    
    fun demonstrateAdvancedFeatures() {
        LogUtil.d("CoroutineExample", "开始演示高级功能")
        
        // =================== 高级功能 ===================
        
        // 重试机制
        coroutineManager.launchIO {
            try {
                val result = coroutineManager.retry(times = 3, delayMs = 1000) {
                    unstableNetworkCall()
                }
                LogUtil.d("CoroutineExample", "重试成功: $result")
            } catch (e: Exception) {
                LogUtil.e("CoroutineExample", "重试全部失败", e)
            }
        }
        
        // 超时控制
        coroutineManager.launchIO {
            try {
                val result = coroutineManager.withTimeout(5000) {
                    longRunningOperation()
                }
                LogUtil.d("CoroutineExample", "操作完成: $result")
            } catch (e: Exception) {
                LogUtil.e("CoroutineExample", "操作超时", e)
            }
        }
        
        // 线程切换
        coroutineManager.launchIO {
            LogUtil.d("CoroutineExample", "当前线程: ${Thread.currentThread().name}")
            
            val result = coroutineManager.withCPU {
                LogUtil.d("CoroutineExample", "CPU线程: ${Thread.currentThread().name}")
                performCPUIntensiveTask()
            }
            
            coroutineManager.withMain {
                LogUtil.d("CoroutineExample", "主线程: ${Thread.currentThread().name}")
                // 更新UI
            }
        }
    }
    
    fun demonstrateFlowUsage() {
        LogUtil.d("CoroutineExample", "开始演示Flow用法")
        
        // =================== Flow使用 ===================
        
        coroutineManager.launchMain {
            // 创建数据流
            val dataFlow = createDataFlow()
            
            // 使用扩展函数处理Flow
            dataFlow
                .onIO() // 在IO线程执行
                .withLoadingState(
                    onStart = { LogUtil.d("CoroutineExample", "开始加载数据") },
                    onComplete = { LogUtil.d("CoroutineExample", "数据加载完成") },
                    onError = { LogUtil.e("CoroutineExample", "数据加载失败", it) }
                )
                .safeCollect(
                    onSuccess = { data ->
                        LogUtil.d("CoroutineExample", "收到数据: $data")
                    },
                    onError = { throwable ->
                        ToastUtil.Companion.showError("数据加载失败")
                    }
                )
        }
    }
    
    fun demonstrateLifecycleAware(lifecycleOwner: LifecycleOwner) {
        LogUtil.d("CoroutineExample", "开始演示生命周期感知")
        
        // =================== 生命周期感知 ===================
        
        // 在特定生命周期状态下执行
        lifecycleOwner.lifecycleScope.launch {
            lifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                // 只在STARTED状态下执行
                collectUserData()
            }
        }
        
        // 使用扩展函数
        lifecycleOwner.lifecycleScope.launchIO {
            val data = fetchDataFromServer()
            
            // 切换到主线程更新UI
            runOnMain {
                updateUI(data)
            }
        }
    }
    
    fun demonstrateCleanup() {
        LogUtil.d("CoroutineExample", "开始演示资源清理")
        
        // =================== 资源清理 ===================
        
        // 取消特定作用域
        scopeManager.cancelScope(CoroutineScopeManager.SCOPE_NETWORK)
        
        // 取消特定Job
        coroutineManager.cancelJob("long_running_task")
        
        // 清理未使用的作用域
        scopeManager.cleanupUnusedScopes()
        
        // 获取统计信息
        val coroutineStats = coroutineManager.getCoroutineStats()
        val scopeStats = scopeManager.getScopeStats()
        val exceptionStats = exceptionMonitor.getExceptionStats()
        
        LogUtil.d("CoroutineExample", "协程统计: $coroutineStats")
        LogUtil.d("CoroutineExample", "作用域统计: $scopeStats")
        LogUtil.d("CoroutineExample", "异常统计: $exceptionStats")
    }
    
    // =================== 模拟方法 ===================
    
    private suspend fun simulateNetworkRequest(): String {
        delay(1000)
        return "网络数据"
    }
    
    private suspend fun performHeavyCalculation(): Int {
        delay(2000)
        return 42
    }
    
    private suspend fun processLargeFile() {
        delay(3000)
    }
    
    private suspend fun fetchUserData(): String {
        delay(800)
        return "用户数据"
    }
    
    private suspend fun saveUserToDatabase(userId: String) {
        delay(500)
    }
    
    private suspend fun saveDataToFile(filename: String) {
        delay(1200)
    }
    
    private suspend fun simulateDatabaseQuery() {
        delay(300)
    }
    
    private suspend fun simulateImageProcessing() {
        delay(2000)
    }
    
    private suspend fun unstableNetworkCall(): String {
        delay(500)
        if (Math.random() > 0.7) {
            throw RuntimeException("网络不稳定")
        }
        return "成功"
    }
    
    private suspend fun longRunningOperation(): String {
        delay(10000) // 可能超时
        return "完成"
    }
    
    private suspend fun performCPUIntensiveTask(): Int {
        // 模拟CPU密集型任务
        var result = 0
        repeat(1000000) {
            result += it
        }
        return result
    }
    
    private fun createDataFlow(): Flow<String> = flow {
        repeat(5) { index ->
            delay(1000)
            emit("数据 $index")
        }
    }.catch { e ->
        LogUtil.e("CoroutineExample", "Flow异常", e)
        emit("错误数据")
    }
    
    private suspend fun collectUserData() {
        while (true) {
            delay(5000)
            LogUtil.d("CoroutineExample", "收集用户数据...")
        }
    }
    
    private suspend fun fetchDataFromServer(): String {
        delay(1500)
        return "服务器数据"
    }
    
    private suspend fun updateUI(data: String) {
        LogUtil.d("CoroutineExample", "更新UI: $data")
    }
}

/**
 * 项目特定的协程工具方法
 */
object ProjectCoroutineUtils {
    
    /**
     * 网络请求协程
     */
    fun launchNetworkRequest(
        requestName: String,
        onSuccess: (String) -> Unit = {},
        onError: (Throwable) -> Unit = {},
        request: suspend () -> String
    ): Job {
        return CoroutineManager.getInstance().launchIO(
            scopeName = CoroutineScopeManager.SCOPE_NETWORK,
            jobName = requestName,
            exceptionHandler = onError
        ) {
            try {
                val result = request()
                CoroutineManager.getInstance().withMain {
                    onSuccess(result)
                }
            } catch (e: Exception) {
                CoroutineManager.getInstance().withMain {
                    onError(e)
                }
            }
        }
    }
    
    /**
     * 数据库操作协程
     */
    fun launchDatabaseOperation(
        operationName: String,
        operation: suspend () -> Unit
    ): Job {
        return CoroutineManager.getInstance().launchIO(
            scopeName = CoroutineScopeManager.SCOPE_DATABASE,
            jobName = operationName
        ) {
            operation()
        }
    }
    
    /**
     * 文件操作协程
     */
    fun launchFileOperation(
        operationName: String,
        operation: suspend () -> Unit
    ): Job {
        return CoroutineManager.getInstance().launchIO(
            scopeName = CoroutineScopeManager.SCOPE_FILE_IO,
            jobName = operationName
        ) {
            operation()
        }
    }
    
    /**
     * 图片处理协程
     */
    fun launchImageProcessing(
        operationName: String,
        operation: suspend () -> Unit
    ): Job {
        return CoroutineManager.getInstance().launchHeavy(
            scopeName = CoroutineScopeManager.SCOPE_IMAGE_PROCESSING,
            jobName = operationName
        ) {
            operation()
        }
    }
}