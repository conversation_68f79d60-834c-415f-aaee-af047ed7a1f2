package com.stargate.pxo.common.util

/**
 * SharedPreferences Key常量类
 * 统一管理所有SP的key，避免字符串硬编码
 */
object SPKey {


    // =================== 用户相关 ===================
    
    /** 用户ID */
    const val USER_ID = "user_id"



    // =================== 登录相关 ===================
    
    /** 是否已登录 */
    const val IS_LOGIN = "is_login"

    const val IS_FIRST_REGISTER = "is_First_Register"

    /** 登录令牌 */
    const val ACCESS_TOKEN = "access_token"
    
    /** 刷新令牌 */
    const val REFRESH_TOKEN = "refresh_token"
    
    /** 令牌过期时间 */
    const val TOKEN_EXPIRE_TIME = "token_expire_time"
    
    /** 是否记住密码 */
    const val REMEMBER_PASSWORD = "remember_password"
    
    /** 自动登录 */
    const val AUTO_LOGIN = "auto_login"
    
    /** 最后登录时间 */
    const val LAST_LOGIN_TIME = "last_login_time"
    
    // =================== 应用设置 ===================
    
    /** 是否首次启动 */
    const val IS_FIRST_LAUNCH = "is_first_launch"
    
    /** 应用版本号 */
    const val APP_VERSION = "app_version"
    
    /** 应用版本名称 */
    const val APP_VERSION_NAME = "app_version_name"
    
    /** 语言设置 */
    const val LANGUAGE = "language"
    
    /** 主题设置 */
    const val THEME = "theme"
    
    /** 是否开启夜间模式 */
    const val DARK_MODE = "dark_mode"
    
    /** 字体大小 */
    const val FONT_SIZE = "font_size"
    
    // =================== 通知设置 ===================
    
    /** 是否开启推送通知 */
    const val PUSH_NOTIFICATION = "push_notification"
    
    /** 是否开启声音提醒 */
    const val SOUND_NOTIFICATION = "sound_notification"
    
    /** 是否开启震动提醒 */
    const val VIBRATE_NOTIFICATION = "vibrate_notification"
    
    /** 免打扰时间开始 */
    const val DO_NOT_DISTURB_START = "do_not_disturb_start"
    
    /** 免打扰时间结束 */
    const val DO_NOT_DISTURB_END = "do_not_disturb_end"
    
    // =================== 隐私设置 ===================
    
    /** 是否允许收集崩溃信息 */
    const val ALLOW_CRASH_REPORT = "allow_crash_report"
    
    /** 是否允许收集使用统计 */
    const val ALLOW_ANALYTICS = "allow_analytics"
    
    /** 是否允许个性化推荐 */
    const val ALLOW_PERSONALIZATION = "allow_personalization"
    
    /** 是否同意隐私政策 */
    const val PRIVACY_POLICY_AGREED = "privacy_policy_agreed"
    
    /** 隐私政策版本 */
    const val PRIVACY_POLICY_VERSION = "privacy_policy_version"
    
    // =================== 缓存相关 ===================
    
    /** 图片缓存大小 */
    const val IMAGE_CACHE_SIZE = "image_cache_size"
    
    /** 文件缓存大小 */
    const val FILE_CACHE_SIZE = "file_cache_size"
    
    /** 自动清理缓存 */
    const val AUTO_CLEAR_CACHE = "auto_clear_cache"
    
    /** 上次清理缓存时间 */
    const val LAST_CACHE_CLEAR_TIME = "last_cache_clear_time"
    
    // =================== 网络相关 ===================
    
    /** 仅WiFi下载 */
    const val WIFI_ONLY_DOWNLOAD = "wifi_only_download"
    
    /** 移动网络提醒 */
    const val MOBILE_DATA_WARNING = "mobile_data_warning"
    
    /** 网络超时时间 */
    const val NETWORK_TIMEOUT = "network_timeout"
    
    /** 最大重试次数 */
    const val MAX_RETRY_COUNT = "max_retry_count"
    
    // =================== 业务相关 ===================
    
    /** 搜索历史 */
    const val SEARCH_HISTORY = "search_history"
    
    /** 浏览历史 */
    const val BROWSE_HISTORY = "browse_history"
    
    /** 收藏列表 */
    const val FAVORITE_LIST = "favorite_list"
    
    /** 最近使用 */
    const val RECENT_USED = "recent_used"
    
    /** 热门标签 */
    const val HOT_TAGS = "hot_tags"
    
    // =================== 调试相关 ===================
    
    /** 是否开启调试模式 */
    const val DEBUG_MODE = "debug_mode"
    
    /** 日志级别 */
    const val LOG_LEVEL = "log_level"
    
    /** 是否显示FPS */
    const val SHOW_FPS = "show_fps"
    
    /** 是否显示内存使用 */
    const val SHOW_MEMORY = "show_memory"
    
    // =================== 安全相关 ===================
    
    /** 是否开启手势密码 */
    const val GESTURE_PASSWORD_ENABLED = "gesture_password_enabled"
    
    /** 手势密码 */
    const val GESTURE_PASSWORD = "gesture_password"
    
    /** 是否开启指纹解锁 */
    const val FINGERPRINT_ENABLED = "fingerprint_enabled"
    
    /** 是否开启面部解锁 */
    const val FACE_UNLOCK_ENABLED = "face_unlock_enabled"
    
    /** 自动锁屏时间 */
    const val AUTO_LOCK_TIME = "auto_lock_time"
    
    // =================== 统计相关 ===================
    
    /** 应用启动次数 */
    const val APP_LAUNCH_COUNT = "app_launch_count"
    
    /** 总使用时长 */
    const val TOTAL_USAGE_TIME = "total_usage_time"
    
    /** 今日使用时长 */
    const val TODAY_USAGE_TIME = "today_usage_time"
    
    /** 上次使用时间 */
    const val LAST_USAGE_TIME = "last_usage_time"
    
    // =================== 广告相关 ===================
    
    /** 是否显示广告 */
    const val SHOW_ADS = "show_ads"
    
    /** 广告配置 */
    const val AD_CONFIG = "ad_config"
    
    /** 上次广告展示时间 */
    const val LAST_AD_SHOW_TIME = "last_ad_show_time"
    
    /** 广告点击次数 */
    const val AD_CLICK_COUNT = "ad_click_count"
    
    // =================== 更新相关 ===================
    
    /** 检查更新 */
    const val CHECK_UPDATE = "check_update"
    
    /** 自动更新 */
    const val AUTO_UPDATE = "auto_update"
    
    /** 忽略的版本 */
    const val IGNORED_VERSION = "ignored_version"
    
    /** 上次检查更新时间 */
    const val LAST_UPDATE_CHECK_TIME = "last_update_check_time"
    
    // =================== 配置相关 ===================


    const val APP_SERVER_KEY = "app_server_key"

    /** 实验性功能开关 */
    const val APP_FB_CLIENT_TOKEN = "app_fb_client_token"
    
    /** Beta功能开关 */
    const val GLT = "glt"
    
    /** 开发者选项 */
    const val APP_FB_ID = "app_fb_id"

    /** 实验性功能开关 */
    const val RC_APP_KEY = "rc_app_key"

    /** Beta功能开关 */
    const val RC_AREA_CODE = "rc_area_code"

    /** 开发者选项 */
    const val ATTRIBUTION_SDK = "attribution_sdk"


    const val TRANSLATE_V2 = "translate_v2"


    const val GOOGLE_TRANSLATION_KEY = "google_translation_key"
    
    // =================== 策略配置相关 ===================
    
    /** 完整策略配置对象（JSON序列化） */
    const val STRATEGY_CONFIG = "strategy_config"
    
    /** 是否免费匹配通话 */
    const val IS_MATCH_CALL_FREE = "is_match_call_free"
    
    /** 初始化标签 */
    const val INIT_TAB = "init_tab"
    
    /** 是否显示匹配性别 */
    const val IS_SHOW_MATCH_GENDER = "is_show_match_gender"
    
    /** 是否为审核包 */
    const val IS_REVIEW_PKG = "is_review_pkg"
    
    /** 是否开启面具 */
    const val IS_MASK_OPEN = "is_mask_open"
    
    /** 是否自动接受 */
    const val IS_AUTO_ACCEPT = "is_auto_accept"
    
    /** 是否显示Meet */
    const val SHOW_MEET = "show_meet"
    
    /** 是否开启闪聊 */
    const val IS_OPEN_FLASH_CHAT = "is_open_flash_chat"
    
    /** 是否显示匹配 */
    const val IS_SHOW_MATCH = "is_show_match"
    
    /** 策略时间戳 */
    const val STRATEGY_TIMESTAMP = "strategy_timestamp"
}