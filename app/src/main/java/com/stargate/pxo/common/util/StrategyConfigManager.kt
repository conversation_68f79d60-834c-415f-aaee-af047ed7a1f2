package com.stargate.pxo.common.util

import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.model.StrategyResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 策略配置管理器
 * 用于获取和缓存策略配置
 */
@Singleton
class StrategyConfigManager @Inject constructor(
    private val gson: Gson
) {
    /**
     * 获取完整的策略配置对象
     * 从 SharedPreferences 中读取序列化的 JSON 并反序列化为 StrategyConfig 对象
     *
     * @return 策略配置对象，如果不存在或解析失败则返回 null
     */
    suspend fun getStrategyConfig(): StrategyResponse? = withContext(Dispatchers.IO) {
        try {
            val configJson = SPUtil.getString(SPKey.STRATEGY_CONFIG, null)
            if (configJson.isNullOrEmpty()) {
                LogUtil.d("StrategyConfigManager", "策略配置不存在")
                return@withContext null
            }

            return@withContext gson.fromJson(configJson, StrategyResponse::class.java)
        } catch (e: JsonSyntaxException) {
            LogUtil.e("StrategyConfigManager", "策略配置解析失败: ${e.message}")
            return@withContext null
        } catch (e: Exception) {
            LogUtil.e("StrategyConfigManager", "获取策略配置异常: ${e.message}")
            return@withContext null
        }
    }

    /**
     * 获取特定的策略配置项
     *
     * @param key SharedPreferences 中的键
     * @param defaultValue 默认值
     * @return 配置项值
     */
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return SPUtil.getBoolean(key, defaultValue)
    }

    fun getInt(key: String, defaultValue: Int = 0): Int {
        return SPUtil.getInt(key, defaultValue)
    }

    fun getString(key: String, defaultValue: String = ""): String {
        return SPUtil.getString(key, defaultValue) ?: defaultValue
    }

    fun getLong(key: String, defaultValue: Long = 0L): Long {
        return SPUtil.getLong(key, defaultValue)
    }

    /**
     * 获取特定的策略配置项（通过属性名）
     * 直接从策略配置对象中获取属性值
     *
     * @param propertyName 属性名
     * @param defaultValue 默认值
     * @return 配置项值
     */
    @Suppress("UNCHECKED_CAST")
    suspend fun <T> getConfigProperty(propertyName: String, defaultValue: T): T {
        val config = getStrategyConfig() ?: return defaultValue

        return try {
            // 使用反射获取属性值
            val property = StrategyResponse::class.java.getDeclaredField(propertyName)
            property.isAccessible = true
            val value = property.get(config) as? T
            value ?: defaultValue
        } catch (e: Exception) {
            LogUtil.e("StrategyConfigManager", "获取策略配置属性失败: $propertyName, ${e.message}")
            defaultValue
        }
    }
    
    /**
     * 常用配置项的便捷访问方法
     */
    fun isMatchCallFree(): Boolean = getBoolean(SPKey.IS_MATCH_CALL_FREE, false)
    
    fun isShowMatchGender(): Boolean = getBoolean(SPKey.IS_SHOW_MATCH_GENDER, false)
    
    fun isReviewPkg(): Boolean = getBoolean(SPKey.IS_REVIEW_PKG, false)
    
    fun isMaskOpen(): Boolean = getBoolean(SPKey.IS_MASK_OPEN, false)
    
    fun isAutoAccept(): Boolean = getBoolean(SPKey.IS_AUTO_ACCEPT, false)
    
    fun isShowMeet(): Boolean = getBoolean(SPKey.SHOW_MEET, false)
    
    fun isOpenFlashChat(): Boolean = getBoolean(SPKey.IS_OPEN_FLASH_CHAT, false)
    
    fun isShowMatch(): Boolean = getBoolean(SPKey.IS_SHOW_MATCH, false)
    
    fun getInitTab(): Int = getInt(SPKey.INIT_TAB, 0)
    
    fun getStrategyTimestamp(): String = getString(SPKey.STRATEGY_TIMESTAMP, "")
} 