package com.stargate.pxo.common.util

import android.util.Base64
import java.nio.charset.Charset
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

object AESUtil {
    private const val AES = "AES"
    private const val TRANSFORMATION = "AES/ECB/PKCS5Padding"
    private val CHARSET: Charset = Charsets.UTF_8

    /**
     * AES加密，返回Base64字符串
     */
    fun encrypt2Base64(data: String, key: String): String? {
        val encrypted = encrypt(data.toByteArray(CHARSET), key.toByteArray(CHARSET)) ?: return null
        return Base64.encodeToString(encrypted, Base64.NO_WRAP)
    }

    /**
     * AES解密Base64字符串
     */
    fun decryptBase64(data: String, key: String): String? {
        val decoded = try {
            Base64.decode(data, Base64.NO_WRAP)
        } catch (e: Exception) {
            return null
        }
        val decrypted = decrypt(decoded, key.toByteArray(CHARSET)) ?: return null
        return try {
            String(decrypted, CHARSET)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * AES加密，返回byte[]
     */
    fun encrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val keySpec = SecretKeySpec(key, AES)
            cipher.init(Cipher.ENCRYPT_MODE, keySpec)
            cipher.doFinal(data)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * AES解密，返回byte[]
     */
    fun decrypt(data: ByteArray, key: ByteArray): ByteArray? {
        if (!isValidKey(key)) return null
        return try {
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val keySpec = SecretKeySpec(key, AES)
            cipher.init(Cipher.DECRYPT_MODE, keySpec)
            cipher.doFinal(data)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 校验密钥长度（16/24/32字节）
     */
    private fun isValidKey(key: ByteArray): Boolean {
        return key.size == 16 || key.size == 24 || key.size == 32
    }

    /** Base64编码（byte[] -> String） */
    fun encodeBase64(data: ByteArray): String = Base64.encodeToString(data, Base64.NO_WRAP)

    /** Base64解码（String -> byte[]） */
    fun decodeBase64(data: String): ByteArray? = try {
        Base64.decode(data, Base64.NO_WRAP)
    } catch (e: Exception) {
        null
    }

    /** Base64编码（String -> String） */
    fun encodeBase64String(data: String): String = encodeBase64(data.toByteArray(CHARSET))

    /** Base64解码（String -> String） */
    fun decodeBase64ToString(data: String): String? = decodeBase64(data)?.let { String(it, CHARSET) }
} 