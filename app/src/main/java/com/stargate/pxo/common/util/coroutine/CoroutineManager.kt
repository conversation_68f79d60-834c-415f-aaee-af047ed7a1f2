package com.stargate.pxo.common.util.coroutine

import android.os.Handler
import android.os.Looper
import com.stargate.pxo.common.util.log.LogUtil
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import kotlin.coroutines.CoroutineContext

/**
 * 协程管理工具类
 * 统一管理项目中的协程和线程操作
 */
class CoroutineManager private constructor() {
    
    companion object {
        @Volatile
        private var INSTANCE: CoroutineManager? = null
        
        fun getInstance(): CoroutineManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CoroutineManager().also { INSTANCE = it }
            }
        }
    }
    
    // 全局异常处理器
    private val globalExceptionHandler = CoroutineExceptionHandler { context, throwable ->
        LogUtil.e("CoroutineManager", "Uncaught coroutine exception in context: $context", throwable)
        handleCoroutineException(throwable, context)
    }
    
    // 主管理作用域
    private val managerScope = CoroutineScope(SupervisorJob() + globalExceptionHandler)
    
    // 自定义线程池
    private val cpuThreadPool = Executors.newFixedThreadPool(
        Runtime.getRuntime().availableProcessors(),
        createThreadFactory("Puxxi-CPU")
    )
    
    private val ioThreadPool = Executors.newCachedThreadPool(
        createThreadFactory("Puxxi-IO")
    )
    
    private val heavyThreadPool = Executors.newFixedThreadPool(
        maxOf(2, Runtime.getRuntime().availableProcessors() / 2),
        createThreadFactory("Puxxi-Heavy")
    )
    
    // 自定义调度器
    val cpuDispatcher: CoroutineDispatcher = cpuThreadPool.asCoroutineDispatcher()
    val ioDispatcher: CoroutineDispatcher = ioThreadPool.asCoroutineDispatcher()
    val heavyDispatcher: CoroutineDispatcher = heavyThreadPool.asCoroutineDispatcher()
    
    // 作用域管理
    private val scopes = ConcurrentHashMap<String, CoroutineScope>()
    private val jobs = ConcurrentHashMap<String, Job>()
    
    // 统计信息
    private val activeCoroutinesCount = AtomicInteger(0)
    private val totalCoroutinesLaunched = AtomicLong(0)
    private val failedCoroutinesCount = AtomicLong(0)
    
    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())
    
    init {
        LogUtil.d("CoroutineManager", "CoroutineManager initialized")
        startMonitoring()
    }
    
    // =================== 作用域管理 ===================
    
    /**
     * 创建或获取命名作用域
     */
    fun getScope(name: String, context: CoroutineContext = Dispatchers.Default): CoroutineScope {
        return scopes.getOrPut(name) {
            CoroutineScope(SupervisorJob() + context + globalExceptionHandler).also {
                LogUtil.d("CoroutineManager", "Created scope: $name")
            }
        }
    }
    
    /**
     * 取消指定作用域
     */
    fun cancelScope(name: String) {
        scopes[name]?.let { scope ->
            scope.cancel("Scope $name cancelled")
            scopes.remove(name)
            LogUtil.d("CoroutineManager", "Cancelled scope: $name")
        }
    }
    
    /**
     * 取消所有自定义作用域
     */
    fun cancelAllScopes() {
        scopes.forEach { (name, scope) ->
            scope.cancel("All scopes cancelled")
            LogUtil.d("CoroutineManager", "Cancelled scope: $name")
        }
        scopes.clear()
    }
    
    // =================== 协程启动方法 ===================
    
    /**
     * 在IO线程中执行
     */
    fun launchIO(
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchCoroutine(
            dispatcher = Dispatchers.IO,
            scopeName = scopeName,
            jobName = jobName,
            exceptionHandler = exceptionHandler,
            block = block
        )
    }
    
    /**
     * 在主线程中执行
     */
    fun launchMain(
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchCoroutine(
            dispatcher = Dispatchers.Main,
            scopeName = scopeName,
            jobName = jobName,
            exceptionHandler = exceptionHandler,
            block = block
        )
    }
    
    /**
     * 在CPU密集型线程中执行
     */
    fun launchCPU(
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchCoroutine(
            dispatcher = cpuDispatcher,
            scopeName = scopeName,
            jobName = jobName,
            exceptionHandler = exceptionHandler,
            block = block
        )
    }
    
    /**
     * 在重型任务线程中执行
     */
    fun launchHeavy(
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchCoroutine(
            dispatcher = heavyDispatcher,
            scopeName = scopeName,
            jobName = jobName,
            exceptionHandler = exceptionHandler,
            block = block
        )
    }
    
    /**
     * 在默认线程中执行
     */
    fun launchDefault(
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        return launchCoroutine(
            dispatcher = Dispatchers.Default,
            scopeName = scopeName,
            jobName = jobName,
            exceptionHandler = exceptionHandler,
            block = block
        )
    }
    
    /**
     * 核心协程启动方法
     */
    private fun launchCoroutine(
        dispatcher: CoroutineDispatcher,
        scopeName: String? = null,
        jobName: String? = null,
        exceptionHandler: ((Throwable) -> Unit)? = null,
        block: suspend CoroutineScope.() -> Unit
    ): Job {
        val scope = scopeName?.let { getScope(it, dispatcher) } ?: managerScope
        
        val customExceptionHandler = if (exceptionHandler != null) {
            CoroutineExceptionHandler { context, throwable ->
                try {
                    exceptionHandler(throwable)
                } catch (e: Exception) {
                    LogUtil.e("CoroutineManager", "Exception in custom exception handler", e)
                }
                globalExceptionHandler.handleException(context, throwable)
            }
        } else {
            globalExceptionHandler
        }
        
        val job = scope.launch(dispatcher + customExceptionHandler) {
            activeCoroutinesCount.incrementAndGet()
            totalCoroutinesLaunched.incrementAndGet()
            
            try {
                jobName?.let { name ->
                    LogUtil.d("CoroutineManager", "Starting coroutine: $name")
                }
                block()
                jobName?.let { name ->
                    LogUtil.d("CoroutineManager", "Completed coroutine: $name")
                }
            } catch (e: Exception) {
                failedCoroutinesCount.incrementAndGet()
                throw e
            } finally {
                activeCoroutinesCount.decrementAndGet()
            }
        }
        
        // 记录Job
        jobName?.let { name ->
            jobs[name] = job
            job.invokeOnCompletion { jobs.remove(name) }
        }
        
        return job
    }
    
    // =================== 切换线程方法 ===================
    
    /**
     * 切换到主线程
     */
    suspend fun <T> withMain(block: suspend CoroutineScope.() -> T): T {
        return withContext(Dispatchers.Main, block)
    }
    
    /**
     * 切换到IO线程
     */
    suspend fun <T> withIO(block: suspend CoroutineScope.() -> T): T {
        return withContext(Dispatchers.IO, block)
    }
    
    /**
     * 切换到CPU线程
     */
    suspend fun <T> withCPU(block: suspend CoroutineScope.() -> T): T {
        return withContext(cpuDispatcher, block)
    }
    
    /**
     * 切换到重型任务线程
     */
    suspend fun <T> withHeavy(block: suspend CoroutineScope.() -> T): T {
        return withContext(heavyDispatcher, block)
    }
    
    /**
     * 切换到默认线程
     */
    suspend fun <T> withDefault(block: suspend CoroutineScope.() -> T): T {
        return withContext(Dispatchers.Default, block)
    }
    
    // =================== Job管理 ===================
    
    /**
     * 取消指定Job
     */
    fun cancelJob(jobName: String) {
        jobs[jobName]?.let { job ->
            job.cancel("Job $jobName cancelled manually")
            LogUtil.d("CoroutineManager", "Cancelled job: $jobName")
        }
    }
    
    /**
     * 检查Job是否活跃
     */
    fun isJobActive(jobName: String): Boolean {
        return jobs[jobName]?.isActive ?: false
    }
    
    /**
     * 等待Job完成
     */
    suspend fun awaitJob(jobName: String) {
        jobs[jobName]?.join()
    }
    
    /**
     * 获取活跃的Job列表
     */
    fun getActiveJobs(): List<String> {
        return jobs.filter { it.value.isActive }.keys.toList()
    }
    
    // =================== 主线程操作 ===================
    
    /**
     * 在主线程中执行（非协程）
     */
    fun runOnMainThread(action: () -> Unit) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            action()
        } else {
            mainHandler.post(action)
        }
    }
    
    /**
     * 延迟在主线程中执行
     */
    fun runOnMainThreadDelayed(delayMs: Long, action: () -> Unit) {
        mainHandler.postDelayed(action, delayMs)
    }
    
    /**
     * 移除主线程中的延迟任务
     */
    fun removeMainThreadCallbacks(action: () -> Unit) {
        mainHandler.removeCallbacks(action)
    }
    
    // =================== 工具方法 ===================
    
    /**
     * 延迟执行
     */
    suspend fun delayExecution(timeMs: Long) {
        delay(timeMs)
    }
    
    /**
     * 重试机制
     */
    suspend fun <T> retry(
        times: Int = 3,
        delayMs: Long = 1000,
        block: suspend () -> T
    ): T {
        repeat(times - 1) { attempt ->
            try {
                return block()
            } catch (e: Exception) {
                LogUtil.w("CoroutineManager", "Retry attempt ${attempt + 1} failed$e" )
                delay(delayMs)
            }
        }
        return block() // 最后一次尝试，如果失败则抛出异常
    }
    
    /**
     * 超时执行
     */
    suspend fun <T> withTimeout(
        timeoutMs: Long,
        block: suspend CoroutineScope.() -> T
    ): T {
        return kotlinx.coroutines.withTimeout(timeoutMs, block)
    }
    
    // =================== 监控和统计 ===================
    
    /**
     * 获取协程统计信息
     */
    fun getCoroutineStats(): CoroutineStats {
        return CoroutineStats(
            activeCoroutines = activeCoroutinesCount.get(),
            totalLaunched = totalCoroutinesLaunched.get(),
            totalFailed = failedCoroutinesCount.get(),
            activeScopes = scopes.size,
            activeJobs = jobs.size
        )
    }
    
    /**
     * 开始监控
     */
    private fun startMonitoring() {
        launchDefault(jobName = "CoroutineMonitor") {
            while (true) {
                delay(30000) // 每30秒记录一次统计信息
                val stats = getCoroutineStats()
                LogUtil.d("CoroutineManager", "Coroutine Stats: $stats")
                
                // 检查是否有过多的活跃协程
                if (stats.activeCoroutines > 100) {
                    LogUtil.w("CoroutineManager", "High number of active coroutines: ${stats.activeCoroutines}")
                }
            }
        }
    }
    
    /**
     * 处理协程异常
     */
    private fun handleCoroutineException(throwable: Throwable, context: CoroutineContext) {
        failedCoroutinesCount.incrementAndGet()
        
        // 可以在这里添加崩溃上报、通知等逻辑
        when (throwable) {
            is OutOfMemoryError -> {
                LogUtil.e("CoroutineManager", "OutOfMemoryError in coroutine, triggering cleanup")
                // 触发内存清理
                runOnMainThread {
                    System.gc()
                }
            }
            is StackOverflowError -> {
                LogUtil.e("CoroutineManager", "StackOverflowError in coroutine")
                // 可能需要重启某些组件
            }
            else -> {
                LogUtil.e("CoroutineManager", "Coroutine exception: ${throwable.javaClass.simpleName}")
            }
        }
    }
    
    // =================== 清理方法 ===================
    
    /**
     * 清理所有资源
     */
    fun shutdown() {
        LogUtil.d("CoroutineManager", "Shutting down CoroutineManager")
        
        // 取消所有作用域
        cancelAllScopes()
        
        // 取消管理作用域
        managerScope.cancel("CoroutineManager shutdown")
        
        // 关闭线程池
        cpuThreadPool.shutdown()
        ioThreadPool.shutdown()
        heavyThreadPool.shutdown()
        
        // 清理其他资源
        jobs.clear()
        mainHandler.removeCallbacksAndMessages(null)
        
        LogUtil.d("CoroutineManager", "CoroutineManager shutdown completed")
    }
    
    // =================== 私有方法 ===================
    
    private fun createThreadFactory(namePrefix: String): ThreadFactory {
        return ThreadFactory { runnable ->
            Thread(runnable, "$namePrefix-${System.currentTimeMillis()}").apply {
                isDaemon = false
                priority = Thread.NORM_PRIORITY
            }
        }
    }
}

/**
 * 协程统计信息
 */
data class CoroutineStats(
    val activeCoroutines: Int,
    val totalLaunched: Long,
    val totalFailed: Long,
    val activeScopes: Int,
    val activeJobs: Int
) {
    val successRate: Double
        get() = if (totalLaunched > 0) {
            ((totalLaunched - totalFailed).toDouble() / totalLaunched.toDouble()) * 100
        } else 0.0
        
    override fun toString(): String {
        return "CoroutineStats(active=$activeCoroutines, total=$totalLaunched, failed=$totalFailed, " +
                "scopes=$activeScopes, jobs=$activeJobs, successRate=${"%.2f".format(successRate)}%)"
    }
}