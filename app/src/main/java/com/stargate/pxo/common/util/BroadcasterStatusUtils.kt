package com.stargate.pxo.common.util

import android.util.Log
import androidx.compose.ui.graphics.Color
import com.stargate.pxo.common.constant.ApiConstants

/**
 * 主播状态工具类
 * 提供状态相关的颜色、文本等工具方法
 */
object BroadcasterStatusUtils {
    
    /**
     * 根据主播状态获取对应的颜色
     * @param status 主播状态（支持忽略大小写）
     * @return 对应的颜色
     */
    fun getStatusColor(status: String?): Color {
        if (status.isNullOrBlank()) {
            return Color(0xFF9E9E9E) // 灰色 - 未知状态
        }
        
        return when (status.uppercase()) {
            ApiConstants.BroadcasterStatus.STATUS_ONLINE.uppercase() ->
                Color(0xFF38C54D) // 绿色 - 在线

            ApiConstants.BroadcasterStatus.STATUS_BUSY.uppercase() -> 
                Color(0xFFFF9800) // 橙色 - 忙碌
                
            ApiConstants.BroadcasterStatus.STATUS_CALLING.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_IN.uppercase() -> 
                Color(0xFFFF455D) // 粉色 - 通话中

            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_IN.uppercase() -> 
                Color(0xFF4CA9F7) // 蓝色 - 匹配中

            else -> Color(0xFFCCCCCC) // 灰色 - 离线或未知状态
        }
    }
    
    /**
     * 根据主播状态获取状态文本描述
     * @param status 主播状态（支持忽略大小写）
     * @return 状态描述文本
     */
    fun getStatusText(status: String?): String {
        if (status.isNullOrBlank()) {
            return "offline"
        }
        
        return when (status.uppercase()) {
            ApiConstants.BroadcasterStatus.STATUS_ONLINE.uppercase() -> "online"
            ApiConstants.BroadcasterStatus.STATUS_BUSY.uppercase() -> "busy"
            ApiConstants.BroadcasterStatus.STATUS_CALLING.uppercase() -> "calling"
            ApiConstants.BroadcasterStatus.STATUS_CALLING_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_IN.uppercase() -> "inCall"
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_IN.uppercase() -> "matching"
            else -> "offline"
        }
    }
    
    /**
     * 判断主播是否在线（可通话）
     * @param status 主播状态（支持忽略大小写）
     * @return 是否可通话
     */
    fun isOnline(status: String?): Boolean {
        if (status.isNullOrBlank()) {
            return false
        }
        
        return status.uppercase() == ApiConstants.BroadcasterStatus.STATUS_ONLINE.uppercase()
    }
    
    /**
     * 判断主播是否忙碌
     * @param status 主播状态（支持忽略大小写）
     * @return 是否忙碌
     */
    fun isBusy(status: String?): Boolean {
        if (status.isNullOrBlank()) {
            return false
        }
        
        return when (status.uppercase()) {
            ApiConstants.BroadcasterStatus.STATUS_BUSY.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_IN.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_IN.uppercase() -> true
            else -> false
        }
    }
    
    /**
     * 获取状态优先级（用于排序）
     * 数值越小优先级越高
     * @param status 主播状态（支持忽略大小写）
     * @return 优先级数值
     */
    fun getStatusPriority(status: String?): Int {
        if (status.isNullOrBlank()) {
            return 999 // 最低优先级
        }
        
        return when (status.uppercase()) {
            ApiConstants.BroadcasterStatus.STATUS_ONLINE.uppercase() -> 1 // 最高优先级
            ApiConstants.BroadcasterStatus.STATUS_BUSY.uppercase() -> 2
            ApiConstants.BroadcasterStatus.STATUS_CALLING.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_IN.uppercase() -> 3
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_OUT.uppercase(),
            ApiConstants.BroadcasterStatus.STATUS_CALLING_MATCH_IN.uppercase() -> 4
            else -> 5 // 离线状态
        }
    }

    fun decodeJsonEscapedUrl(url: String): String {
        if (url.isBlank()) return url

        return try {
            // 先处理 JSON 转义字符
            var decodedUrl = url
                .replace("\\u003d", "=")
                .replace("\\u0026", "&")
                .replace("\\\\u003d", "=")
                .replace("\\\\u0026", "&")
                .replace("\\u002f", "/")
                .replace("\\\\u002f", "/")
                .replace("\\u003a", ":")
                .replace("\\\\u003a", ":")
                .replace("\\u003f", "?")
                .replace("\\\\u003f", "?")
                .replace("\\u0025", "%")
                .replace("\\\\u0025", "%")
                .replace("\\u002b", "+")
                .replace("\\\\u002b", "+")

            // 然后处理 URL 编码（如果需要）
            // 注意：只解码特定的字符，避免破坏 URL 结构
            decodedUrl = decodedUrl
                .replace("%2B", "+")
                .replace("%2F", "/")
                .replace("%3D", "=")
                .replace("%26", "&")
                .replace("%3F", "?")
                .replace("%3A", ":")

            Log.d("URLDecode", "Original: $url")
            Log.d("URLDecode", "Decoded: $decodedUrl")

            decodedUrl
        } catch (e: Exception) {
            Log.e("URLDecode", "Failed to decode URL: $url", e)
            url // 如果解码失败，返回原始 URL
        }
    }
}




/**
 * 扩展函数：为 String 添加获取状态颜色的方法
 */
fun String?.getStatusColor(): Color = BroadcasterStatusUtils.getStatusColor(this)

/**
 * 扩展函数：为 String 添加获取状态文本的方法
 */
fun String?.getStatusText(): String = BroadcasterStatusUtils.getStatusText(this)

/**
 * 扩展函数：为 String 添加判断是否在线的方法
 */
fun String?.isOnlineStatus(): Boolean = BroadcasterStatusUtils.isOnline(this)

/**
 * 扩展函数：为 String 添加判断是否忙碌的方法
 */
fun String?.isBusyStatus(): Boolean = BroadcasterStatusUtils.isBusy(this)

/**
 * 扩展函数：为 String 添加获取状态优先级的方法
 */
fun String?.getStatusPriority(): Int = BroadcasterStatusUtils.getStatusPriority(this)
