package com.stargate.pxo.common.base

import androidx.lifecycle.ViewModel
import com.stargate.pxo.presentation.ui.screens.main.home.HomeUiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

abstract class BaseViewModel<T : UiState> : ViewModel() {
    
    private val _uiState = MutableStateFlow(createInitialState())
    val uiState: StateFlow<T> = _uiState.asStateFlow()

    abstract fun createInitialState(): T
    
    protected fun updateState(reducer: T.() -> T) {
        _uiState.value = _uiState.value.reducer()
    }

}

interface UiState