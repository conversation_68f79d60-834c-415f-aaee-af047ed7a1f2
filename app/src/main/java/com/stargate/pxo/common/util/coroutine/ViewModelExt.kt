package com.stargate.pxo.common.util.coroutine

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * ViewModel 协程扩展函数
 * 提供ViewModel中使用协程的简化方法
 */

/**
 * 在UI线程执行协程
 * 对标准viewModelScope.launch的简化包装
 */
fun ViewModel.launchUI(
    onError: ((Throwable) -> Unit)? = null,
    block: suspend () -> Unit
): Job {
    return PuxxiCoroutine.ui {
        try {
            block()
        } catch (e: Throwable) {
            onError?.invoke(e) ?: throw e
        }
    }
}

/**
 * 在IO线程执行协程
 * 自动切换到IO线程执行，处理完成后自动切回主线程
 */
fun ViewModel.launchIO(
    onError: ((Throwable) -> Unit)? = null,
    block: suspend () -> Unit
): Job {
    return viewModelScope.launch {
        try {
            withContext(Dispatchers.IO) {
                block()
            }
        } catch (e: Throwable) {
            onError?.invoke(e) ?: throw e
        }
    }
}

/**
 * 简化数据加载模式
 * 封装了加载中、成功、错误的状态处理
 */
fun <T> ViewModel.loadData(
    dispatcher: CoroutineDispatcher = Dispatchers.IO,
    loader: suspend () -> T,
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit,
    onLoading: (() -> Unit)? = null
): Job {
    return viewModelScope.launch {
        try {
            onLoading?.invoke()
            val result = withContext(dispatcher) { loader() }
            onSuccess(result)
        } catch (e: Throwable) {
            onError(e)
        }
    }
}

/**
 * 收集Flow
 * 简化Flow收集和状态处理
 */
fun <T> ViewModel.collectFlow(
    flow: Flow<T>,
    onStart: (() -> Unit)? = null,
    onComplete: (() -> Unit)? = null,
    onError: ((Throwable) -> Unit)? = null,
    collector: (T) -> Unit
): Job {
    return flow
        .flowOn(Dispatchers.IO)
        .onStart { onStart?.invoke() }
        .onEach { collector(it) }
        .onCompletion { onComplete?.invoke() }
        .catch { error -> onError?.invoke(error) ?: throw error }
        .launchIn(viewModelScope)
}

/**
 * Flow扩展 - 在ViewModel中收集Flow
 */
fun <T> Flow<T>.collectIn(
    viewModel: ViewModel,
    onStart: (() -> Unit)? = null,
    onComplete: (() -> Unit)? = null,
    onError: ((Throwable) -> Unit)? = null,
    collector: (T) -> Unit
): Job {
    return viewModel.collectFlow(
        flow = this,
        onStart = onStart,
        onComplete = onComplete,
        onError = onError,
        collector = collector
    )
} 