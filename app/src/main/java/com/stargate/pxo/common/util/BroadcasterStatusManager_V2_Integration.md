# BroadcasterStatusManager V2 接口集成

## 🎯 新增功能

BroadcasterStatusManager 现在集成了 `getUserOnlineStatusPostV2` 接口，提供更灵活的状态查询方式。

## 📋 接口策略

### 1. **双接口支持**

#### getUserListOnlineStatusPostV3 (批量接口)
- 🎯 **用途**: 批量查询多个用户状态
- 📊 **返回**: `List<Map<String, Any>>`
- 🔄 **场景**: 轮询、主播墙、消息列表

#### getUserOnlineStatusPostV2 (单个接口) ✨ **新增**
- 🎯 **用途**: 查询单个用户状态
- 📊 **返回**: `String`
- ⚡ **场景**: 个人资料、通话前检查、实时查询

### 2. **智能选择策略**

```kotlin
// 轮询时根据数量自动选择接口
if (userIdsToUpdate.size == 1) {
    // 单个用户使用 V2 接口
    requestSingleUserStatus(userIdsToUpdate.first())
} else {
    // 多个用户使用 V3 批量接口
    userIdsToUpdate.chunked(BATCH_SIZE).forEach { batch ->
        requestStatusBatchInternal(batch)
    }
}
```

## 🔧 新增方法

### 1. **立即请求单个状态**

```kotlin
/**
 * 立即请求单个用户状态（不加入轮询队列）
 * 适用于需要立即获取状态的场景
 */
fun requestStatusImmediately(userId: String)
```

**使用场景：**
- 👤 **个人资料页** - 进入时立即查询用户状态
- 📞 **通话前检查** - 确认对方是否在线
- 🔍 **搜索结果** - 查询搜索到的用户状态

### 2. **内部单个请求方法**

```kotlin
/**
 * 请求单个用户状态（使用 V2 接口）
 */
suspend fun requestSingleUserStatus(userId: String)
```

**特点：**
- ✅ 使用 getUserOnlineStatusPostV2 接口
- ✅ 完整的错误处理和日志记录
- ✅ 自动更新缓存和通知UI

### 3. **单个状态响应处理**

```kotlin
/**
 * 处理单个用户状态响应
 */
private fun handleSingleStatusResponse(userId: String, status: String)
```

**功能：**
- ✅ 检查状态是否真正变化
- ✅ 更新缓存并保持LRU顺序
- ✅ 只在状态变化时通知UI

## 📊 使用场景对比

### 场景1：主播墙页面（批量查询）

```kotlin
// HomeViewModel 中
private fun requestBroadcasterStatuses() {
    val broadcasterIds = currentState.broadcasters.map { it.id }
    if (broadcasterIds.isNotEmpty()) {
        // 使用批量接口，添加到轮询队列
        broadcasterStatusManager.requestStatusBatch(broadcasterIds)
    }
}
```

**使用接口：** `getUserListOnlineStatusPostV3`

### 场景2：个人资料页（单个查询）

```kotlin
// ProfileViewModel 中
fun loadUserProfile(userId: String) {
    // 立即查询用户状态，不加入轮询
    broadcasterStatusManager.requestStatusImmediately(userId)
    
    // 监听状态变化
    viewModelScope.launch {
        broadcasterStatusManager.statusUpdates.collectLatest { statusUpdates ->
            statusUpdates[userId]?.let { status ->
                updateProfileStatus(status.status)
            }
        }
    }
}
```

**使用接口：** `getUserOnlineStatusPostV2`

### 场景3：通话前检查（单个查询）

```kotlin
// CallViewModel 中
fun checkUserBeforeCall(userId: String) {
    viewModelScope.launch {
        // 立即检查用户状态
        broadcasterStatusManager.requestStatusImmediately(userId)
        
        // 等待状态更新
        broadcasterStatusManager.statusUpdates
            .filter { it.containsKey(userId) }
            .take(1)
            .collect { statusUpdates ->
                val userStatus = statusUpdates[userId]?.status
                if (userStatus == "online") {
                    initiateCall(userId)
                } else {
                    showUserOfflineMessage()
                }
            }
    }
}
```

**使用接口：** `getUserOnlineStatusPostV2`

### 场景4：轮询机制（智能选择）

```kotlin
// BroadcasterStatusManager 内部轮询
private suspend fun startPolling() {
    while (isPollingActive && scope.isActive) {
        val userIdsToUpdate = getExpiredUserIds()
        
        if (userIdsToUpdate.isNotEmpty()) {
            if (userIdsToUpdate.size == 1) {
                // 单个用户：使用 V2 接口
                requestSingleUserStatus(userIdsToUpdate.first())
            } else {
                // 多个用户：使用 V3 批量接口
                requestStatusBatchInternal(userIdsToUpdate)
            }
        }
        
        delay(POLLING_INTERVAL)
    }
}
```

**使用接口：** 根据数量智能选择

## 🎯 方法选择指南

### 何时使用 requestStatus() ?

```kotlin
// ✅ 适用场景：需要持续监控状态的场景
broadcasterStatusManager.requestStatus("user123")
```

- 🏠 **主播墙** - 需要持续监控主播状态
- 💬 **消息列表** - 需要持续显示联系人状态
- 👥 **关注列表** - 需要持续监控关注的主播

### 何时使用 requestStatusImmediately() ?

```kotlin
// ✅ 适用场景：需要立即获取状态，不需要持续监控
broadcasterStatusManager.requestStatusImmediately("user123")
```

- 👤 **个人资料页** - 进入时查询一次即可
- 📞 **通话前检查** - 只需要确认当前状态
- 🔍 **搜索结果** - 显示搜索时的状态

### 何时使用 requestStatusBatch() ?

```kotlin
// ✅ 适用场景：需要查询多个用户状态
val userIds = listOf("user1", "user2", "user3")
broadcasterStatusManager.requestStatusBatch(userIds)
```

- 📱 **列表页面** - 批量查询列表中的用户
- 🔄 **批量操作** - 需要同时处理多个用户

## 📈 性能优化

### 1. **智能接口选择**

```kotlin
// 轮询时的智能选择
if (expiredUsers.size == 1) {
    // 单个用户：响应更快
    requestSingleUserStatus(expiredUsers.first())
} else {
    // 多个用户：网络效率更高
    requestStatusBatchInternal(expiredUsers)
}
```

### 2. **避免重复请求**

```kotlin
// ✅ 推荐：立即查询不加入轮询
fun checkUserStatusOnce(userId: String) {
    broadcasterStatusManager.requestStatusImmediately(userId)
}

// ❌ 不推荐：加入轮询后立即移除
fun checkUserStatusOnce(userId: String) {
    broadcasterStatusManager.requestStatus(userId)
    // 需要手动移除，容易忘记
    broadcasterStatusManager.removeBroadcaster(userId)
}
```

### 3. **合理的缓存利用**

```kotlin
// 先检查缓存，避免不必要的网络请求
fun getUserStatusWithCache(userId: String): String? {
    val cachedStatus = broadcasterStatusManager.getStatus(userId)
    
    if (cachedStatus != null && !isStatusExpired(cachedStatus)) {
        return cachedStatus.status
    } else {
        // 缓存过期或不存在，立即请求
        broadcasterStatusManager.requestStatusImmediately(userId)
        return null // 异步获取
    }
}
```

## 🔍 调试和监控

### 日志标签

搜索 `BroadcasterStatusManager` 可以看到：

```
D/BroadcasterStatusManager: Requesting single user status: user123
D/BroadcasterStatusManager: Successfully updated single user status: user123 -> online
D/BroadcasterStatusManager: Updated single user status: user123 -> online
D/BroadcasterStatusManager: Notified single user status change: user123
```

### 错误处理

```
E/BroadcasterStatusManager: Failed to get single user status for user123: Network timeout
```

## ✅ 总结

**BroadcasterStatusManager 现在支持双接口策略！**

### 新增功能：
1. ✅ **getUserOnlineStatusPostV2 集成** - 单个用户状态查询
2. ✅ **智能接口选择** - 根据用户数量自动选择最优接口
3. ✅ **立即查询方法** - 不加入轮询队列的即时查询
4. ✅ **完整错误处理** - 单个接口的完整错误处理和日志

### 使用策略：
- 🔄 **持续监控** - 使用 `requestStatus()` 加入轮询队列
- ⚡ **立即查询** - 使用 `requestStatusImmediately()` 即时获取
- 📦 **批量查询** - 使用 `requestStatusBatch()` 批量处理

### 性能优势：
- 🎯 **精确控制** - 根据场景选择最合适的接口
- ⚡ **响应速度** - 单个查询响应更快
- 🔄 **网络效率** - 批量查询网络效率更高
- 💾 **资源优化** - 避免不必要的轮询和缓存

现在你可以根据不同的使用场景选择最合适的状态查询方式了！
