package com.stargate.pxo.common.util

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.ref.WeakReference
import androidx.core.content.edit
import com.stargate.pxo.common.constant.ApiConstants

/**
 * SharedPreferences工具类
 * 提供简单易用的轻量级存储功能
 * 包含加密存储能力
 */
object SPUtil {
    
    private const val DEFAULT_NAME = "puxxi_sp"
    private const val SECURE_PREFS_NAME = "puxxi_secure_prefs"
    private var context: WeakReference<Context>? = null
    private var defaultSP: SharedPreferences? = null
    val gson = Gson()
    
    // 使用ApiConstants.APP_API_DOMAIN作为密钥，确保长度为32字节
    private val ENCRYPTION_KEY by lazy { 
        ApiConstants.APP_API_DOMAIN.padEnd(32, '0')
    }
    
    /**
     * 初始化SPUtil
     */
    fun init(context: Context) {
        this.context = WeakReference(context.applicationContext)
        defaultSP = context.getSharedPreferences(DEFAULT_NAME, Context.MODE_PRIVATE)
    }
    
    /**
     * 获取默认SharedPreferences
     */
    private fun getDefaultSP(): SharedPreferences {
        return defaultSP ?: throw IllegalStateException("SPUtil not initialized")
    }
    
    /**
     * 获取指定名称的SharedPreferences
     */
    fun getSP(name: String): SharedPreferences {
        val ctx = context?.get() ?: throw IllegalStateException("Context is null")
        return ctx.getSharedPreferences(name, Context.MODE_PRIVATE)
    }
    
    /**
     * 检查SPUtil是否已初始化
     */
    fun isInitialized(): Boolean {
        return defaultSP != null
    }
    
    // =================== 存储方法 ===================
    
    /**
     * 存储String
     */
    fun putString(key: String, value: String?) {
        getDefaultSP().edit { putString(key, value) }
    }
    
    fun putString(spName: String, key: String, value: String?) {
        getSP(spName).edit { putString(key, value) }
    }
    
    /**
     * 存储Int
     */
    fun putInt(key: String, value: Int) {
        getDefaultSP().edit { putInt(key, value) }
    }
    
    fun putInt(spName: String, key: String, value: Int) {
        getSP(spName).edit { putInt(key, value) }
    }
    
    /**
     * 存储Long
     */
    fun putLong(key: String, value: Long) {
        getDefaultSP().edit { putLong(key, value) }
    }
    
    fun putLong(spName: String, key: String, value: Long) {
        getSP(spName).edit { putLong(key, value) }
    }
    
    /**
     * 存储Float
     */
    fun putFloat(key: String, value: Float) {
        getDefaultSP().edit { putFloat(key, value) }
    }
    
    fun putFloat(spName: String, key: String, value: Float) {
        getSP(spName).edit { putFloat(key, value) }
    }
    
    /**
     * 存储Boolean
     */
    fun putBoolean(key: String, value: Boolean) {
        getDefaultSP().edit { putBoolean(key, value) }
    }
    
    fun putBoolean(spName: String, key: String, value: Boolean) {
        getSP(spName).edit { putBoolean(key, value) }
    }
    
    /**
     * 存储StringSet
     */
    fun putStringSet(key: String, value: Set<String>?) {
        getDefaultSP().edit { putStringSet(key, value) }
    }
    
    fun putStringSet(spName: String, key: String, value: Set<String>?) {
        getSP(spName).edit { putStringSet(key, value) }
    }
    
    /**
     * 存储对象（使用Gson序列化）
     */
    fun putObject(key: String, value: Any?) {
        val json = if (value != null) gson.toJson(value) else null
        putString(key, json)
    }
    
    fun putObject(spName: String, key: String, value: Any?) {
        val json = if (value != null) gson.toJson(value) else null
        putString(spName, key, json)
    }
    
    /**
     * 存储List
     */
    fun <T> putList(key: String, list: List<T>?) {
        val json = if (list != null) gson.toJson(list) else null
        putString(key, json)
    }
    
    fun <T> putList(spName: String, key: String, list: List<T>?) {
        val json = if (list != null) gson.toJson(list) else null
        putString(spName, key, json)
    }
    
    // =================== 加密存储方法 ===================
    
    /**
     * 存储加密的字符串
     */
    fun putEncryptedString(key: String, value: String?) {
        if (value == null) {
            remove(SECURE_PREFS_NAME, key)
            return
        }
        
        // 使用AESUtil加密
        val encryptedValue = AESUtil.encrypt2Base64(value, ENCRYPTION_KEY)
        putString(SECURE_PREFS_NAME, key, encryptedValue)
    }
    
    /**
     * 获取解密的字符串
     */
    fun getDecryptedString(key: String, defaultValue: String? = null): String? {
        val encryptedValue = getString(SECURE_PREFS_NAME, key) ?: return defaultValue
        
        // 使用AESUtil解密
        return AESUtil.decryptBase64(encryptedValue, ENCRYPTION_KEY) ?: defaultValue
    }
    
    /**
     * 存储加密的对象
     */
    fun putEncryptedObject(key: String, value: Any?) {
        if (value == null) {
            remove(SECURE_PREFS_NAME, key)
            return
        }
        
        // 转换为JSON字符串
        val json = gson.toJson(value)
        putEncryptedString(key, json)
    }
    
    /**
     * 获取解密的对象
     */
    inline fun <reified T> getDecryptedObject(key: String): T? {
        val json = getDecryptedString(key) ?: return null
        return try {
            gson.fromJson(json, T::class.java)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 存储加密的列表
     */
    fun <T> putEncryptedList(key: String, list: List<T>?) {
        if (list == null) {
            remove(SECURE_PREFS_NAME, key)
            return
        }
        
        // 转换为JSON字符串
        val json = gson.toJson(list)
        putEncryptedString(key, json)
    }
    
    /**
     * 获取解密的列表
     */
    inline fun <reified T> getDecryptedList(key: String): List<T>? {
        val json = getDecryptedString(key) ?: return null
        return try {
            val type = object : TypeToken<List<T>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 清除所有加密的数据
     */
    fun clearEncrypted() {
        clear(SECURE_PREFS_NAME)
    }
    
    // =================== 获取方法 ===================
    
    /**
     * 获取String
     */
    fun getString(key: String, defaultValue: String? = null): String? {
        return getDefaultSP().getString(key, defaultValue)
    }
    
    fun getString(spName: String, key: String, defaultValue: String? = null): String? {
        return getSP(spName).getString(key, defaultValue)
    }
    
    /**
     * 获取Int
     */
    fun getInt(key: String, defaultValue: Int = 0): Int {
        return getDefaultSP().getInt(key, defaultValue)
    }
    
    fun getInt(spName: String, key: String, defaultValue: Int = 0): Int {
        return getSP(spName).getInt(key, defaultValue)
    }
    
    /**
     * 获取Long
     */
    fun getLong(key: String, defaultValue: Long = 0L): Long {
        return getDefaultSP().getLong(key, defaultValue)
    }
    
    fun getLong(spName: String, key: String, defaultValue: Long = 0L): Long {
        return getSP(spName).getLong(key, defaultValue)
    }
    
    /**
     * 获取Float
     */
    fun getFloat(key: String, defaultValue: Float = 0f): Float {
        return getDefaultSP().getFloat(key, defaultValue)
    }
    
    fun getFloat(spName: String, key: String, defaultValue: Float = 0f): Float {
        return getSP(spName).getFloat(key, defaultValue)
    }
    
    /**
     * 获取Boolean
     */
    fun getBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return getDefaultSP().getBoolean(key, defaultValue)
    }
    
    fun getBoolean(spName: String, key: String, defaultValue: Boolean = false): Boolean {
        return getSP(spName).getBoolean(key, defaultValue)
    }
    
    /**
     * 获取StringSet
     */
    fun getStringSet(key: String, defaultValue: Set<String>? = null): Set<String>? {
        return getDefaultSP().getStringSet(key, defaultValue)
    }
    
    fun getStringSet(spName: String, key: String, defaultValue: Set<String>? = null): Set<String>? {
        return getSP(spName).getStringSet(key, defaultValue)
    }
    
    /**
     * 获取对象（使用Gson反序列化）
     */
    inline fun <reified T> getObject(key: String): T? {
        val json = getString(key)
        return if (json.isNullOrEmpty()) null else {
            try {
                gson.fromJson(json, T::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    inline fun <reified T> getObject(spName: String, key: String): T? {
        val json = getString(spName, key)
        return if (json.isNullOrEmpty()) null else {
            try {
                gson.fromJson(json, T::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * 获取List
     */
    inline fun <reified T> getList(key: String): List<T>? {
        val json = getString(key)
        return if (json.isNullOrEmpty()) null else {
            try {
                val type = object : TypeToken<List<T>>() {}.type
                gson.fromJson(json, type)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    inline fun <reified T> getList(spName: String, key: String): List<T>? {
        val json = getString(spName, key)
        return if (json.isNullOrEmpty()) null else {
            try {
                val type = object : TypeToken<List<T>>() {}.type
                gson.fromJson(json, type)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // =================== 判断方法 ===================
    
    /**
     * 判断是否包含某个key
     */
    fun contains(key: String): Boolean {
        return getDefaultSP().contains(key)
    }
    
    fun contains(spName: String, key: String): Boolean {
        return getSP(spName).contains(key)
    }
    
    // =================== 删除方法 ===================
    
    /**
     * 删除指定key的数据
     */
    fun remove(key: String) {
        getDefaultSP().edit { remove(key) }
    }
    
    fun remove(spName: String, key: String) {
        getSP(spName).edit { remove(key) }
    }
    
    /**
     * 删除多个key的数据
     */
    fun remove(vararg keys: String) {
        getDefaultSP().edit {
            keys.forEach { key ->
                remove(key)
            }
        }
    }
    
    fun remove(spName: String, vararg keys: String) {
        getSP(spName).edit {
            keys.forEach { key ->
                remove(key)
            }
        }
    }
    
    /**
     * 清空所有数据
     */
    fun clear() {
        getDefaultSP().edit { clear() }
    }
    
    fun clear(spName: String) {
        getSP(spName).edit { clear() }
    }
    
    // =================== 获取所有数据 ===================
    
    /**
     * 获取所有键值对
     */
    fun getAll(): Map<String, *> {
        return getDefaultSP().all
    }
    
    fun getAll(spName: String): Map<String, *> {
        return getSP(spName).all
    }
    
    // =================== 批量操作 ===================
    
    /**
     * 批量存储（使用同一个Editor，提高性能）
     */
    internal inline fun batch(action: SharedPreferences.Editor.() -> Unit) {
        getDefaultSP().edit().apply(action).apply()
    }
    
    inline fun batch(spName: String, action: SharedPreferences.Editor.() -> Unit) {
        getSP(spName).edit().apply(action).apply()
    }
    
    // =================== 同步提交 ===================
    
    /**
     * 同步提交（commit）
     */
    fun putStringSync(key: String, value: String?) {
        return getDefaultSP().edit(commit = true) { putString(key, value) }
    }
    
    fun putIntSync(key: String, value: Int) {
        return getDefaultSP().edit(commit = true) { putInt(key, value) }
    }
    
    fun putLongSync(key: String, value: Long) {
        return getDefaultSP().edit(commit = true) { putLong(key, value) }
    }
    
    fun putFloatSync(key: String, value: Float) {
        return getDefaultSP().edit(commit = true) { putFloat(key, value) }
    }
    
    fun putBooleanSync(key: String, value: Boolean) {
        return getDefaultSP().edit(commit = true) { putBoolean(key, value) }
    }
    
    fun removeSync(key: String) {
        return getDefaultSP().edit(commit = true) { remove(key) }
    }
    
    fun clearSync() {
        return getDefaultSP().edit(commit = true) { clear() }
    }
}