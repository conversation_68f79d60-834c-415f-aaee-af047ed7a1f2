package com.stargate.pxo.common.util

import android.util.Log
import com.stargate.pxo.data.network.Resource
import com.stargate.pxo.data.repository.BroadcasterInfoRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主播状态管理类
 * 负责管理主播的在线状态，提供状态查询和监听功能
 * 支持轮询机制，每2.5秒轮询一次，保证状态实时性
 */
@Singleton
class BroadcasterStatusManager @Inject constructor(
    private val broadcasterInfoRepository: BroadcasterInfoRepository
) {
    companion object {
        private const val TAG = "BroadcasterStatusManager"
        private const val MAX_CACHE_SIZE = 20 // 最大缓存数量，避免内存爆炸
        private const val BATCH_SIZE = 20 // 批量查询大小
        private const val POLLING_INTERVAL = 2500L // 轮询间隔：2.5秒
        private const val STATUS_EXPIRE_TIME = 5000L // 状态过期时间：5秒
    }
    
    // 协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 使用 LinkedHashMap 保持插入顺序，便于LRU管理
    private val statusCache = LinkedHashMap<String, BroadcasterStatus>()

    // 状态变化的 Flow - 使用 StateFlow 保证状态的一致性
    private val _statusUpdates = MutableStateFlow<Map<String, BroadcasterStatus>>(emptyMap())
    val statusUpdates: StateFlow<Map<String, BroadcasterStatus>> = _statusUpdates.asStateFlow()

    // 正在请求的用户ID集合，避免重复请求
    private val requestingIds = mutableSetOf<String>()

    // 轮询状态标志
    @Volatile
    private var isPollingActive = false
    
    /**
     * 主播状态数据类
     */
    data class BroadcasterStatus(
        val userId: String,
        val status: String,
        val lastUpdateTime: Long = System.currentTimeMillis()
    )
    
    /**
     * 根据主播ID获取状态
     * @param userId 主播ID
     * @return 主播状态，如果缓存中没有则返回null
     */
    fun getStatus(userId: String): BroadcasterStatus? {
        synchronized(statusCache) {
            val status = statusCache[userId]
            if (status != null) {
                // 访问时移到最前面（LRU策略）
                statusCache.remove(userId)
                statusCache[userId] = status
            }
            return status
        }
    }
    
    /**
     * 根据主播ID列表批量获取状态
     * @param userIds 主播ID列表
     * @return 状态映射表
     */
    fun getStatusBatch(userIds: List<String>): Map<String, BroadcasterStatus> {
        synchronized(statusCache) {
            return userIds.mapNotNull { userId ->
                val status = statusCache[userId]
                if (status != null) {
                    // 访问时移到最前面（LRU策略）
                    statusCache.remove(userId)
                    statusCache[userId] = status
                    userId to status
                } else {
                    null
                }
            }.toMap()
        }
    }
    
    /**
     * 请求单个主播状态（添加到轮询队列）
     * @param userId 主播ID
     */
    fun requestStatus(userId: String) {
        addToPollingQueue(listOf(userId))
    }

    /**
     * 立即请求单个用户状态（不加入轮询队列）
     * 适用于需要立即获取状态的场景，如个人资料页、通话前检查等
     * @param userId 用户ID
     */
    fun requestStatusImmediately(userId: String) {
        scope.launch {
            requestSingleUserStatus(userId)
        }
    }

    /**
     * 批量请求主播状态（添加到轮询队列）
     * @param userIds 主播ID列表
     */
    fun requestStatusBatch(userIds: List<String>) {
        addToPollingQueue(userIds)
    }

    /**
     * 添加主播到轮询队列
     * @param userIds 主播ID列表
     */
    private fun addToPollingQueue(userIds: List<String>) {
        if (userIds.isEmpty()) return

        synchronized(statusCache) {
            userIds.forEach { userId ->
                // 如果已存在，先移除再添加（移到最前面）
                if (statusCache.containsKey(userId)) {
                    val existingStatus = statusCache.remove(userId)
                    statusCache[userId] = existingStatus!!
                } else {
                    // 新增主播，创建初始状态
                    statusCache[userId] = BroadcasterStatus(
                        userId = userId,
                        status = "unknown",
                        lastUpdateTime = 0L // 设为0，强制第一次更新
                    )
                }
            }

            // 清理超出限制的缓存（移除最旧的）
            while (statusCache.size > MAX_CACHE_SIZE) {
                val oldestKey = statusCache.keys.first()
                statusCache.remove(oldestKey)
                Log.d(TAG, "Removed oldest broadcaster from cache: $oldestKey")
            }
        }

        Log.d(TAG, "Added ${userIds.size} broadcasters to polling queue, total: ${statusCache.size}")

        // 启动轮询（如果还没启动）
        startPollingIfNeeded()
    }

    /**
     * 启动轮询机制
     */
    private fun startPollingIfNeeded() {
        if (!isPollingActive) {
            isPollingActive = true
            scope.launch {
                startPolling()
            }
        }
    }

    /**
     * 轮询主循环
     */
    private suspend fun startPolling() {
        Log.d(TAG, "Starting status polling...")

        while (isPollingActive && scope.isActive) {
            try {
                val currentTime = System.currentTimeMillis()
                val userIdsToUpdate = mutableListOf<String>()

                synchronized(statusCache) {
                    statusCache.forEach { (userId, status) ->
                        // 检查状态是否在5秒内更新过
                        if (currentTime - status.lastUpdateTime > STATUS_EXPIRE_TIME) {
                            userIdsToUpdate.add(userId)
                        }
                    }
                }

                if (userIdsToUpdate.isNotEmpty()) {
                    Log.d(TAG, "Polling ${userIdsToUpdate.size} broadcasters: $userIdsToUpdate")

                    // 根据数量选择请求策略
                    if (userIdsToUpdate.size == 1) {
                        // 单个用户使用 V2 接口
                        requestSingleUserStatus(userIdsToUpdate.first())
                    } else {
                        // 多个用户使用 V3 批量接口
                        userIdsToUpdate.chunked(BATCH_SIZE).forEach { batch ->
                            requestStatusBatchInternal(batch)
                        }
                    }
                } else {
                    Log.d(TAG, "All broadcaster statuses are up to date")
                }

                // 等待2.5秒后进行下一次轮询
                delay(POLLING_INTERVAL)

            } catch (e: Exception) {
                Log.e(TAG, "Error in polling loop", e)
                delay(POLLING_INTERVAL) // 出错后也要等待，避免快速重试
            }
        }

        Log.d(TAG, "Status polling stopped")
    }
    
    /**
     * 内部批量请求方法
     */
    private suspend fun requestStatusBatchInternal(userIds: List<String>) {
        val requestData = mapOf("userIds" to userIds)

        broadcasterInfoRepository.getUserListOnlineStatusPostV3(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        handleStatusResponse(resource.data)
                        Log.d(TAG, "Successfully updated status for ${userIds.size} broadcasters")
                    }
                    is Resource.Error -> {
                        Log.e(TAG, "Failed to get broadcaster status: ${resource.message}")
                        // 可以在这里实现重试逻辑或错误处理
                    }
                    is Resource.Loading -> {
                        Log.d(TAG, "Loading broadcaster status...")
                    }
                }
            }
    }

    /**
     * 请求单个用户状态（使用 V2 接口）
     * @param userId 用户ID
     */
    suspend fun requestSingleUserStatus(userId: String) {
        val requestData = mapOf("userId" to userId)

        Log.d(TAG, "Requesting single user status: $userId")

        broadcasterInfoRepository.getUserOnlineStatusPostV2(requestData)
            .collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        handleSingleStatusResponse(userId, resource.data)
                        Log.d(TAG, "Successfully updated single user status: $userId -> ${resource.data}")
                    }
                    is Resource.Error -> {
                        Log.e(TAG, "Failed to get single user status for $userId: ${resource.message}")
                    }
                    is Resource.Loading -> {
                        Log.d(TAG, "Loading single user status for $userId...")
                    }
                }
            }
    }

    /**
     * 处理单个用户状态响应
     */
    private fun handleSingleStatusResponse(userId: String, status: String) {
        val currentTime = System.currentTimeMillis()
        val updatedStatuses = mutableMapOf<String, BroadcasterStatus>()

        synchronized(statusCache) {
            val existingStatus = statusCache[userId]

            // 检查状态是否真正发生变化
            val shouldUpdate = existingStatus == null ||
                             existingStatus.status != status ||
                             (currentTime - existingStatus.lastUpdateTime > STATUS_EXPIRE_TIME)

            if (shouldUpdate) {
                val broadcasterStatus = BroadcasterStatus(
                    userId = userId,
                    status = status,
                    lastUpdateTime = currentTime
                )

                // 更新缓存（移除后重新添加，保持LRU顺序）
                statusCache.remove(userId)
                statusCache[userId] = broadcasterStatus
                updatedStatuses[userId] = broadcasterStatus

                Log.d(TAG, "Updated single user status: $userId -> $status")
            } else {
                // 状态未变化，只更新时间戳
                existingStatus.let {
                    val updatedStatus = it.copy(lastUpdateTime = currentTime)
                    statusCache.remove(userId)
                    statusCache[userId] = updatedStatus
                }
                Log.d(TAG, "Status unchanged for single user: $userId -> $status")
            }
        }

        // 发送状态更新通知（只有真正变化的状态）
        if (updatedStatuses.isNotEmpty()) {
            _statusUpdates.value = updatedStatuses.toMap()
            Log.d(TAG, "Notified single user status change: $userId")
        }
    }
    
    /**
     * 处理状态响应数据
     */
    private fun handleStatusResponse(data: Map<String, Any>) {
        val updatedStatuses = mutableMapOf<String, BroadcasterStatus>()
        val currentTime = System.currentTimeMillis()

        synchronized(statusCache) {
            data.forEach { (userId, status) ->
                try {
                    val userIdStr = userId.toString()
                    val newStatus = status.toString()

                    if (userIdStr.isNotEmpty() && newStatus.isNotEmpty()) {
                        val existingStatus = statusCache[userIdStr]

                        // 检查状态是否真正发生变化
                        val shouldUpdate = existingStatus == null ||
                                         existingStatus.status != newStatus ||
                                         (currentTime - existingStatus.lastUpdateTime > STATUS_EXPIRE_TIME)

                        if (shouldUpdate) {
                            val broadcasterStatus = BroadcasterStatus(
                                userId = userIdStr,
                                status = newStatus,
                                lastUpdateTime = currentTime
                            )

                            // 更新缓存（移除后重新添加，保持LRU顺序）
                            statusCache.remove(userIdStr)
                            statusCache[userIdStr] = broadcasterStatus
                            updatedStatuses[userIdStr] = broadcasterStatus

                            Log.d(TAG, "Updated status for broadcaster $userIdStr: $newStatus")
                        } else {
                            // 状态未变化，只更新时间戳
                            existingStatus?.let {
                                val updatedStatus = it.copy(lastUpdateTime = currentTime)
                                statusCache.remove(userIdStr)
                                statusCache[userIdStr] = updatedStatus
                            }
                            Log.d(TAG, "Status unchanged for broadcaster $userIdStr: $newStatus")
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing status data for userId: $userId, status: $status", e)
                }
            }
        }

        // 发送状态更新通知（只有真正变化的状态）
        if (updatedStatuses.isNotEmpty()) {
            _statusUpdates.value = updatedStatuses.toMap()
            Log.d(TAG, "Notified ${updatedStatuses.size} status changes")
        }
    }
    
    /**
     * 停止轮询
     */
    fun stopPolling() {
        isPollingActive = false
        Log.d(TAG, "Polling stopped")
    }

    /**
     * 重新启动轮询
     */
    fun restartPolling() {
        stopPolling()
        startPollingIfNeeded()
    }

    /**
     * 清空所有缓存并停止轮询
     */
    fun clearCache() {
        synchronized(statusCache) {
            statusCache.clear()
        }
        stopPolling()
        _statusUpdates.value = emptyMap()
        Log.d(TAG, "Cache cleared and polling stopped")
    }

    /**
     * 移除指定主播（从轮询队列中移除）
     */
    fun removeBroadcaster(userId: String) {
        synchronized(statusCache) {
            statusCache.remove(userId)
        }
        Log.d(TAG, "Removed broadcaster from polling: $userId")

        // 如果缓存为空，停止轮询
        if (statusCache.isEmpty()) {
            stopPolling()
        }
    }

    /**
     * 批量移除主播
     */
    fun removeBroadcasters(userIds: List<String>) {
        synchronized(statusCache) {
            userIds.forEach { userId ->
                statusCache.remove(userId)
            }
        }
        Log.d(TAG, "Removed ${userIds.size} broadcasters from polling")

        // 如果缓存为空，停止轮询
        if (statusCache.isEmpty()) {
            stopPolling()
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    fun getCacheStats(): CacheStats {
        synchronized(statusCache) {
            return CacheStats(
                totalCount = statusCache.size,
                requestingCount = requestingIds.size,
                isPollingActive = isPollingActive,
                pollingQueueOrder = statusCache.keys.toList()
            )
        }
    }

    /**
     * 缓存统计数据类
     */
    data class CacheStats(
        val totalCount: Int,
        val requestingCount: Int,
        val isPollingActive: Boolean,
        val pollingQueueOrder: List<String>
    )
    
    /**
     * 手动更新单个主播状态（用于实时更新）
     */
    fun updateStatus(userId: String, status: String) {
        synchronized(statusCache) {
            val broadcasterStatus = BroadcasterStatus(
                userId = userId,
                status = status,
                lastUpdateTime = System.currentTimeMillis()
            )

            // 移除后重新添加，保持LRU顺序
            statusCache.remove(userId)
            statusCache[userId] = broadcasterStatus

            _statusUpdates.value = mapOf(userId to broadcasterStatus)

            Log.d(TAG, "Manually updated status for broadcaster $userId: $status")
        }
    }

    /**
     * 检查主播是否在线
     */
    fun isOnline(userId: String): Boolean {
        return getStatus(userId)?.status == "online"
    }

    /**
     * 获取所有缓存的状态（按优先级排序）
     */
    fun getAllCachedStatuses(): Map<String, BroadcasterStatus> {
        synchronized(statusCache) {
            return LinkedHashMap(statusCache)
        }
    }

    /**
     * 获取当前轮询的主播列表（按优先级排序）
     */
    fun getPollingBroadcasters(): List<String> {
        synchronized(statusCache) {
            return statusCache.keys.toList()
        }
    }

    /**
     * 检查轮询是否活跃
     */
    fun isPollingActive(): Boolean {
        return isPollingActive
    }
}
