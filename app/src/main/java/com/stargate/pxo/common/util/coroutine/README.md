# Puxxi 协程框架

这个框架提供了一套简洁、统一的协程管理方案，解决了Android开发中常见的协程使用问题，如生命周期管理、异常处理、线程调度等。

## 核心优势

- **简洁的DSL风格API**：提供直观易用的协程API
- **生命周期安全**：自动处理生命周期相关问题
- **统一异常处理**：集中处理协程异常
- **流畅的流式操作**：简化Flow相关操作
- **减少样板代码**：消除重复代码模式
- **线程安全**：合理的线程调度策略

## 快速开始

### 基础用法

```kotlin
// UI线程操作
PuxxiCoroutine.ui {
    // 在主线程执行UI操作
    binding.textView.text = "更新UI"
    
    // 切换到IO线程执行网络请求
    val data = PuxxiCoroutine.withIO {
        api.fetchData()
    }
    
    // 自动切回主线程更新UI
    binding.textView.text = data.toString()
}

// IO操作 (网络请求、文件读写等)
PuxxiCoroutine.io {
    // 在IO线程执行网络请求或文件操作
    val response = api.fetchData()
    
    // 需要在主线程更新UI时
    PuxxiCoroutine.withMain {
        binding.textView.text = response.toString()
    }
}

// 计算密集型操作
PuxxiCoroutine.compute {
    // 执行CPU密集型任务
    val result = processLargeData(data)
}

// 后台长时间任务
PuxxiCoroutine.background {
    // 执行可能长时间运行的任务
    downloadLargeFile()
}
```

### 延迟操作

```kotlin
// 简单延迟
PuxxiCoroutine.ui {
    // 显示加载中
    showLoading()
    
    // 延迟2秒
    PuxxiCoroutine.delay(2000)
    
    // 隐藏加载中
    hideLoading()
}
```

### 生命周期感知

```kotlin
// 在Activity/Fragment中使用，自动感知生命周期
PuxxiCoroutine.ui(lifecycleOwner = this) {
    // 这个协程会随Activity/Fragment生命周期自动取消
    // 默认在STARTED状态时执行
}

// 指定生命周期状态
PuxxiCoroutine.ui(
    owner = this,
    state = Lifecycle.State.RESUMED
) {
    // 只在RESUMED状态执行
}
```

## ViewModel扩展

ViewModel中使用协程的最佳实践：

```kotlin
class MyViewModel : ViewModel() {

    // 使用launchUI进行UI相关操作
    fun loadData() {
        launchUI {
            try {
                val result = repository.getData()
                _uiState.value = Success(result)
            } catch (e: Exception) {
                _uiState.value = Error(e.message)
            }
        }
    }
    
    // 使用launchIO处理IO操作
    fun processData() {
        launchIO {
            repository.processData()
        }
    }
    
    // 使用loadData模板方法简化数据加载流程
    fun fetchUsers() = loadData(
        loader = { repository.getUsers() },
        onSuccess = { users -> _users.value = users },
        onError = { error -> _error.value = error.message }
    )
    
    // 收集Flow数据
    fun observeData() {
        repository.dataFlow().collectIn(
            scope = viewModelScope,
            onStart = { _loading.value = true },
            onComplete = { _loading.value = false },
            onSuccess = { data -> _result.value = data },
            onError = { error -> _error.value = error.message }
        )
    }
}
```

## Flow扩展

简化Flow操作：

```kotlin
// 转换为UI状态
repository.getData()
    .asUiState() // 转换为 UiState<T>
    .collectIn(viewModelScope) { state ->
        when (state) {
            is UiState.Loading -> showLoading()
            is UiState.Success -> showData(state.data)
            is UiState.Error -> showError(state.error)
        }
    }

// 安全收集Flow
dataFlow.safeCollect(
    scope = lifecycleScope,
    onStart = { showLoading() },
    onComplete = { hideLoading() },
    onError = { handleError(it) },
    onSuccess = { processData(it) }
)
```

## 状态包装器

提供统一的状态管理:

```kotlin
// UiState 封装UI状态
sealed class UiState<out T> {
    object Loading : UiState<Nothing>()
    data class Success<T>(val data: T) : UiState<T>()
    data class Error(val error: Throwable) : UiState<Nothing>()
}

// LoadState 封装加载状态
sealed class LoadState<out T> {
    object Idle : LoadState<Nothing>()
    object Loading : LoadState<Nothing>()
    data class Success<T>(val data: T) : LoadState<T>()
    data class Error(val error: Throwable) : LoadState<Nothing>()
}
```

## 异常处理

内置异常处理机制：

```kotlin
// 指定异常处理器
PuxxiCoroutine.io(
    onError = { throwable ->
        // 处理异常
        Logger.e("发生错误", throwable)
    }
) {
    // 执行可能抛出异常的代码
}

// 使用try-catch处理特定异常
PuxxiCoroutine.io {
    try {
        api.riskyOperation()
    } catch (e: SpecificException) {
        // 处理特定类型的异常
    }
}
```

## 最佳实践

1. **ViewModel中的协程**：使用`launchUI`、`launchIO`等扩展函数
2. **网络请求**：使用`io()`或`withIO()`
3. **UI更新**：使用`ui()`或`withMain()`
4. **Flow处理**：使用Flow扩展函数如`collectIn()`、`asUiState()`
5. **多任务协调**：按需使用并发或顺序执行
6. **生命周期感知**：在UI组件中使用带有lifecycleOwner参数的协程启动器

## 高级用例

### 并发执行多个任务

```kotlin
PuxxiCoroutine.io {
    // 并发执行多个任务
    val results = listOf(
        async { api.fetchData1() },
        async { api.fetchData2() },
        async { api.fetchData3() }
    ).awaitAll()
    
    // 处理所有结果
    PuxxiCoroutine.withMain {
        updateUI(results)
    }
}
```

### 超时处理

```kotlin
PuxxiCoroutine.io {
    try {
        withTimeout(5000) {
            // 5秒后超时
            api.longRunningOperation()
        }
    } catch (e: TimeoutCancellationException) {
        // 处理超时
    }
}
```

### 取消操作

```kotlin
// 保存Job引用
val job = PuxxiCoroutine.io {
    // 长时间运行的任务
}

// 需要时取消
job.cancel()
```

## 性能考虑

- 使用`compute()`处理CPU密集型任务，避免阻塞主线程
- 使用`background()`处理长时间运行的后台任务
- 合理使用`withContext()`切换上下文，避免创建过多协程

## 兼容性

这个框架兼容Android API级别16及以上，使用Kotlin协程和Flow。 