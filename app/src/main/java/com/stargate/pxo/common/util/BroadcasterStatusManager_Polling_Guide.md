# BroadcasterStatusManager 轮询机制使用指南

## 🎯 新功能概述

根据你的需求，BroadcasterStatusManager 现在支持：

1. **2.5秒轮询机制** - 每2.5秒自动查询主播状态
2. **5秒状态过期检查** - 只更新5秒内未更新的状态
3. **LRU缓存策略** - 最多缓存20个主播，超出时移除最旧的
4. **智能状态更新** - 只在状态真正变化时通知UI

## 🏗️ 核心设计

### 1. **LRU缓存机制**

```kotlin
// 使用 LinkedHashMap 保持插入顺序
private val statusCache = LinkedHashMap<String, BroadcasterStatus>()

// 访问时移到最前面
fun getStatus(userId: String): BroadcasterStatus? {
    synchronized(statusCache) {
        val status = statusCache[userId]
        if (status != null) {
            statusCache.remove(userId)  // 移除
            statusCache[userId] = status // 重新添加到末尾
        }
        return status
    }
}
```

### 2. **轮询机制**

```kotlin
// 轮询配置
companion object {
    private const val MAX_CACHE_SIZE = 20        // 最大缓存20个
    private const val POLLING_INTERVAL = 2500L   // 2.5秒轮询
    private const val STATUS_EXPIRE_TIME = 5000L // 5秒过期
}

// 轮询主循环
private suspend fun startPolling() {
    while (isPollingActive && scope.isActive) {
        val currentTime = System.currentTimeMillis()
        val userIdsToUpdate = mutableListOf<String>()
        
        // 检查哪些状态需要更新（5秒内未更新的）
        statusCache.forEach { (userId, status) ->
            if (currentTime - status.lastUpdateTime > STATUS_EXPIRE_TIME) {
                userIdsToUpdate.add(userId)
            }
        }
        
        // 批量请求需要更新的状态
        if (userIdsToUpdate.isNotEmpty()) {
            requestStatusBatchInternal(userIdsToUpdate)
        }
        
        delay(POLLING_INTERVAL) // 等待2.5秒
    }
}
```

### 3. **智能状态更新**

```kotlin
private fun handleStatusResponse(data: List<Map<String, Any>>) {
    data.forEach { statusMap ->
        val userId = statusMap["userId"]?.toString()
        val newStatus = statusMap["status"]?.toString()
        val existingStatus = statusCache[userId]
        
        // 只在状态真正变化或过期时更新
        val shouldUpdate = existingStatus == null || 
                         existingStatus.status != newStatus ||
                         (currentTime - existingStatus.lastUpdateTime > STATUS_EXPIRE_TIME)
        
        if (shouldUpdate) {
            // 更新状态并通知UI
            updateStatusAndNotify(userId, newStatus)
        } else {
            // 只更新时间戳，不通知UI
            updateTimestampOnly(userId)
        }
    }
}
```

## 📋 主要方法

### 1. **添加主播到轮询队列**

```kotlin
// 添加单个主播
broadcasterStatusManager.requestStatus("user123")

// 批量添加主播
broadcasterStatusManager.requestStatusBatch(listOf("user1", "user2", "user3"))
```

**行为：**
- 主播被添加到队列最前面（最高优先级）
- 如果队列超过20个，最旧的主播被移除
- 自动启动轮询机制

### 2. **移除主播**

```kotlin
// 移除单个主播
broadcasterStatusManager.removeBroadcaster("user123")

// 批量移除主播
broadcasterStatusManager.removeBroadcasters(listOf("user1", "user2"))

// 清空所有主播并停止轮询
broadcasterStatusManager.clearCache()
```

### 3. **轮询控制**

```kotlin
// 停止轮询
broadcasterStatusManager.stopPolling()

// 重新启动轮询
broadcasterStatusManager.restartPolling()

// 检查轮询状态
val isActive = broadcasterStatusManager.isPollingActive()
```

### 4. **状态查询**

```kotlin
// 获取单个状态（会提升优先级）
val status = broadcasterStatusManager.getStatus("user123")

// 获取当前轮询的主播列表（按优先级排序）
val pollingList = broadcasterStatusManager.getPollingBroadcasters()

// 获取缓存统计
val stats = broadcasterStatusManager.getCacheStats()
```

## 🔧 在 HomeViewModel 中的使用

### 1. **页面进入时添加主播**

```kotlin
// 在 loadBroadcasterWall 成功后
private fun requestBroadcasterStatuses() {
    val broadcasterIds = currentState.broadcasters.map { it.id }
    if (broadcasterIds.isNotEmpty()) {
        // 添加到轮询队列，自动开始轮询
        broadcasterStatusManager.requestStatusBatch(broadcasterIds)
        Log.d("HomeViewModel", "Added ${broadcasterIds.size} broadcasters to polling")
    }
}
```

### 2. **页面离开时清理**

```kotlin
override fun onCleared() {
    super.onCleared()
    // 清理当前页面的主播（可选）
    val currentBroadcasterIds = currentState.broadcasters.map { it.id }
    broadcasterStatusManager.removeBroadcasters(currentBroadcasterIds)
}
```

### 3. **监听状态变化**

```kotlin
private fun observeBroadcasterStatusUpdates() {
    viewModelScope.launch {
        broadcasterStatusManager.statusUpdates.collectLatest { statusUpdates ->
            // 只有真正变化的状态才会收到通知
            if (statusUpdates.isNotEmpty()) {
                updateBroadcasterStatuses(statusUpdates)
                Log.d("HomeViewModel", "Received ${statusUpdates.size} status updates")
            }
        }
    }
}
```

## 📊 轮询队列管理

### LRU 队列示例：

```
初始状态：[]

添加 user1: [user1]
添加 user2: [user1, user2]
添加 user3: [user1, user2, user3]

访问 user1: [user2, user3, user1]  // user1 移到最前面
添加 user4: [user2, user3, user1, user4]

... 继续添加到20个 ...

添加 user21: [user3, user1, user4, ..., user21]  // user2 被移除
```

### 优先级规则：

1. **最近访问的主播** - 优先级最高
2. **最近添加的主播** - 优先级较高
3. **长时间未访问的主播** - 优先级最低，会被移除

## 🔍 调试和监控

### 1. **日志监控**

搜索 `BroadcasterStatusManager` 标签：

```
D/BroadcasterStatusManager: Added 10 broadcasters to polling queue, total: 15
D/BroadcasterStatusManager: Starting status polling...
D/BroadcasterStatusManager: Polling 3 broadcasters: [user1, user2, user3]
D/BroadcasterStatusManager: Updated status for broadcaster user1: online
D/BroadcasterStatusManager: Status unchanged for broadcaster user2: busy
D/BroadcasterStatusManager: Notified 1 status changes
```

### 2. **缓存统计**

```kotlin
val stats = broadcasterStatusManager.getCacheStats()
Log.d("Cache", """
    Total broadcasters: ${stats.totalCount}
    Polling active: ${stats.isPollingActive}
    Queue order: ${stats.pollingQueueOrder}
""")
```

### 3. **性能监控**

```kotlin
// 监控轮询频率
class PollingMonitor {
    private var lastPollingTime = 0L
    
    fun onPollingStart() {
        val currentTime = System.currentTimeMillis()
        val interval = currentTime - lastPollingTime
        Log.d("PollingMonitor", "Polling interval: ${interval}ms")
        lastPollingTime = currentTime
    }
}
```

## ⚡ 性能优化

### 1. **内存控制**

- ✅ **最大20个缓存** - 避免内存爆炸
- ✅ **LRU策略** - 自动移除不常用的主播
- ✅ **同步访问** - 线程安全的缓存操作

### 2. **网络优化**

- ✅ **批量请求** - 最多20个主播一次请求
- ✅ **智能更新** - 只更新过期的状态
- ✅ **避免重复** - 5秒内不重复请求

### 3. **UI优化**

- ✅ **只通知变化** - 状态未变化时不通知UI
- ✅ **批量更新** - 一次通知多个状态变化
- ✅ **异步处理** - 不阻塞UI线程

## ✅ 使用最佳实践

### 1. **合理的队列管理**

```kotlin
// ✅ 推荐：页面切换时管理队列
fun onPageChanged(newBroadcasters: List<String>) {
    // 移除旧的主播
    broadcasterStatusManager.removeBroadcasters(oldBroadcasters)
    
    // 添加新的主播
    broadcasterStatusManager.requestStatusBatch(newBroadcasters)
}
```

### 2. **避免频繁操作**

```kotlin
// ❌ 不推荐：频繁添加单个主播
broadcasters.forEach { broadcaster ->
    broadcasterStatusManager.requestStatus(broadcaster.id)
}

// ✅ 推荐：批量添加
val broadcasterIds = broadcasters.map { it.id }
broadcasterStatusManager.requestStatusBatch(broadcasterIds)
```

### 3. **生命周期管理**

```kotlin
// 在 Activity/Fragment 的生命周期中管理
override fun onResume() {
    super.onResume()
    // 恢复轮询
    broadcasterStatusManager.restartPolling()
}

override fun onPause() {
    super.onPause()
    // 暂停轮询（可选，节省电量）
    broadcasterStatusManager.stopPolling()
}
```

## 🚀 总结

**新的 BroadcasterStatusManager 完全满足你的需求！**

### 核心特性：
1. ✅ **2.5秒轮询** - 保证状态实时性
2. ✅ **5秒过期检查** - 避免不必要的更新
3. ✅ **20个LRU缓存** - 控制内存使用
4. ✅ **智能队列管理** - 最近使用的主播优先级最高

### 性能优势：
- 🚀 **内存可控** - 最多20个主播，避免内存爆炸
- ⚡ **网络高效** - 只更新过期状态，减少请求
- 🎯 **UI流畅** - 只在状态真正变化时更新UI
- 🔄 **自动管理** - LRU策略自动清理不常用的主播

现在你的主播状态管理系统具有了完整的轮询机制和智能缓存策略！
