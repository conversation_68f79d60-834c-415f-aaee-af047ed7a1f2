package com.stargate.pxo.common.util.coroutine

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 协程扩展函数
 * 提供便捷的协程操作方法
 */

// =================== CoroutineScope 扩展 ===================

/**
 * 在IO线程中启动协程
 */
fun CoroutineScope.launchIO(
    jobName: String? = null,
    exceptionHandler: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return CoroutineManager.getInstance().launchIO(
        scopeName = null,
        jobName = jobName,
        exceptionHandler = exceptionHandler,
        block = block
    )
}

/**
 * 在主线程中启动协程
 */
fun CoroutineScope.launchMain(
    jobName: String? = null,
    exceptionHandler: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return CoroutineManager.getInstance().launchMain(
        scopeName = null,
        jobName = jobName,
        exceptionHandler = exceptionHandler,
        block = block
    )
}

/**
 * 在CPU线程中启动协程
 */
fun CoroutineScope.launchCPU(
    jobName: String? = null,
    exceptionHandler: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return CoroutineManager.getInstance().launchCPU(
        scopeName = null,
        jobName = jobName,
        exceptionHandler = exceptionHandler,
        block = block
    )
}

/**
 * 在重型任务线程中启动协程
 */
fun CoroutineScope.launchHeavy(
    jobName: String? = null,
    exceptionHandler: ((Throwable) -> Unit)? = null,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return CoroutineManager.getInstance().launchHeavy(
        scopeName = null,
        jobName = jobName,
        exceptionHandler = exceptionHandler,
        block = block
    )
}

/**
 * 带性能监控的协程启动
 */
fun CoroutineScope.launchWithMonitoring(
    operationName: String,
    dispatcher: CoroutineDispatcher = Dispatchers.Default,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return launch(dispatcher) {
        CoroutinePerformanceMonitor.getInstance().measureCoroutine(operationName, this) {
            block()
        }
    }
}

// =================== Flow 扩展 ===================

/**
 * Flow自动切换到IO线程
 */
fun <T> Flow<T>.onIO(): Flow<T> = flowOn(Dispatchers.IO)

/**
 * Flow自动切换到主线程
 */
fun <T> Flow<T>.onMain(): Flow<T> = flowOn(Dispatchers.Main)

/**
 * Flow自动切换到CPU线程
 */
fun <T> Flow<T>.onCPU(): Flow<T> = flowOn(CoroutineManager.getInstance().cpuDispatcher)

/**
 * Flow带加载状态
 */
fun <T> Flow<T>.withLoadingState(
    onStart: () -> Unit = {},
    onComplete: () -> Unit = {},
    onError: (Throwable) -> Unit = {}
): Flow<T> {
    return this
        .onStart { onStart() }
        .catch { throwable ->
            onError(throwable)
            throw throwable
        }
        .flowOn(Dispatchers.Main)
}

/**
 * Flow安全执行（带异常处理）
 */
fun <T> Flow<T>.safeCollect(
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit = {},
    onComplete: () -> Unit = {}
): Flow<T> {
    return this.catch { throwable ->
        onError(throwable)
    }
}

// =================== 便捷方法 ===================

/**
 * 快速在IO线程执行
 */
suspend fun <T> runOnIO(block: suspend () -> T): T {
    return withContext(Dispatchers.IO) {
        block()
    }
}

/**
 * 快速在主线程执行
 */
suspend fun <T> runOnMain(block: suspend () -> T): T {
    return withContext(Dispatchers.Main) {
        block()
    }
}

/**
 * 快速在CPU线程执行
 */
suspend fun <T> runOnCPU(block: suspend () -> T): T {
    return withContext(CoroutineManager.getInstance().cpuDispatcher) {
        block()
    }
}

/**
 * 快速在重型任务线程执行
 */
suspend fun <T> runOnHeavy(block: suspend () -> T): T {
    return withContext(CoroutineManager.getInstance().heavyDispatcher) {
        block()
    }
}

/**
 * 带重试的协程执行
 */
suspend fun <T> retryCoroutine(
    times: Int = 3,
    delayMs: Long = 1000,
    block: suspend () -> T
): T {
    return CoroutineManager.getInstance().retry(times, delayMs, block)
}

/**
 * 带超时的协程执行
 */
suspend fun <T> timeoutCoroutine(
    timeoutMs: Long,
    block: suspend CoroutineScope.() -> T
): T {
    return CoroutineManager.getInstance().withTimeout(timeoutMs, block)
}