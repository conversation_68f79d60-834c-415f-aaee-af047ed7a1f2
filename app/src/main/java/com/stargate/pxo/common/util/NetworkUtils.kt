package com.stargate.pxo.common.util

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import com.stargate.pxo.common.constant.ApiConstants
import com.stargate.pxo.common.util.log.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import java.net.UnknownHostException
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLException

/**
 * 网络工具类
 * 提供网络状态检查和连接诊断功能
 */
object NetworkUtils {
    
    private const val TAG = "NetworkUtils"
    private const val CONNECTION_TIMEOUT = 5000 // 5秒
    
    /**
     * 检查网络是否可用
     * @param context 上下文
     * @return 网络是否可用
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            return networkInfo != null && networkInfo.isConnected
        }
    }
    
    /**
     * 注册网络状态回调
     * @param context 上下文
     * @param callback 网络状态回调
     */
    fun registerNetworkCallback(context: Context, callback: ConnectivityManager.NetworkCallback) {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()
        
        connectivityManager.registerNetworkCallback(networkRequest, callback)
    }
    
    /**
     * 取消注册网络状态回调
     * @param context 上下文
     * @param callback 网络状态回调
     */
    fun unregisterNetworkCallback(context: Context, callback: ConnectivityManager.NetworkCallback) {
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            connectivityManager.unregisterNetworkCallback(callback)
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error unregistering network callback: ${e.message}")
        }
    }
    
    /**
     * 检查服务器连接
     * @return 连接诊断结果
     */
    suspend fun checkServerConnection(): ConnectionDiagnosticResult = withContext(Dispatchers.IO) {
        val url = URL(ApiConstants.BASE_URL)
        
        try {
            val connection = if (url.protocol == "https") {
                url.openConnection() as HttpsURLConnection
            } else {
                url.openConnection() as HttpURLConnection
            }
            
            connection.connectTimeout = CONNECTION_TIMEOUT
            connection.readTimeout = CONNECTION_TIMEOUT
            connection.requestMethod = "HEAD"
            
            val responseCode = connection.responseCode
            connection.disconnect()
            
            when {
                responseCode in 200..299 -> ConnectionDiagnosticResult.Success
                responseCode in 400..499 -> ConnectionDiagnosticResult.ClientError(responseCode)
                responseCode in 500..599 -> ConnectionDiagnosticResult.ServerError(responseCode)
                else -> ConnectionDiagnosticResult.UnknownError("Unexpected response code: $responseCode")
            }
        } catch (e: UnknownHostException) {
            LogUtil.e(TAG, "DNS resolution failed: ${e.message}")
            ConnectionDiagnosticResult.DnsError(e.message ?: "Unknown host")
        } catch (e: SSLException) {
            LogUtil.e(TAG, "SSL error: ${e.message}")
            ConnectionDiagnosticResult.SslError(e.message ?: "SSL handshake failed")
        } catch (e: IOException) {
            LogUtil.e(TAG, "Connection error: ${e.message}")
            ConnectionDiagnosticResult.ConnectionError(e.message ?: "Connection failed")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Unknown error: ${e.message}")
            ConnectionDiagnosticResult.UnknownError(e.message ?: "Unknown error")
        }
    }
    
    /**
     * 连接诊断结果
     */
    sealed class ConnectionDiagnosticResult {
        object Success : ConnectionDiagnosticResult()
        data class ClientError(val code: Int) : ConnectionDiagnosticResult()
        data class ServerError(val code: Int) : ConnectionDiagnosticResult()
        data class DnsError(val message: String) : ConnectionDiagnosticResult()
        data class SslError(val message: String) : ConnectionDiagnosticResult()
        data class ConnectionError(val message: String) : ConnectionDiagnosticResult()
        data class UnknownError(val message: String) : ConnectionDiagnosticResult()
    }
}