package com.stargate.pxo.common.util.log

import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.LinkedBlockingQueue

class LogFileHandler(
    private val context: Context,
    private val config: LogConfig
) {
    private val logQueue = LinkedBlockingQueue<String>()
    private val writeMutex = Mutex()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val scope = CoroutineScope(Dispatchers.IO)
    
    init {
        if (config.enableFileLog) {
            startLogWriter()
            cleanOldLogs()
        }
    }
    
    fun writeLog(logContent: String) {
        if (!config.enableFileLog) return
        
        scope.launch {
            try {
                logQueue.offer(logContent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun startLogWriter() {
        scope.launch {
            while (true) {
                try {
                    val logContent = logQueue.take()
                    writeToFile(logContent)
                } catch (e: InterruptedException) {
                    break
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }
    
    private suspend fun writeToFile(logContent: String) {
        writeMutex.withLock {
            try {
                val logFile = getCurrentLogFile()
                
                // 检查文件大小，如果超过限制则创建新文件
                if (logFile.exists() && logFile.length() > config.maxFileSize) {
                    rotateLogFile()
                }
                
                FileWriter(getCurrentLogFile(), true).use { writer ->
                    writer.appendLine(logContent)
                    writer.flush()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    private fun getCurrentLogFile(): File {
        val logDir = getLogDirectory()
        val fileName = "app_${dateFormat.format(Date())}.log"
        return File(logDir, fileName)
    }
    
    private fun getLogDirectory(): File {
        val logDir = File(context.filesDir, config.logDir)
        if (!logDir.exists()) {
            logDir.mkdirs()
        }
        return logDir
    }
    
    private fun rotateLogFile() {
        try {
            val logDir = getLogDirectory()
            val currentDate = dateFormat.format(Date())
            val baseFileName = "app_$currentDate"
            
            // 找到当前最大的序号
            var maxIndex = 0
            logDir.listFiles()?.forEach { file ->
                if (file.name.startsWith(baseFileName)) {
                    val indexPart = file.name.substringAfter("${baseFileName}_", "")
                        .substringBefore(".log", "")
                    if (indexPart.isNotEmpty() && indexPart.all { it.isDigit() }) {
                        maxIndex = maxOf(maxIndex, indexPart.toInt())
                    }
                }
            }
            
            // 重命名当前文件
            val currentFile = getCurrentLogFile()
            if (currentFile.exists()) {
                val newFileName = "${baseFileName}_${maxIndex + 1}.log"
                val newFile = File(logDir, newFileName)
                currentFile.renameTo(newFile)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    private fun cleanOldLogs() {
        scope.launch {
            try {
                val logDir = getLogDirectory()
                val logFiles = logDir.listFiles { file ->
                    file.isFile && file.name.endsWith(".log")
                }?.sortedByDescending { it.lastModified() }
                
                if (logFiles != null && logFiles.size > config.maxFileCount) {
                    logFiles.drop(config.maxFileCount).forEach { file ->
                        file.delete()
                    }
                }
                
                // 删除7天前的日志
                val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
                logFiles?.filter { it.lastModified() < sevenDaysAgo }?.forEach { file ->
                    file.delete()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    fun getAllLogFiles(): List<File> {
        return try {
            val logDir = getLogDirectory()
            logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log")
            }?.sortedByDescending { it.lastModified() }?.toList() ?: emptyList()
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    fun getLogContent(file: File): String {
        return try {
            if (file.exists()) {
                file.readText()
            } else {
                ""
            }
        } catch (e: Exception) {
            ""
        }
    }
    
    fun clearAllLogs() {
        scope.launch {
            try {
                val logDir = getLogDirectory()
                logDir.listFiles()?.forEach { file ->
                    if (file.isFile && file.name.endsWith(".log")) {
                        file.delete()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
    
    fun getLogStats(): LogStats {
        return try {
            val logDir = getLogDirectory()
            val logFiles = logDir.listFiles { file ->
                file.isFile && file.name.endsWith(".log")
            } ?: emptyArray()
            
            val totalSize = logFiles.sumOf { it.length() }
            val fileCount = logFiles.size
            val oldestFile = logFiles.minByOrNull { it.lastModified() }
            val newestFile = logFiles.maxByOrNull { it.lastModified() }
            
            LogStats(
                fileCount = fileCount,
                totalSize = totalSize,
                oldestFileTime = oldestFile?.lastModified() ?: 0,
                newestFileTime = newestFile?.lastModified() ?: 0
            )
        } catch (e: Exception) {
            LogStats()
        }
    }
}

data class LogStats(
    val fileCount: Int = 0,
    val totalSize: Long = 0,
    val oldestFileTime: Long = 0,
    val newestFileTime: Long = 0
) {
    fun getTotalSizeFormatted(): String {
        val kb = 1024.0
        val mb = kb * 1024
        val gb = mb * 1024
        
        return when {
            totalSize >= gb -> String.format("%.2f GB", totalSize / gb)
            totalSize >= mb -> String.format("%.2f MB", totalSize / mb)
            totalSize >= kb -> String.format("%.2f KB", totalSize / kb)
            else -> "$totalSize B"
        }
    }
}