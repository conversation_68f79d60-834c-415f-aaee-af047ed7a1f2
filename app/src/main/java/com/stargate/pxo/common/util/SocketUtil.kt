package com.stargate.pxo.common.util.socket

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.common.util.coroutine.PuxxiCoroutine
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.net.URISyntaxException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * Socket.IO连接工具类
 * 提供Socket连接管理、重连机制、网络监听等功能
 */
object SocketUtil {
    
    private const val TAG = "SocketUtil"
    
    // Socket实例
    private var socket: Socket? = null
    
    // 上下文引用
    private var context: WeakReference<Context>? = null
    
    // 连接配置
    private var serverUrl: String = ""
    private var options: IO.Options? = null
    
    // 连接状态
    private val isConnected = AtomicBoolean(false)
    private val isConnecting = AtomicBoolean(false)
    private val shouldReconnect = AtomicBoolean(true)
    
    // 重连机制
    private val reconnectAttempts = AtomicInteger(0)
    private val maxReconnectAttempts = 5
    private val reconnectDelay = 3000L // 3秒
    private val maxReconnectDelay = 30000L // 30秒
    
    // 事件监听器
    private val eventListeners = ConcurrentHashMap<String, MutableList<Emitter.Listener>>()
    
    // 网络监听
    private var networkReceiver: NetworkReceiver? = null
    private var isNetworkReceiverRegistered = false
    
    // 连接状态回调
    private val connectionListeners = mutableListOf<SocketConnectionListener>()
    
    /**
     * Socket连接状态监听器
     */
    interface SocketConnectionListener {
        fun onConnected()
        fun onDisconnected()
        fun onConnecting()
        fun onReconnecting(attempt: Int)
        fun onReconnectFailed()
        fun onError(error: String)
    }
    
    /**
     * 网络状态监听器
     */
    private class NetworkReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (ConnectivityManager.CONNECTIVITY_ACTION == intent?.action) {
                if (isNetworkAvailable()) {
                    LogUtil.d(TAG, "Network available, attempting to reconnect...")
                    if (!isConnected.get() && shouldReconnect.get()) {
                        connect()
                    }
                } else {
                    LogUtil.d(TAG, "Network unavailable")
                }
            }
        }
    }
    
    /**
     * 初始化SocketUtil
     */
    fun init(context: Context) {
        this.context = WeakReference(context.applicationContext)
        LogUtil.d(TAG, "SocketUtil initialized")
    }
    
    /**
     * 配置Socket连接参数
     */
    fun configure(
        url: String,
        options: IO.Options? = null
    ) {
        this.serverUrl = url
        this.options = options ?: createDefaultOptions()
        LogUtil.d(TAG, "Socket configured with URL: $url")
    }
    
    /**
     * 创建默认连接选项
     */
    private fun createDefaultOptions(): IO.Options {
        return IO.Options().apply {
            // 连接超时
            timeout = 10000
            // 强制使用WebSocket
            forceNew = true
            // 重连配置
            reconnection = false // 我们自己实现重连机制
            // 传输方式
            transports = arrayOf("websocket", "polling")
        }
    }
    
    /**
     * 连接Socket
     */
    fun connect() {
        if (serverUrl.isEmpty()) {
            LogUtil.e(TAG, "Server URL not configured")
            notifyError("Server URL not configured")
            return
        }
        
        if (isConnecting.get()) {
            LogUtil.d(TAG, "Already connecting...")
            return
        }
        
        if (isConnected.get()) {
            LogUtil.d(TAG, "Already connected")
            return
        }
        
        try {
            isConnecting.set(true)
            notifyConnecting()
            
            // 创建Socket实例
            socket = IO.socket(serverUrl, options)
            
            // 设置事件监听
            setupSocketListeners()
            
            // 注册网络监听
            registerNetworkReceiver()
            
            // 连接
            socket?.connect()
            
            LogUtil.d(TAG, "Attempting to connect to: $serverUrl")
            
        } catch (e: URISyntaxException) {
            LogUtil.e(TAG, "Invalid URL: $serverUrl", e)
            isConnecting.set(false)
            notifyError("Invalid server URL")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Failed to connect", e)
            isConnecting.set(false)
            notifyError("Connection failed: ${e.message}")
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        shouldReconnect.set(false)
        
        socket?.let { s ->
            if (s.connected()) {
                s.disconnect()
                LogUtil.d(TAG, "Socket disconnected")
            }
        }
        
        cleanup()
    }
    
    /**
     * 重新连接
     */
    fun reconnect() {
        LogUtil.d(TAG, "Manual reconnect requested")
        reconnectAttempts.set(0)
        shouldReconnect.set(true)
        
        disconnect()
        
        PuxxiCoroutine.io {
            PuxxiCoroutine.delay(1000) // 延迟1秒后重连
            connect()
        }
    }
    
    /**
     * 设置Socket事件监听
     */
    private fun setupSocketListeners() {
        socket?.apply {
            
            // 连接成功
            on(Socket.EVENT_CONNECT) {
                LogUtil.d(TAG, "Socket connected successfully")
                isConnected.set(true)
                isConnecting.set(false)
                reconnectAttempts.set(0)
                notifyConnected()
            }
            
            // 连接断开
            on(Socket.EVENT_DISCONNECT) { args ->
                LogUtil.d(TAG, "Socket disconnected: ${args.joinToString()}")
                isConnected.set(false)
                isConnecting.set(false)
                notifyDisconnected()
                
                // 自动重连
                if (shouldReconnect.get()) {
                    startReconnect()
                }
            }
            
            // 连接错误
            on(Socket.EVENT_CONNECT_ERROR) { args ->
                val error = args.firstOrNull()?.toString() ?: "Unknown error"
                LogUtil.e(TAG, "Socket connection error: $error")
                isConnected.set(false)
                isConnecting.set(false)
                notifyError(error)
                
                // 重连
                if (shouldReconnect.get()) {
                    startReconnect()
                }
            }

        }
    }
    
    /**
     * 开始重连
     */
    private fun startReconnect() {
        val currentAttempt = reconnectAttempts.incrementAndGet()
        
        if (currentAttempt > maxReconnectAttempts) {
            LogUtil.e(TAG, "Max reconnect attempts reached")
            shouldReconnect.set(false)
            notifyReconnectFailed()
            return
        }
        
        // 计算延迟时间（指数退避）
        val delay = minOf(reconnectDelay * currentAttempt, maxReconnectDelay)
        
        LogUtil.d(TAG, "Reconnecting in ${delay}ms (attempt $currentAttempt/$maxReconnectAttempts)")
        notifyReconnecting(currentAttempt)
        
        PuxxiCoroutine.io {
            PuxxiCoroutine.delay(delay)
            
            if (shouldReconnect.get() && !isConnected.get()) {
                LogUtil.d(TAG, "Attempting reconnect #$currentAttempt")
                connect()
            }
        }
    }
    
    /**
     * 发送消息
     */
    fun emit(event: String, vararg args: Any) {
        socket?.let { s ->
            if (s.connected()) {
                s.emit(event, *args)
                LogUtil.d(TAG, "Emitted event: $event")
            } else {
                LogUtil.w(TAG, "Cannot emit event '$event': socket not connected")
            }
        } ?: run {
            LogUtil.w(TAG, "Cannot emit event '$event': socket is null")
        }
    }
    
    /**
     * 发送JSON消息
     */
    fun emit(event: String, data: JSONObject) {
        emit(event, data as Any)
    }
    
    /**
     * 监听事件
     */
    fun on(event: String, listener: Emitter.Listener) {
        // 保存监听器引用
        eventListeners.getOrPut(event) { mutableListOf() }.add(listener)
        
        // 添加到Socket
        socket?.on(event, listener)
        
        LogUtil.d(TAG, "Added listener for event: $event")
    }
    
    /**
     * 移除事件监听
     */
    fun off(event: String, listener: Emitter.Listener? = null) {
        if (listener != null) {
            eventListeners[event]?.remove(listener)
            socket?.off(event, listener)
        } else {
            eventListeners.remove(event)
            socket?.off(event)
        }
        
        LogUtil.d(TAG, "Removed listener for event: $event")
    }
    
    /**
     * 添加连接状态监听器
     */
    fun addConnectionListener(listener: SocketConnectionListener) {
        if (!connectionListeners.contains(listener)) {
            connectionListeners.add(listener)
        }
    }
    
    /**
     * 移除连接状态监听器
     */
    fun removeConnectionListener(listener: SocketConnectionListener) {
        connectionListeners.remove(listener)
    }
    
    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isConnected.get()
    
    /**
     * 获取连接中状态
     */
    fun isConnecting(): Boolean = isConnecting.get()
    
    /**
     * 获取Socket ID
     */
    fun getSocketId(): String? = socket?.id()
    
    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        val context = this.context?.get() ?: return false
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }
    
    /**
     * 注册网络状态监听
     */
    private fun registerNetworkReceiver() {
        val context = this.context?.get() ?: return
        
        if (!isNetworkReceiverRegistered) {
            networkReceiver = NetworkReceiver()
            val filter = IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION)
            context.registerReceiver(networkReceiver, filter)
            isNetworkReceiverRegistered = true
            LogUtil.d(TAG, "Network receiver registered")
        }
    }
    
    /**
     * 注销网络状态监听
     */
    private fun unregisterNetworkReceiver() {
        val context = this.context?.get()
        
        if (isNetworkReceiverRegistered && networkReceiver != null && context != null) {
            try {
                context.unregisterReceiver(networkReceiver)
                LogUtil.d(TAG, "Network receiver unregistered")
            } catch (e: Exception) {
                LogUtil.w(TAG, "Failed to unregister network receiver$e")
            } finally {
                networkReceiver = null
                isNetworkReceiverRegistered = false
            }
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        isConnected.set(false)
        isConnecting.set(false)
        
        // 清理事件监听器
        eventListeners.clear()
        
        // 关闭Socket
        socket?.let { s ->
            s.off()
            s.close()
        }
        socket = null
        
        // 注销网络监听
        unregisterNetworkReceiver()
        
        LogUtil.d(TAG, "Resources cleaned up")
    }
    
    /**
     * 销毁SocketUtil
     */
    fun destroy() {
        shouldReconnect.set(false)
        disconnect()
        connectionListeners.clear()
        context?.clear()
        context = null
        LogUtil.d(TAG, "SocketUtil destroyed")
    }
    
    // =================== 连接状态通知方法 ===================
    
    private fun notifyConnected() {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onConnected() }
        }
    }
    
    private fun notifyDisconnected() {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onDisconnected() }
        }
    }
    
    private fun notifyConnecting() {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onConnecting() }
        }
    }
    
    private fun notifyReconnecting(attempt: Int) {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onReconnecting(attempt) }
        }
    }
    
    private fun notifyReconnectFailed() {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onReconnectFailed() }
        }
    }
    
    private fun notifyError(error: String) {
        PuxxiCoroutine.ui {
            connectionListeners.forEach { it.onError(error) }
        }
    }
    
    // =================== 调试信息 ===================
    
    /**
     * 获取连接信息
     */
    fun getConnectionInfo(): String {
        return buildString {
            appendLine("Socket Connection Info:")
            appendLine("- URL: $serverUrl")
            appendLine("- Connected: ${isConnected.get()}")
            appendLine("- Connecting: ${isConnecting.get()}")
            appendLine("- Should Reconnect: ${shouldReconnect.get()}")
            appendLine("- Reconnect Attempts: ${reconnectAttempts.get()}/$maxReconnectAttempts")
            appendLine("- Socket ID: ${getSocketId() ?: "N/A"}")
            appendLine("- Network Available: ${isNetworkAvailable()}")
            appendLine("- Event Listeners: ${eventListeners.size}")
            appendLine("- Connection Listeners: ${connectionListeners.size}")
        }
    }
}