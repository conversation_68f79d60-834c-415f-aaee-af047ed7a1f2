package com.stargate.pxo

import android.app.Application
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import com.stargate.pxo.common.util.NetworkUtils
import com.stargate.pxo.common.util.SPUtil
import com.stargate.pxo.common.util.ToastUtil
import com.stargate.pxo.common.util.coroutine.CoroutineExceptionMonitor
import com.stargate.pxo.common.util.coroutine.CoroutineManager
import com.stargate.pxo.common.util.coroutine.CoroutineScopeManager
import com.stargate.pxo.common.util.log.LogConfig
import com.stargate.pxo.common.util.log.LogLevel
import com.stargate.pxo.common.util.log.LogUtil
import com.stargate.pxo.data.network.PuxxiUrl
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class PuxxiApplication : Application() {

    // 网络状态回调
    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            LogUtil.i("NetworkCallback", "Network available")
        }
        
        override fun onLost(network: Network) {
            LogUtil.w("NetworkCallback", "Network lost")
        }
        
        override fun onCapabilitiesChanged(
            network: Network,
            networkCapabilities: NetworkCapabilities
        ) {
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            LogUtil.d("NetworkCallback", "Network capabilities changed: internet=$hasInternet, validated=$hasValidated")
        }
    }

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志系统
        initializeLogger()
        
        // 初始化Toast工具
        initializeToast()
        
        // 初始化网络状态监听
        initializeNetworkMonitoring()
        
        // 初始化URL模式
        initializeUrlMode()
        
        // 初始化应用配置
        initializeAppConfig()

    }
    
    private fun initializeLogger() {
        val logConfig = if (BuildConfig.DEBUG) {
            LogConfig.debugConfig().copy(
                globalTag = "Puxxi",
                showThreadInfo = true,
                showMethodInfo = true,
                enableBorder = true,
                enableJsonFormat = true,
                enableFileLog = true,
                enableAsync = true
            )
        } else {
            LogConfig.releaseConfig().copy(
                globalTag = "Puxxi",
                minLogLevel = LogLevel.WARN,
                showThreadInfo = false,
                showMethodInfo = false,
                enableBorder = false,
                enableJsonFormat = false,
                enableFileLog = true,
                enableAsync = true
            )
        }
    }
    
    private fun initializeToast() {
        // ToastUtil会自动根据设备选择最佳配置
        ToastUtil.init(this)
        LogUtil.i("Application", "ToastUtil initialized successfully")
        
        // 初始化协程管理器
        initializeCoroutineManager()

        // 初始化SP工具，包含普通存储和加密存储功能
        SPUtil.init(this)

        // 显示初始化完成提示（仅在Debug模式）
        if (BuildConfig.DEBUG) {
            ToastUtil.showSuccess("Application initialized")
        }
    }
    
    /**
     * 初始化网络状态监听
     */
    private fun initializeNetworkMonitoring() {
        // 注册网络状态回调
        NetworkUtils.registerNetworkCallback(this, networkCallback)
        LogUtil.i("Application", "Network monitoring initialized successfully")
    }
    
    private fun initializeCoroutineManager() {
        // 协程管理器会自动初始化，这里可以进行一些配置
        val coroutineManager = CoroutineManager.getInstance()
        val scopeManager = CoroutineScopeManager.getInstance()
        val exceptionMonitor = CoroutineExceptionMonitor.getInstance()
        
        // 注册全局异常监听器
        exceptionMonitor.addGlobalExceptionListener { exceptionInfo ->
            LogUtil.e("Application", "Global coroutine exception: ${exceptionInfo.exceptionType}", )
            
            // 在特定异常时显示用户友好的提示
            when (exceptionInfo.exceptionType) {
                "NetworkException", "ConnectException", "SocketTimeoutException" -> {
                    ToastUtil.showError("网络连接异常，请检查网络设置")
                }
                "OutOfMemoryError" -> {
                    ToastUtil.showError("内存不足，请关闭一些应用后重试")
                }
            }
        }
        
        LogUtil.i("Application", "CoroutineManager initialized successfully")
    }
    

    /**
     * 初始化URL模式
     */
    private fun initializeUrlMode() {
        // 根据构建类型设置URL模式
        // Debug模式使用真实URL，Release模式使用混淆URL
        PuxxiUrl.setUrlMode(!BuildConfig.DEBUG)
        
        LogUtil.i("Application", "URL mode initialized: ${if (PuxxiUrl.isUsingObfuscatedUrl()) "obfuscated" else "real"}")
    }
    
    /**
     * 初始化应用配置
     */
    private fun initializeAppConfig() {
        try {
            LogUtil.i("Application", "App configuration initialization started")
        } catch (e: Exception) {
            LogUtil.e("Application", "Failed to initialize app configuration", e)
        }
    }

    override fun onTerminate() {
        super.onTerminate()
        
        // 取消注册网络状态回调
        NetworkUtils.unregisterNetworkCallback(this, networkCallback)
        
        // 清理协程资源
        CoroutineManager.getInstance().shutdown()
        CoroutineScopeManager.getInstance().destroyAll()
        
        // 清理Toast资源
        ToastUtil.destroy()
        LogUtil.i("Application", "Application terminated, resources cleaned up")
    }
}