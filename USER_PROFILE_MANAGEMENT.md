# 用户资料管理模块文档

## 模块概述
用户资料管理模块提供完整的用户个人信息管理功能，包括头像上传、个人信息编辑、资料展示等核心功能。

## 模块位置
```
presentation/ui/screens/profile/     # 个人资料UI组件
├── ProfileFormScreen.kt            # 资料编辑页面
├── ProfileViewModel.kt             # 资料管理业务逻辑
└── AvatarUpload_README.md          # 头像上传文档

data/network/model/                 # 数据模型
├── UpdateAvatarResult.kt           # 头像更新响应
├── OssPolicyResult.kt              # OSS上传策略
└── XFileUploadResult.kt            # 文件上传结果

common/util/                        # 工具类
├── ImagePickerUtil.kt              # 图片选择工具
├── FileUploadDownloadUtil.kt       # 文件上传下载
└── PermissionUtil.kt               # 权限管理工具
```

## 核心功能

### 1. 头像管理
- **头像选择**: 支持相机拍摄和相册选择
- **图片裁剪**: 自动裁剪为圆形头像格式
- **上传处理**: 异步上传到OSS存储服务
- **缓存策略**: 本地缓存优化加载性能

### 2. 个人信息编辑  
- **基本信息**: 昵称、性别、生日等基础资料
- **联系方式**: 邮箱、手机号码等联系信息
- **个性化设置**: 个人签名、兴趣标签等
- **隐私控制**: 信息可见性和隐私设置

### 3. 权限管理
- **相机权限**: 智能请求和处理相机访问权限
- **存储权限**: 文件读写权限的动态申请
- **权限引导**: 用户友好的权限说明和引导
- **降级方案**: 权限被拒绝时的功能降级处理

## 技术实现

### ProfileFormScreen.kt
```kotlin
// UI功能实现
- Compose表单界面
- 头像选择和预览
- 表单验证和提交
- 加载状态处理
- 错误信息展示
```

### ProfileViewModel.kt
```kotlin
// 业务逻辑管理
- 个人信息状态管理
- 头像上传流程控制
- 表单数据验证
- 网络请求协调
- UI状态更新
```

### ImagePickerUtil.kt
```kotlin
// 图片选择工具
- 相机和相册选择
- 图片压缩和优化
- 权限检查和申请
- 结果回调处理
```

## 头像上传流程

### 完整上传流程
1. **权限检查** → 验证相机/存储权限
2. **图片选择** → 相机拍摄或相册选择
3. **图片处理** → 压缩、裁剪、格式转换
4. **获取上传策略** → 向服务器请求OSS上传凭证
5. **文件上传** → 使用OSS SDK上传到云存储
6. **更新头像** → 将新头像URL提交给服务器
7. **本地更新** → 更新本地用户信息和UI

### 上传策略
```kotlin
// OSS上传策略请求
POST /api/user/oss-policy
Response: {
    "accessKeyId": "临时访问密钥ID",
    "accessKeySecret": "临时访问密钥",
    "securityToken": "安全令牌",
    "bucketName": "存储桶名称",
    "endpoint": "服务端点",
    "expiration": "过期时间"
}
```

## 文件处理特性

### 图片优化
- **自动压缩**: 根据屏幕密度智能压缩
- **格式转换**: 统一转换为WebP格式
- **尺寸限制**: 头像限制为512x512像素
- **质量控制**: 压缩质量85%保证清晰度

### 上传优化
- **断点续传**: 支持大文件断点续传
- **进度显示**: 实时显示上传进度
- **重试机制**: 网络异常自动重试3次
- **并发控制**: 限制同时上传文件数量

## 数据存储

### 本地存储
```kotlin
// 用户信息本地缓存
- Room数据库存储详细信息
- SharedPreferences存储基本配置
- 文件缓存存储头像图片
```

### 云端存储
```kotlin
// 阿里云OSS存储
- 头像文件云端存储
- CDN加速全球访问
- 多重备份保证可靠性
```

## 安全与隐私

### 隐私保护
- 🔒 **权限最小化**: 仅申请必要的系统权限
- 🛡️ **数据加密**: 敏感信息本地加密存储
- 🚫 **访问控制**: 用户可控制信息可见性
- 📱 **本地处理**: 图片处理在本地完成

### 安全特性
- ✅ **文件类型检查**: 严格验证上传文件类型
- 🔍 **内容安全**: 图片内容安全检测
- 🔐 **传输加密**: HTTPS/TLS加密传输
- ⏰ **临时凭证**: OSS上传使用临时凭证

## API接口

### 更新用户信息
```kotlin
PUT /api/user/profile
Request: {
    "nickname": "用户昵称",
    "gender": "性别",
    "birthday": "生日",
    "signature": "个性签名",
    "avatar": "头像URL"
}
```

### 头像上传确认
```kotlin
POST /api/user/avatar
Request: {
    "avatarUrl": "上传完成的头像URL",
    "fileSize": "文件大小",
    "fileType": "文件类型"
}
```

## 配置选项
- **图片质量**: 可配置压缩质量 (默认85%)
- **缓存时间**: 头像缓存有效期 (默认7天)
- **上传超时**: 文件上传超时时间 (默认60秒)
- **重试次数**: 失败重试次数 (默认3次)