# 用户认证模块文档

## 模块概述
用户认证模块负责处理用户的登录、注册、token管理等核心认证功能，是应用的安全基础。

## 模块位置
```
presentation/ui/screens/login/    # 登录UI组件
├── LoginScreen.kt               # 登录页面UI
└── LoginViewModel.kt            # 登录业务逻辑

data/manager/
└── UserManager.kt               # 用户数据管理器

data/network/model/
├── LoginResponse.kt             # 登录响应模型
└── UserInfo.kt                  # 用户信息模型

data/repository/
└── UserInfoRepository.kt        # 用户信息仓库
```

## 核心功能

### 1. 用户登录
- **登录UI**: 现代化Compose界面，支持账号密码登录
- **表单验证**: 实时输入验证和错误提示
- **登录状态**: 支持记住登录状态，自动重新登录
- **安全性**: Token安全存储，支持加密保存

### 2. 用户状态管理
- **登录状态**: 全局登录状态管理和监听
- **用户信息**: 用户基本信息的本地缓存和同步
- **Token管理**: 自动Token刷新和过期处理
- **登出功能**: 安全登出，清理本地数据

### 3. 数据存储
- **SharedPreferences**: 用户基本设置和登录状态
- **加密存储**: 敏感信息如Token使用AES加密
- **Room数据库**: 用户详细信息本地持久化

## 技术实现

### LoginScreen.kt
```kotlin
// 主要功能
- Compose UI界面实现
- 表单状态管理
- 用户交互处理
- 导航跳转控制
```

### LoginViewModel.kt  
```kotlin
// 核心职责
- 登录业务逻辑处理
- 网络请求管理
- 状态管理和UI更新
- 错误处理和提示
```

### UserManager.kt
```kotlin
// 管理功能
- 用户信息全局管理
- 登录状态维护
- Token生命周期管理
- 数据同步协调
```

## 数据流程

### 登录流程
1. **用户输入** → 表单验证 → 发送登录请求
2. **服务器验证** → 返回Token和用户信息
3. **本地存储** → 保存Token(加密) + 用户信息
4. **状态更新** → 更新全局登录状态
5. **页面跳转** → 导航到主页面

### 自动登录流程
1. **应用启动** → 检查本地Token
2. **Token验证** → 验证Token有效性
3. **用户信息** → 加载本地用户信息
4. **状态恢复** → 恢复登录状态
5. **页面导航** → 根据状态决定起始页面

## 安全特性
- 🔐 **Token加密存储**: 使用AES加密算法保护敏感信息
- 🛡️ **自动过期处理**: Token过期自动清理和重新登录
- 🔒 **安全传输**: HTTPS协议保证数据传输安全
- 🚫 **防重放攻击**: 时间戳和随机数防止重放
- 📱 **设备绑定**: 支持设备ID绑定增强安全性

## API接口

### 登录接口
```kotlin
POST /api/auth/login
Request: {
    "username": "用户名",
    "password": "密码",
    "deviceId": "设备ID"
}
Response: {
    "token": "访问令牌",
    "refreshToken": "刷新令牌", 
    "userInfo": {
        "userId": "用户ID",
        "username": "用户名",
        "avatar": "头像URL",
        "email": "邮箱"
    }
}
```

## 配置说明
- **Debug模式**: 支持测试账号快速登录
- **Release模式**: 完整的安全验证流程
- **网络配置**: 支持开发和生产环境切换
- **缓存策略**: 用户信息本地缓存7天